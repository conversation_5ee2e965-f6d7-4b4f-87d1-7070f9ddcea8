#ifndef __LINUX_WAIT_H__
#define __LINUX_WAIT_H__

#include <errno.h>
#include <list.h>
#include <ttos.h>
#include <ttosBase.h>

typedef T_TTOS_Task_Queue_Control wait_queue_head_t;

#define init_waitqueue_head(q)                                                                     \
    ttosInitializeTaskq((q), T_TTOS_QUEUE_DISCIPLINE_FIFO, TTOS_TASK_WAITING_FOR_EVENT)

#define wait_event_interruptible(wq, condition)                                                    \
    ({                                                                                             \
        int __ret__ = 0;                                                                           \
        for (;;)                                                                                   \
        {                                                                                          \
            if (condition)                                                                         \
                break;                                                                             \
            ttosDisableTaskDispatchWithLock();                                                     \
            ttosGetRunningTask()->wait.returnCode = 0;                                             \
                                                                                                   \
            ttosEnqueueTaskq((wq), TTOS_WAIT_FOREVER, TRUE);                                       \
            ttosEnableTaskDispatchWithLock();                                                      \
            __ret__ = ttosGetRunningTask()->wait.returnCode;                                       \
                                                                                                   \
            if (__ret__ == TTOS_SIGNAL_INTR)                                                       \
            {                                                                                      \
                __ret__ = -ERESTARTSYS;                                                            \
                break;                                                                             \
            }                                                                                      \
        }                                                                                          \
                                                                                                   \
        __ret__;                                                                                   \
    })

static inline void wake_up_interruptible(wait_queue_head_t *wq)
{
    T_TTOS_ChainNode *node = NULL, *next_node = NULL;
    T_TTOS_TaskControlBlock *task;

    /* @KEEP_COMMENT: 禁止调度器 */
    ttosDisableTaskDispatchWithLock();

    node = ttosGetChainFirstNode(&(wq->queues.fifoQueue));

    while (node)
    {
        next_node = ttosGetNextNode(&(wq->queues.fifoQueue), node);
        task = (T_TTOS_TaskControlBlock *)(((T_TTOS_ResourceTaskNode *)node)->task);
        ttosExtractChainNode(node);

        if ((task != NULL))
        {
            /* @REPLACE_BRACKET: node任务在任务tick等待队列上 */
            if (task->objCore.objectNode.next != NULL)
            {
                /* @KEEP_COMMENT:
                 * 调用ttosExactWaitedTask(DT.2.24)将node任务从任务tick等待队列中移除
                 */
                (void)ttosExactWaitedTask(task);
            }

            (void)ttosClearTaskWaiting(task);
            task->wait.queue = NULL;
            task->wait.returnCode = TTOS_OK;
        }
        node = next_node;
    }

    /* @KEEP_COMMENT: 重新使能调度 */
    ttosEnableTaskDispatchWithLock();
}

#endif
