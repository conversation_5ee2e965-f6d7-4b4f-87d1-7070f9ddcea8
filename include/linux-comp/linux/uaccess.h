#ifndef __LINUX_UACCESS_H__
#define __LINUX_UACCESS_H__

#include <uaccess.h>

#define put_user(x, ptr)                                                                           \
    ({                                                                                             \
        typeof(x) _x = x;                                                                          \
        copy_to_user(ptr, &(_x), sizeof(x));                                                       \
    })

#define get_user(x, ptr) copy_from_user(&(x), ptr, sizeof(x))

#endif /* __LINUX_UACCESS_H__ */
