#ifndef __ARCH_X86_64_INCLUDE_ACPI_H
#define __ARCH_X86_64_INCLUDE_ACPI_H

#include <stdint.h>
#include <system/types.h>
/****************************************************************************
 * Included Files
 ****************************************************************************/

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

#define ACPI_EBDA_PTR_LOCATION 0x0000040E /* Physical Address */
#define ACPI_EBDA_PTR_LENGTH 2
#define ACPI_EBDA_WINDOW_SIZE 1024
#define ACPI_HI_RSDP_WINDOW_BASE 0x000E0000 /* Physical Address */
#define ACPI_HI_RSDP_WINDOW_SIZE 0x00020000
#define ACPI_RSDP_SCAN_STEP 16

/* RSDP checksums */

#define ACPI_RSDP_CHECKSUM_LENGTH 20
#define ACPI_RSDP_XCHECKSUM_LENGTH 36

#define ACPI_SIG_RSDP "RSD PTR " /* Root System Description Pointer */

/* Tables defined by ACPI spec */

#define ACPI_SIG_APIC "APIC" /* Multiple APIC Description Table (MADT) */
#define ACPI_SIG_BERT "BERT"
#define ACPI_SIG_BGRT "BGRT"
#define ACPI_SIG_CCEL "CCEL"
#define ACPI_SIG_CPEP "CPEP"
#define ACPI_SIG_DSDT "DSDT"
#define ACPI_SIG_ECDT "ECDT"
#define ACPI_SIG_EINJ "EINJ"
#define ACPI_SIG_ERST "ERST"
#define ACPI_SIG_FACP "FACP"
#define ACPI_SIG_FACS "FACS"
#define ACPI_SIG_FPDT "FPDT"
#define ACPI_SIG_GTDT "GTDT"
#define ACPI_SIG_HEST "HEST"
#define ACPI_SIG_MISC "MISC"
#define ACPI_SIG_MSCT "MSCT"
#define ACPI_SIG_MPST "MPST"
#define ACPI_SIG_NFIT "NFIT"
#define ACPI_SIG_OEMx "OEMx"
#define ACPI_SIG_PCCT "PCCT"
#define ACPI_SIG_PHAT "PHAT"
#define ACPI_SIG_PMTT "PMTT"
#define ACPI_SIG_PPTT "PPTT"
#define ACPI_SIG_PSDT "PSDT"
#define ACPI_SIG_RASF "RASF"
#define ACPI_SIG_RAS2 "RAS2"
#define ACPI_SIG_RSDT "RSDT"
#define ACPI_SIG_SBST "SBST"
#define ACPI_SIG_SDEV "SDEV"
#define ACPI_SIG_SLIT "SLIT"
#define ACPI_SIG_SRAT "SRAT"
#define ACPI_SIG_SSDT "SSDT"
#define ACPI_SIG_SVKL "SVKL"
#define ACPI_SIG_XSDT "XSDT"

/* Tables not defined by ACPI spec */

#define ACPI_SIG_AEST "AEST"
#define ACPI_SIG_AGDI "AGDI"
#define ACPI_SIG_APMT "APMT"
#define ACPI_SIG_BDAT "BDAT"
#define ACPI_SIG_BOOT "BOOT"
#define ACPI_SIG_CEDT "CEDT"
#define ACPI_SIG_CSRT "CSRT"
#define ACPI_SIG_DBGT "DBGT"
#define ACPI_SIG_DBG2 "DBG2"
#define ACPI_SIG_DMAR "DMAR"
#define ACPI_SIG_DRTM "DRTM"
#define ACPI_SIG_DTPR "DTPR"
#define ACPI_SIG_ETDT "ETDT"
#define ACPI_SIG_HPET "HPET"
#define ACPI_SIG_IBFT "IBFT"
#define ACPI_SIG_IERS "IERS"
#define ACPI_SIG_IORT "IORT"
#define ACPI_SIG_IVRS "IVRS"
#define ACPI_SIG_KEYP "KEYP"
#define ACPI_SIG_LPIT "LPIT"
#define ACPI_SIG_MCFG "MCFG" /* PCI Express Memory-mapped Configuration table */
#define ACPI_SIG_MCHI "MCHI"
#define ACPI_SIG_MHSP "MHSP"
#define ACPI_SIG_MPAM "MPAM"
#define ACPI_SIG_MSDM "MSDM"
#define ACPI_SIG_NBFT "NBFT"
#define ACPI_SIG_PRMT "PRMT"
#define ACPI_SIG_RGRT "RGRT"
#define ACPI_SIG_SDEI "SDEI"
#define ACPI_SIG_SLIC "SLIC"
#define ACPI_SIG_SPCR "SPCR"
#define ACPI_SIG_SPMI "SPMI"
#define ACPI_SIG_STAO "STAO"
#define ACPI_SIG_SWFT "SWFT"
#define ACPI_SIG_TCPA "TCPA"
#define ACPI_SIG_TPM2 "TPM2"
#define ACPI_SIG_UEFI "UEFI"
#define ACPI_SIG_WAET "WAET"
#define ACPI_SIG_WDAT "WDAT"
#define ACPI_SIG_WDDT "WDDT"
#define ACPI_SIG_WDRT "WDRT"
#define ACPI_SIG_WPBT "WPBT"
#define ACPI_SIG_WSMT "WSMT"
#define ACPI_SIG_XENV "XENV"

/* MADT Interrupt Controller Structure types */

#define ACPI_MADT_TYPE_LOCAL_APIC (0)
#define ACPI_MADT_TYPE_IO_APIC (1)
#define ACPI_MADT_TYPE_LOCAL_APIC64 (5)
#define ACPI_MADT_TYPE_LOCAL_X2APIC (9)

/* Local APIC Flags */

#define ACPI_LAPIC_FLAGS_ENABLED (1 << 0)
#define ACPI_LAPIC_FLAGS_ONLINECAP (1 << 1)
#define ACPI_LAPIC_FLAGS_RESERVED (0xfffffffc)

#define begin_packed_struct
#define end_packed_struct __attribute__((packed))

/****************************************************************************
 * Public Types
 ****************************************************************************/

/* Root System Description Pointer (RSDP) Structure */

begin_packed_struct struct acpi_rsdp_s
{
    int8_t signature[8];            /* ACPI signature, contains "RSD PTR " */
    uint8_t checksum;               /* ACPI 1.0 checksum */
    int8_t oem_id[6];               /* OEM identification */
    uint8_t revision;               /* Must be (0) for ACPI 1.0 or (2) for ACPI 2.0+ */
    uint32_t rsdt_physical_address; /* 32-bit physical address of the RSDT */
    uint32_t length;                /* Table length in bytes, including header (ACPI 2.0+) */
    uint64_t xsdt_physical_address; /* 64-bit physical address of the XSDT (ACPI 2.0+) */
    uint8_t extended_checksum;      /* Checksum of entire table (ACPI 2.0+) */
    uint8_t reserved[3];            /* Reserved, must be zero */
} end_packed_struct;

/* System Description Table Header */

begin_packed_struct struct acpi_sdt_s
{
    char signature[4];         /* Table ASCII identifier */
    uint32_t length;           /* The length of the table in bytes */
    uint8_t revision;          /* The revision of the structure */
    uint8_t checksum;          /* The entire table checksum */
    char oem_id[6];            /* An OEM identification string */
    char oem_table_id[8];      /* An OEM data table string */
    uint32_t oem_revision;     /* An OEM revision number */
    uint32_t creator_id;       /* Vendor ID */
    uint32_t creator_revision; /* Vendor revision */
} end_packed_struct;

/* Root System Description Table */

begin_packed_struct struct acpi_rsdt_s
{
    struct acpi_sdt_s sdt;
    uint32_t table_ptrs;
} end_packed_struct;

/* Extended System Descriptior Table */

begin_packed_struct struct acpi_xsdt_s
{
    struct acpi_sdt_s sdt;
    uint64_t table_ptrs;
} end_packed_struct;

/* Common structure for tables entry */

begin_packed_struct struct acpi_entry_s
{
    uint8_t type;
    uint8_t length;
} end_packed_struct;

/* Multiple APIC Description Table */

begin_packed_struct struct acpi_madt_s
{
    struct acpi_sdt_s sdt;
    uint32_t loapic;
    uint32_t flags;
    struct acpi_entry_s entries;
} end_packed_struct;

/* Multiple APIC Description Table */

begin_packed_struct struct acpi_lapic_s
{
    struct acpi_entry_s entry;
    uint8_t acpi_id;
    uint8_t apic_id;
    uint8_t flags;
} end_packed_struct;

/* Configuration space base address allocation structure */

begin_packed_struct struct acpi_pciseg_s
{
    uint64_t base_addr;     /* Base address */
    uint16_t seg_group_num; /* PCI Segment Group Number */
    uint8_t start_bus;      /* Strt PCI bus number */
    uint8_t end_bus;        /* End PCI bus number */
    uint32_t reserved;      /* Reserved */
} end_packed_struct;

/* PCI Express Memory-mapped Configuration Table */

begin_packed_struct struct acpi_mcfg_s
{
    struct acpi_sdt_s sdt;     /* Header */
    uint64_t reserved;         /* Reserved */
    struct acpi_pciseg_s segs; /* Configuration space base addresses */
} end_packed_struct;

struct acpi_generic_address
{
    uint8_t space_id;     /* Address space where struct or register exists */
    uint8_t bit_width;    /* Size in bits of given register */
    uint8_t bit_offset;   /* Bit offset within the register */
    uint8_t access_width; /* Minimum Access size (ACPI 3.0) */
    uint64_t address;     /* 64-bit address of struct or register */
};
struct acpi_spcr_s
{
    struct acpi_sdt_s sdt;  /* Common ACPI table header */
    uint8_t interface_type; /* 0=full 16550, 1=subset of 16550 */
    uint8_t reserved[3];
    struct acpi_generic_address serial_port;
    uint8_t interrupt_type;
    uint8_t pc_interrupt;
    uint32_t interrupt;
    uint8_t baud_rate;
    uint8_t parity;
    uint8_t stop_bits;
    uint8_t flow_control;
    uint8_t terminal_type;
    uint8_t reserved1;
    uint16_t pci_device_id;
    uint16_t pci_vendor_id;
    uint8_t pci_bus;
    uint8_t pci_device;
    uint8_t pci_function;
    uint32_t pci_flags;
    uint8_t pci_segment;
    uint32_t reserved2;
};

/****************************************************************************
 * Inline Functions
 ****************************************************************************/

#ifndef __ASSEMBLY__

/****************************************************************************
 * Public Data
 ****************************************************************************/

#undef EXTERN
#if defined(__cplusplus)
#define EXTERN extern "C"
extern "C" {
#else
#define EXTERN extern
#endif

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Name: acpi_init
 *
 * Description:
 *   Initialize ACPI parser.
 *
 ****************************************************************************/

int acpi_init(void);

/****************************************************************************
 * Name: acpi_madt_get
 *
 * Description:
 *   Find the n'th occurence of a MADT entry with a given type.
 *
 ****************************************************************************/

int acpi_madt_get(int type, int n, struct acpi_entry_s **entry);

/****************************************************************************
 * Name: acpi_lapi_get
 *
 * Description:
 *   Get Local APIC entry for a given CPU.
 *
 ****************************************************************************/

int acpi_lapic_get(int cpu, struct acpi_lapic_s **lapic);

#ifdef CONFIG_ARCH_X86_64_ACPI_DUMP
/****************************************************************************
 * Name: acpi_dump
 *
 * Description:
 *   Dump ACPI tables.
 *
 ****************************************************************************/

void acpi_dump(void);
#endif

phys_addr_t acpi_mcfg_base_get(void);
#undef EXTERN
#if defined(__cplusplus)
}
#endif

#endif /* __ASSEMBLY__ */
#endif /* __ARCH_X86_64_INCLUDE_ACPI_H */
