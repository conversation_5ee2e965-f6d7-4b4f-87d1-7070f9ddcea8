#ifndef _x86_pci_io_h
#define _x86_pci_io_h

#include <acpi.h>
#include <asm/io.h>

#define X86_64_IO_ADDR_LIMIT 0xffff

#define port_readb(addr, val) inb(addr, val)
#define port_readw(addr, val) inw(addr, val)
#define port_readl(addr, val) inl(addr, val)

#define port_writeb(addr, val) outb(addr, val)
#define port_writew(addr, val) outw(addr, val)
#define port_writel(addr, val) outl(addr, val)

// #define X86_64_IO_ADDR_LIMIT 0xffff

#define mm_readb(addr, val) (val = *((volatile uint8_t *)(addr)))

#define mm_readw(addr, val) (val = *((volatile uint16_t *)(addr)))

#define mm_readl(addr, val) (val = *((volatile uint32_t *)(addr)))

#define mm_writeb(addr, val) (*((volatile uint8_t *)(addr)) = (val))

#define mm_writew(addr, val) (*((volatile uint16_t *)(addr)) = (val))

#define mm_writel(addr, val) (*((volatile uint32_t *)(addr)) = (val))

static inline int x86_64_pci_read_port(struct pci_host *bus, uintptr_t addr, int size,
                                       uint32_t *val)
{
    switch (size)
    {
    case 1:
        port_readb(addr, *val);
        break;
    case 2:
        port_readw(addr, *val);
        break;
    case 4:
        port_readl(addr, *val);
        break;
    default:
        *val = 0;
        return -EINVAL;
    }

    return 0;
}

static inline int x86_64_pci_read_mem(struct pci_host *bus, uintptr_t addr, int size, uint32_t *val)
{
    switch (size)
    {
    case 1:
        mm_readb(addr, *val);
        break;
    case 2:
        mm_readw(addr, *val);
        break;
    case 4:
        mm_readl(addr, *val);
        break;
    default:
        *val = 0;
        return -EINVAL;
    }

    return 0;
}

static inline int x86_64_pci_write_port(struct pci_host *bus, uintptr_t addr, int size,
                                        uint32_t val)
{
    switch (size)
    {
    case 1:
        port_writeb(addr, val);
        break;
    case 2:
        port_writew(addr, val);
        break;
    case 4:
        port_writel(addr, val);
        break;
    default:
        return -EINVAL;
    }

    return 0;
}

static inline int x86_64_pci_write_mem(struct pci_host *bus, uintptr_t addr, int size, uint32_t val)
{
    switch (size)
    {
    case 1:
        mm_writeb(addr, val);
        break;
    case 2:
        mm_writew(addr, val);
        break;
    case 4:
        mm_writel(addr, val);
        break;
    default:
        return -EINVAL;
    }

    return 0;
}

static inline int x86_64_pci_read_io(struct pci_host *bus, uintptr_t addr, int size, uint32_t *val)
{
    if (addr > X86_64_IO_ADDR_LIMIT)
    {
        return x86_64_pci_read_mem(bus, addr, size, val);
    }
    else
    {
        return x86_64_pci_read_port(bus, addr, size, val);
    }
}

/****************************************************************************
 * Name: x86_64_pci_write_io
 *
 * Description:
 *  Write 8, 16, 32, 64 bits data to PCI io address space of x86 64 device
 *
 * Input Parameters:
 *   bus    - Bus that PCI device resides
 *   addr   - The address to write data
 *   size   - The requested number of bytes to be write
 *   val    - The value to write
 *
 * Returned Value:
 *   0: success, <0: A negated errno
 *
 ****************************************************************************/

static inline int x86_64_pci_write_io(struct pci_host *bus, uintptr_t addr, int size, uint32_t val)
{
    if (addr > X86_64_IO_ADDR_LIMIT)
    {
        return x86_64_pci_write_mem(bus, addr, size, val);
    }
    else
    {
        return x86_64_pci_write_port(bus, addr, size, val);
    }
}
#endif