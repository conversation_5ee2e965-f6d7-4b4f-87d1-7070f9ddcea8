#ifndef PROCESS_EXIT_H
#define PROCESS_EXIT_H

#include <system/types.h>
#include <ttosProcess.h>
#include <sys/wait.h>

typedef struct
{
    int   exit_status;
    pid_t pid;

} wait_stat;

typedef struct
{
    pid_t                 pid;
    int                   options;
    int __user           *stat_addr;
    struct rusage __user *ru;
    wait_stat            stat;
} wait_handle_t;

typedef enum
{
    STOPPED,
    EXITED,
} process_exit_state;

pcb_t wait_process_zombie (pcb_t pcb, int state, wait_handle_t *wait_handle);
pcb_t wait_thread_zombie(pcb_t pcb, int ptraced, wait_handle_t *wait_handle);
int   wait_process (pcb_t target, int ptraced, wait_handle_t *wait_handle);
#endif /* PROCESS_EXIT_H */