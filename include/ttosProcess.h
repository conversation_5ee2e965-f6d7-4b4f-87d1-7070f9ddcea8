#ifndef __TTOS_PROCESS_H__
#define __TTOS_PROCESS_H__

#include <completion.h>
#include <fs/fs.h>
#include <pgroup.h>
#include <process_obj.h>
#include <ptrace/ptrace.h>
#include <ttos.h>
#include <ttosMM.h>
#include <ttosRBTree.h>
#include <tty.h>

#ifndef __user
#define __user
#endif

#define INVALID_PROCESS_ID ((pid_t)-1)

#define FORKNOEXEC 0x00000001

/* Used in pcb->exit_state: */
#define EXIT_DEAD 0x00000010
#define EXIT_ZOMBIE 0x00000020
#define EXIT_TRACE (EXIT_ZOMBIE | EXIT_DEAD)

#define COREDUMP_FLAG (0x80)
#define PROCESS_CREATE_STAT_EXIT(exit_code) (((exit_code)&0xff) << 8)
#define PROCESS_CREATE_STAT_SIGNALED(signo, coredump)                                              \
    ((signo & 0x7f) | (coredump ? COREDUMP_FLAG : 0))
#define PROCESS_CREATE_STAT_STOPPED(signo) (PROCESS_CREATE_STAT_EXIT(signo) | 0x7f)
#define PROCESS_CREATE_STAT_CONTINUED (0xffff)

#define CREATE_PTRACE_STATUS(signo) ((signo) << 8 | 0x7f)

#ifdef __arm__

union debug_insn {
    uint32_t arm;
    uint16_t thumb;
};

struct debug_entry
{
    uint32_t address;
    union debug_insn insn;
};

struct debug_info
{
    int nsaved;
    struct debug_entry bp[2];
};
#endif

struct waitpid_info
{
    T_TTOS_Task_Queue_Control waitQueue;
    /* 保留退出的子进程，父进程将其从僵尸态释放 */
    pcb_t waker;
    int status;
};

struct T_TTOS_ProcessControlBlock
{
    struct arch_context exception_context;
    TASK_ID taskControlId;
    struct T_TTOS_ProcessControlBlock *parent;
    struct T_TTOS_ProcessControlBlock *first_child;
    struct T_TTOS_ProcessControlBlock *sibling;
    struct process_obj *pid;
    pcb_t group_leader;
    struct pgroup *pgrp;
    struct process_obj *mm;
    struct process_obj *vfs_list;
    struct process_obj *signal;
    struct process_obj *sighand;
    process_sigset_t blocked;
    process_sigset_t real_blocked;
    process_sigset_t saved_sigmask;
    unsigned long sas_ss_sp;
    size_t sas_ss_size;
    struct process_obj *cmdline;
    phys_addr_t auxvp;
    struct process_obj *envp;
    int (*entry)(void *);
    void *args;
    void *userStack;
    T_ULONG tls;
    struct process_obj *futex_root;
    int __user *set_child_tid;
    int __user *clear_child_tid;

    int exit_state;
    int exit_code;
    int exit_signal;

    T_TTOS_CompletionControl *vfork_done;
    unsigned long ptrace;
    unsigned long ptrace_first_start;
    unsigned long ptrace_message;
    struct list_head thread_group;
    struct list_head sibling_node;
    struct list_head pgrp_node;
    struct list_head obj_list;
    struct list_head sched_group_node;
    struct list_head non_auto_start_node;
    struct timespec64 utime;
    struct timespec64 utime_prev;
    struct timespec64 stime;
    struct timespec64 stime_prev;
    struct timespec64 start_time;
    struct list_head posix_timers;
    struct process_sigqueue sig_queue;
    struct waitpid_info wait_info;
    ksiginfo_t ptrace_siginfo;

#ifdef __arm__
    /* 用于gdb调试，保存断点处的原有指令 */
    struct debug_info debug;
#elif defined(__aarch64__)
    struct user_hwdebug_state debugbp;
    struct user_hwdebug_state debugwr;
#endif
    unsigned int personality;
    /* 启动进程的用户 */
    uid_t uid;
    /* 用于判断权限的uid 对于带有setuid位的二进制文件启动时更改 */
    uid_t euid;
    /* 用于保存变化前的 euid */
    uid_t suid;
    /* 启动进程的用户组 */
    uid_t gid;
    /* 用于判断权限的egid 对于带有setgid位的二进制文件启动时更改 */
    uid_t egid;
    /* 用户保存变化前的egid */
    uid_t sgid;

    pid_t tgid;
    pid_t pgid;
    struct
    {
        bool is_terminated : 1;
        bool group_request_terminate : 1;
        bool is_normal_exit : 1;
        uint8_t padding : 5;
        uint32_t exit_code : 24;
    } group_exit_status;
    pid_t sid;
    mode_t umask;
    int need_restore_blocked;
    unsigned int sas_ss_flags;
    uint32_t aux_len;
    unsigned int jobctl_stopped;
    unsigned int wait_reap_stp;
    T_UWORD state;
    int sched_group_id;
    int flags;
    ttos_spinlock_t lock;
    MUTEX_ID tglock;
    char cmd_name[NAME_MAX];
    char cmd_path[PATH_MAX];
    char pwd[PATH_MAX];
    char root[PATH_MAX];
    unsigned long jobctl;
};

pcb_t ttosProcessSelf(void);
void process_destroy(pcb_t pcb);
int do_execve(const char *filename, const char *const *argv, const char *const *envp);

struct process_obj *pid_obj_alloc(pcb_t pcb);
int pid_obj_ref(pcb_t parent, pcb_t child);
pcb_t pcb_get_by_pid(pid_t pid);
pcb_t pcb_get_by_pid_nt(pid_t pid);

void process_filelist_copy(pcb_t parent, pcb_t child);
void process_filelist_ref(pcb_t parent, pcb_t child);
void process_filelist_create(pcb_t pcb);

void process_mm_ref(pcb_t parent, pcb_t child);
void process_mm_create(pcb_t child);
void process_mm_copy(pcb_t parent, pcb_t child);
phys_addr_t process_mm_map(pcb_t pcb, phys_addr_t paddr, virt_addr_t *vaddr, uintptr_t attr,
                           size_t page_count, int flags);
int process_mm_unmap(pcb_t pcb, virt_addr_t vaddr);
int process_mremap(virt_addr_t addr, unsigned long old_len, unsigned long new_len,
                   unsigned long flags, virt_addr_t *new_addr);
long process_exit_group(int flag, bool is_normal_exit);
void process_exit(pcb_t pcb);

void vfork_exec_wake(pcb_t pcb);
void vfork_exit_wake(pcb_t pcb);

int process_create_futex_root(pcb_t pcb);
int process_ref_futex_root(pcb_t parent, pcb_t child);

void process_tid_set_to_pid(pcb_t pcb);
void foreach_process_child(pcb_t pcb, void (*func)(pcb_t, void *), void *param);
void foreach_task_group(pcb_t pcb, void (*func)(pcb_t, void *), void *param);

void process_foreach(void (*func)(pcb_t, void *), void *arg);
pid_t kernel_execve(const char *filename, const char *const *argv, const char *const *envp);
struct filelist *pcb_get_files(pcb_t pcb);
pid_t pid_obj_get_pid(struct process_obj *obj);
char *process_getfullpath(int dirfd, const char __user *path);
void pcb_set_pid_leader(pcb_t pcb);
void process_wakeup_waiter(pcb_t pcb);
int process_release_zombie(pcb_t pcb);
struct file *process_getfile(pcb_t pcb, int fd);
uint64_t process_prot_to_attr(unsigned long prot);
int process_mmap(pcb_t pcb, unsigned long *addr, unsigned long len, unsigned long prot,
                 unsigned long flags, int fd, off_t off, int file_len);
#define get_process_mm(pcb) PROCESS_OBJ_GET((pcb)->mm, struct mm *)
#define get_process_vfs_list(pcb) PROCESS_OBJ_GET((pcb)->vfs_list, struct filelist *)
#define get_process_pid(pcb) pid_obj_get_pid((pcb)->pid)

#define get_process_sighand(pcb) PROCESS_OBJ_GET((pcb)->sighand, struct sighand_struct *)

#define get_process_signal(pcb) PROCESS_OBJ_GET((pcb)->signal, struct ttos_signal *)

#define get_process_ctty(pcb)   ((tty_t)get_process_signal(pcb)->ctty)

#define for_each_thread_in_tgroup(pcb, tmp_pcb)                                                    \
    list_for_each_entry(tmp_pcb, &pcb->group_leader->thread_group, sibling_node)
pid_t do_fork(unsigned long clone_flags, unsigned long newsp, int __user *set_child_tid,
              int __user *clear_child_tid, unsigned long tls, struct period_param *param);

void pcb_lock(pcb_t pcb, long *flags);
void pcb_unlock(pcb_t pcb, long *flags);

/* 兼容linux的 current */
#define current (ttosProcessSelf())

#endif /* __TTOS_PROCESS_H__ */
