#include <stdio.h>
#include <assert.h>
#include <errno.h>
#include <ttos_time.h>
#include <unistd.h>
#include <io.h>
#include <driver/i2c/i2c.h>
#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/of.h>
#include <ttosMM.h>
#include <ttos_init.h>
#include <ttos_pic.h>
#include <ttos_time.h>
#include "dw_i2c.h"

#undef KLOG_TAG
#define KLOG_TAG "dw_i2c"
#include <klog.h>

#define udelay(x)	ttos_time_udelay(x)

struct dw_i2c_priv_s
{
    struct i2c_bus_device bus_device;
    u32 speed_rate;
	i2c_adap_dev_t i2c_adap; 
    MUTEX_ID lock;
};

static int dw_i2c_xfer(i2c_adap_dev_t *adap, struct i2c_msg *msgs, int num);
static int dw_i2c_poll_handler(i2c_adap_dev_t *adap);

static int semidrv_i2c_transfer (const struct i2c_bus_device *dev,
                            struct i2c_msg *msgs, uint8_t num, uint16_t addr);
static struct i2c_host_ops semidrv_i2c_host_ops = {
    .transfer   = semidrv_i2c_transfer,
};

static int semidrv_i2c_transfer (const struct i2c_bus_device *dev,
                            struct i2c_msg *msgs, uint8_t num, uint16_t addr)
{
    struct dw_i2c_priv_s *priv = NULL;
    u8 mem_addr = 0;
    int ret = 0;

    if ((NULL == dev) || (NULL == msgs))
    {
        ret = -EINVAL;
        KLOG_E("i2c transfer error, dev or msgs is NULL");
        return ret;
    }

    priv = (struct dw_i2c_priv_s *)dev->priv;
    if (NULL == priv)
    {
        ret = -EFAULT;
        return ret;
    }

  //  TTOS_ObtainMutex (priv->lock, TTOS_WAIT_FOREVER);

	msgs->addr = addr;
	ret = dw_i2c_xfer(&(priv->i2c_adap) , msgs, num);

  //  TTOS_ReleaseMutex (priv->lock);

    return ret;
}

static void dw_i2c_enable(virt_addr_t base)
{
    writel(1, base + DW_IC_ENABLE);
}

static void dw_i2c_disable_nowait(virt_addr_t base)
{
    writel(0, base + DW_IC_ENABLE);
}

static void dw_i2c_disable(i2c_adap_dev_t *adap)
{
    int timeout = 100;
    uint32_t status;

    do {
        dw_i2c_disable_nowait(adap->base);
        /*
         * The enable status register may be unimplemented, but
         * in that case this test reads zero and exits the loop.
         */
        status = readl(adap->base + DW_IC_ENABLE_STATUS);

        if ((status & 1) == 0)
            return;

        /*
         * Wait 10 times the signaling period of the highest I2C
         * transfer supported by the driver (for 400KHz this is
         * 25us) as described in the DesignWare I2C databook.
         */
        udelay(100);
    }
    while (timeout--);

    KLOG_E("i2c%d-adap, timeout disabling adapter", adap->id);
}

static void dw_i2c_disable_int(virt_addr_t base)
{
    writel(0, base + DW_IC_INTR_MASK);
}

static int dw_i2c_get_fsm_stat(virt_addr_t base)
{
    uint32_t stat, enabled;

    enabled = readl(base + DW_IC_ENABLE);
    stat = readl(base + DW_IC_RAW_INTR_STAT);

    if (!enabled || !(stat & ~DW_IC_INTR_ACTIVITY))
        return 0;

    return 1;
}

static uint32_t dw_i2c_read_clear_intrbits(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    uint32_t stat;

    /*
     * The IC_INTR_STAT register just indicates "enabled" interrupts.
     * The unmasked raw version of interrupt status bits is available
     * in the IC_RAW_INTR_STAT register.
     *
     * That is,
     *   stat = readl(IC_INTR_STAT);
     * equals to,
     *   stat = readl(IC_RAW_INTR_STAT) & readl(IC_INTR_MASK);
     *
     * The raw version might be useful for debugging purposes.
     */
    stat = readl(adap->base + DW_IC_INTR_STAT);

    /*
     * Do not use the IC_CLR_INTR register to clear interrupts, or
     * you'll miss some interrupts, triggered during the period from
     * readl(IC_INTR_STAT) to readl(IC_CLR_INTR).
     *
     * Instead, use the separately-prepared IC_CLR_* registers.
     */
    if (stat & DW_IC_INTR_RX_UNDER)
        readl(adap->base + DW_IC_CLR_RX_UNDER);

    if (stat & DW_IC_INTR_RX_OVER)
        readl(adap->base + DW_IC_CLR_RX_OVER);

    if (stat & DW_IC_INTR_TX_OVER)
        readl(adap->base + DW_IC_CLR_TX_OVER);

    if (stat & DW_IC_INTR_RD_REQ)
        readl(adap->base + DW_IC_CLR_RD_REQ);

    if (stat & DW_IC_INTR_TX_ABRT) {
        /*
         * The IC_TX_ABRT_SOURCE register is cleared whenever
         * the IC_CLR_TX_ABRT is read.  Preserve it beforehand.
         */
        cxt->abort_source = readl(adap->base + DW_IC_TX_ABRT_SOURCE);
        readl(adap->base + DW_IC_CLR_TX_ABRT);
    }

    if (stat & DW_IC_INTR_RX_DONE)
        readl(adap->base + DW_IC_CLR_RX_DONE);

    if (stat & DW_IC_INTR_ACTIVITY)
        readl(adap->base + DW_IC_CLR_ACTIVITY);

    if (stat & DW_IC_INTR_STOP_DET)
        readl(adap->base + DW_IC_CLR_STOP_DET);

    if (stat & DW_IC_INTR_START_DET)
        readl(adap->base + DW_IC_CLR_START_DET);

    if (stat & DW_IC_INTR_GEN_CALL)
        readl(adap->base + DW_IC_CLR_GEN_CALL);

    if (stat & DW_IC_INTR_SCL_STUCK)
        readl(adap->base + DW_IC_CLR_SCL_STUCK_DET);

    return stat;
}

static void dw_i2c_read(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    struct i2c_msg *msgs = cxt->msgs;
    unsigned int rx_valid;

    for (; cxt->msg_read_idx < cxt->msgs_num; cxt->msg_read_idx++) {
        uint32_t len;
        uint8_t *buf;

        if (!(msgs[cxt->msg_read_idx].flags & I2C_MSG_READ))
            continue;

        if (!(cxt->status & DW_STATUS_R_IN_PROGRESS)) {
            len = msgs[cxt->msg_read_idx].len;
            buf = msgs[cxt->msg_read_idx].buf;
        }
        else {
            len = cxt->rx_buf_len;
            buf = cxt->rx_buf;
        }

        rx_valid = readl(adap->base + DW_IC_RXFLR);

        for (; len > 0 && rx_valid > 0; len--, rx_valid--) {
            *buf++ = readl(adap->base + DW_IC_DATA_CMD);
            cxt->rx_outstanding--;
        }

        if (len > 0) {
            cxt->status |= DW_STATUS_R_IN_PROGRESS;
            cxt->rx_buf_len = len;
            cxt->rx_buf = buf;
            return;
        }
        else
            cxt->status &= ~DW_STATUS_R_IN_PROGRESS;
    }
}

/*
 * Initiate (and continue) low level master read/write transaction.
 * This function is only called from i2c_dw_isr, and pumping i2c_msg
 * messages into the tx buffer.  Even if the size of i2c_msg data is
 * longer than the size of the tx buffer, it handles everything.
 */
static void dw_i2c_xfer_msg(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    struct i2c_msg *msgs = cxt->msgs;
    uint32_t intr_mask;
    int tx_limit, rx_limit;
    uint32_t addr = msgs[cxt->msg_write_idx].addr;
    uint32_t buf_len = cxt->tx_buf_len;
    uint8_t *buf = cxt->tx_buf;
    bool need_restart = false;
    unsigned int flr;

    intr_mask = DW_IC_INTR_MASTER_MASK;

    for (; cxt->msg_write_idx < cxt->msgs_num; cxt->msg_write_idx++) {
        /*
         * If target address has changed, we need to
         * reprogram the target address in the I2C
         * adapter when we are done with this transfer.
         */
        if (msgs[cxt->msg_write_idx].addr != addr) {
            KLOG_E("%s: invalid target addr", __func__);
            cxt->msg_err = -DW_I2C_EMSG;
            break;
        }

        if (!(cxt->status & DW_STATUS_W_IN_PROGRESS)) {
            /* new i2c_msg */
            buf = msgs[cxt->msg_write_idx].buf;
            buf_len = msgs[cxt->msg_write_idx].len;

            /* If both IC_EMPTYFIFO_HOLD_MASTER_EN and
             * IC_RESTART_EN are set, we must manually
             * set restart bit between messages.
             */
            if ((cxt->master_cfg & DW_IC_CON_RESTART_EN) &&
                    (cxt->msg_write_idx > 0))
                need_restart = true;
        }

        flr = readl(adap->base + DW_IC_TXFLR);
        tx_limit = cxt->tx_fifo_depth - flr;

        flr = readl(adap->base + DW_IC_RXFLR);
        rx_limit = cxt->rx_fifo_depth - flr;

        while (buf_len > 0 && tx_limit > 0 && rx_limit > 0) {
            uint32_t cmd = 0;
            /*
             * If IC_EMPTYFIFO_HOLD_MASTER_EN is set we must
             * manually set the stop bit. However, it cannot be
             * detected from the registers so we set it always
             * when writing/reading the last byte.
             */

            /*
             * i2c-core always sets the buffer length of
             * I2C_FUNC_SMBUS_BLOCK_DATA to 1. The length will
             * be adjusted when receiving the first byte.
             * Thus we can't stop the transaction here.
             */
            if (cxt->msg_write_idx == cxt->msgs_num - 1 && buf_len == 1)
                cmd |= 0x1 << 9;

            if (need_restart) {
                cmd |= 0x1 << 10;
                need_restart = false;
            }

			if (msgs[cxt->msg_write_idx].flags & I2C_MSG_READ) {
                /* Avoid rx buffer overrun */
                if (cxt->rx_outstanding >= cxt->rx_fifo_depth)
                    break;

                writel(cmd | 0x100, adap->base + DW_IC_DATA_CMD);
                rx_limit--;
                cxt->rx_outstanding++;
            }
            else {
                writel(cmd | *buf++, adap->base + DW_IC_DATA_CMD);
            }

            tx_limit--;
            buf_len--;
        }

        cxt->tx_buf = buf;
        cxt->tx_buf_len = buf_len;

        /*
         * Because we don't know the buffer length in the
         * I2C_FUNC_SMBUS_BLOCK_DATA case, we can't stop
         * the transaction here.
         */
        if (buf_len > 0) {
            /* more bytes to be written */
            cxt->status |= DW_STATUS_W_IN_PROGRESS;
            break;
        }
        else
            cxt->status &= ~DW_STATUS_W_IN_PROGRESS;
    }

    /*
     * If i2c_msg index search is completed, we don't need TX_EMPTY
     * interrupt any more.
     */
    if (cxt->msg_write_idx == cxt->msgs_num)
        intr_mask &= ~DW_IC_INTR_TX_EMPTY;

    if (cxt->msg_err)
        intr_mask = 0;

    writel(intr_mask, adap->base + DW_IC_INTR_MASK);
}


static void dw_i2c_irq_handler(uint32_t irq, void *arg)
{
    i2c_adap_dev_t *adap = arg;
    dw_i2c_context *cxt = adap->priv;
    uint32_t stat;

    if (!dw_i2c_get_fsm_stat(adap->base))
        return ;

    stat = dw_i2c_read_clear_intrbits(adap);

    if (stat & (DW_IC_INTR_TX_ABRT | DW_IC_INTR_SCL_STUCK)) {
        if (stat & DW_IC_INTR_TX_ABRT)
            cxt->cmd_err |= DW_IC_ERR_TX_ABRT;

        if (stat & DW_IC_INTR_SCL_STUCK)
            cxt->cmd_err |= DW_IC_ERR_SCL_STUCK;

        cxt->status = DW_STATUS_IDLE;

        /*
         * Anytime TX_ABRT is set, the contents of the tx/rx
         * buffers are flushed. Make sure to skip them.
         */
        writel(0, adap->base + DW_IC_INTR_MASK);
        goto tx_aborted;
    }

    if (stat & DW_IC_INTR_RX_FULL)
        dw_i2c_read(adap);

    if (stat & DW_IC_INTR_TX_EMPTY)
        dw_i2c_xfer_msg(adap);

    /*
     * No need to modify or disable the interrupt mask here.
     * i2c_dw_xfer_msg() will take care of it according to
     * the current transmit status.
     */

tx_aborted:

    if ((stat & (DW_IC_INTR_TX_ABRT | DW_IC_INTR_SCL_STUCK | DW_IC_INTR_STOP_DET))
            || cxt->msg_err)
        TTOS_SendEvent(cxt->event, DW_I2C_EVENT_COMPLETE);

    return;
}

static int dw_i2c_poll_write(i2c_adap_dev_t *adap, uint8_t *txbuf,
                             uint16_t txlen, bool need_restart, bool need_stop)
{
    dw_i2c_context *cxt = adap->priv;
    uint32_t cmd, stat;
    int flag = 0;
    int cnt = 0;

    while (txlen) {
        stat = dw_i2c_read_clear_intrbits(adap);

        if (stat & (DW_IC_INTR_TX_ABRT | DW_IC_INTR_SCL_STUCK)) {
            if (stat & DW_IC_INTR_TX_ABRT)
                cxt->cmd_err |= DW_IC_ERR_TX_ABRT;

            if (stat & DW_IC_INTR_SCL_STUCK)
                cxt->cmd_err |= DW_IC_ERR_SCL_STUCK;

            return -1;
        }

        stat = readl(adap->base + DW_IC_STATUS);

        if (stat & 0x2) {
            cnt = 0;
            cmd = 0;

            if (need_stop && txlen == 1)
                cmd |= 0x1 << 9;

            if (need_restart) {
                cmd |= 0x1 << 10;
                need_restart = false;
            }

            writel(cmd | *txbuf++, adap->base + DW_IC_DATA_CMD);
            txlen--;
        }
        else {
            udelay(5);
            if (cnt++ > 10000)
                return -DW_I2C_ETIMEOUT;
        }
    }

    cnt = 0;
    while (1) {
        stat = dw_i2c_read_clear_intrbits(adap);

        if (stat & (DW_IC_INTR_TX_ABRT | DW_IC_INTR_SCL_STUCK)) {
            if (stat & DW_IC_INTR_TX_ABRT)
                cxt->cmd_err |= DW_IC_ERR_TX_ABRT;

            if (stat & DW_IC_INTR_SCL_STUCK)
                cxt->cmd_err |= DW_IC_ERR_SCL_STUCK;

            return -1;
        }
        else if (need_stop && (stat & DW_IC_INTR_STOP_DET)) {
            return 0;
        }
        else if (readl(adap->base + DW_IC_STATUS) & 0x4) {
            if (!flag) {
                flag = 1;
                continue;
            }

            return 0;
        }
        else {
            udelay(5);
            if (cnt++ > 10000)
                return -DW_I2C_ETIMEOUT;
        }
    }

    return 0;
}

static int dw_i2c_poll_read(i2c_adap_dev_t *adap, uint8_t *rxbuf,
                            uint16_t rxlen, bool need_restart, bool need_stop)
{
    dw_i2c_context *cxt = adap->priv;
    uint16_t txlen = rxlen;
    uint32_t cmd, stat, rx_valid;
    int cnt = 0;

    while (1) {
        stat = dw_i2c_read_clear_intrbits(adap);

        if (stat & (DW_IC_INTR_TX_ABRT | DW_IC_INTR_SCL_STUCK)) {
            if (stat & DW_IC_INTR_TX_ABRT)
                cxt->cmd_err |= DW_IC_ERR_TX_ABRT;

            if (stat & DW_IC_INTR_SCL_STUCK)
                cxt->cmd_err |= DW_IC_ERR_SCL_STUCK;

            return -1;
        }
        else if (!rxlen) {
            return 0;
        }

        stat = readl(adap->base + DW_IC_STATUS);

        if ((stat & 0x2) && (cxt->rx_outstanding < rxlen)
                && (cxt->rx_outstanding < cxt->rx_fifo_depth)) {
            cmd = 0;

            if (need_stop && txlen == 1)
                cmd |= 0x1 << 9;

            if (need_restart) {
                cmd |= 0x1 << 10;
                need_restart = false;
            }

            writel(cmd | 0x100, adap->base + DW_IC_DATA_CMD);
            cxt->rx_outstanding++;
            txlen--;
        }

        rx_valid = readl(adap->base + DW_IC_RXFLR);

        if (rx_valid > 0) {
            cnt = 0;
            for (; rxlen > 0 && rx_valid > 0; rxlen--, rx_valid--) {
                *rxbuf++ = readl(adap->base + DW_IC_DATA_CMD);
                cxt->rx_outstanding--;
            }
        }
        else {
            udelay(5);
            if (cnt++ > 10000)
                return -DW_I2C_ETIMEOUT;
        }
    }

    return 0;
}

static int dw_i2c_poll_handler(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    bool need_restart = false;
    bool need_stop = false;
    int ret, cnt = 100;

    while (cnt--) {
        if (dw_i2c_get_fsm_stat(adap->base))
            break;

        udelay(10);
    }

    if (cnt <= 0)
        return -1;

    for (int i = 0; i < cxt->msgs_num; i++) {
        if (i > 0) {
            need_restart = true;

            if (cxt->msgs[i - 1].addr != cxt->msgs[i].addr) {
                KLOG_E("%s: invalid target addr\n", __func__);
                cxt->msg_err = -EINVAL;
                return -1;
            }
        }

        if (i == cxt->msgs_num - 1)
            need_stop = true;

        if (cxt->msgs[i].flags & I2C_MSG_READ)
            ret = dw_i2c_poll_read(adap, cxt->msgs[i].buf, cxt->msgs[i].len,
                                   need_restart, need_stop);
        else
            ret = dw_i2c_poll_write(adap, cxt->msgs[i].buf, cxt->msgs[i].len,
                                    need_restart, need_stop);

        if (ret < 0)
            break;
    }

    return ret;
}

static int dw_i2c_wait_bus_not_busy(i2c_adap_dev_t *adap)
{
    uint32_t status;
    int ret = 0, cnt = 100;

    while (cnt--) {
        status = readl(adap->base + DW_IC_STATUS);

        if (!(status & DW_IC_STATUS_ACTIVITY))
            break;

        udelay(200);
    }

    if (cnt <= 0)
        ret = -1;

    return ret;
}

static int dw_i2c_xfer_init(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    struct i2c_msg *msgs = cxt->msgs;
    uint32_t master_cfg = cxt->master_cfg;

    /* Disable the adapter */
    dw_i2c_disable(adap);

    /* If the slave address is ten bit address, enable 10BITADDR */
    if (!adap->use_7bit_addr)
       master_cfg |= DW_IC_CON_10BITADDR_MASTER;

    writel(master_cfg, adap->base + DW_IC_CON);

    /* Set the slave (target) address */
    writel(msgs[0].addr, adap->base + DW_IC_TAR);

    /* Enforce disabled interrupts (due to HW issues) */
    dw_i2c_disable_int(adap->base);

    /* Enable the adapter */
    dw_i2c_enable(adap->base);

    /* Clear and enable interrupts */
    readl(adap->base + DW_IC_CLR_INTR);
    writel(DW_IC_INTR_MASTER_MASK, adap->base + DW_IC_INTR_MASK);
}

static void dw_i2c_sda_stuck_recover(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    int cnt = 0;

    writel(0x9, adap->base + DW_IC_ENABLE);

    while (cnt++ < 10) {
        udelay(20);

        if (!(readl(adap->base + DW_IC_ENABLE) & 0x8))
            break;
    }

    if (readl(adap->base + DW_IC_STATUS) & 0x800)
        cxt->sda_stuck_err = 1;
}

static int dw_i2c_xfer(i2c_adap_dev_t *adap, struct i2c_msg *msgs, int num)
{
    dw_i2c_context *cxt = adap->priv;
    int ret = 0;
    T_TTOS_EventSet eventOut = 0;
    T_TTOS_ReturnCode flags = TTOS_OK;

    if (!adap || !msgs || (num == 0)) {
        KLOG_E("i2c%d-adap, param err %p,%p,%d",
                    adap->id, adap, msgs, num);
        ret = -DW_I2C_EPARAM;
        goto done;
    }

    if (cxt->scl_stuck_err) {
        KLOG_E("i2c%d-adap, scl stuck", adap->id);
        ret = -DW_I2C_ESCLSTUCK;
        goto done;
    }

    if (cxt->sda_stuck_err) {
        KLOG_E("i2c%d-adap, sda stuck", adap->id);
        ret = -DW_I2C_ESDASTUCK;
        goto done;
    }

    ret = dw_i2c_wait_bus_not_busy(adap);
    if (ret < 0) {
        ret = -DW_I2C_EBUSY;
        goto done;
    }

    cxt->msgs = msgs;
    cxt->msgs_num = num;
    cxt->cmd_err = 0;
    cxt->msg_write_idx = 0;
    cxt->msg_read_idx = 0;
    cxt->msg_err = 0;
    cxt->status = DW_STATUS_IDLE;
    cxt->abort_source = 0;
    cxt->rx_outstanding = 0;

    dw_i2c_xfer_init(adap);
 
    if (USE_POLLING)
    {
    	ret = dw_i2c_poll_handler(adap);
    }
    else {
        flags = TTOS_ReceiveEvent(cxt->event, DW_I2C_EVENT_COMPLETE,TTOS_EVENT_ANY, DW_I2C_EVENT_TIMEOUT, &eventOut);

        if (TTOS_TIMEOUT == flags)
            ret = -DW_I2C_ETIMEOUT;
    }

    if (ret == (-DW_I2C_ETIMEOUT)) {
        dw_i2c_disable(adap);
        goto done;
    }

    /*
     * We must disable the adapter before returning and signaling the end
     * of the current transfer. Otherwise the hardware might continue
     * generating interrupts which in turn causes a race condition with
     * the following transfer.  Needs some more investigation if the
     * additional interrupts are a hardware bug or this driver doesn't
     * handle them correctly yet.
     */
    dw_i2c_disable_nowait(adap->base);

    if (cxt->msg_err) {
        ret = cxt->msg_err;
        goto done;
    }

    /* No error */
    if (likely(!cxt->cmd_err && !cxt->status)) {
        ret = 0;
        goto done;
    }

    /* We have an error */
    if (cxt->cmd_err & DW_IC_ERR_SCL_STUCK) {
        cxt->scl_stuck_err = 1;
        ret = -DW_I2C_ESCLSTUCK;
        goto done;
    }

    if (cxt->cmd_err & DW_IC_ERR_TX_ABRT) {
        if (cxt->abort_source & DW_IC_TX_ABRT_NOACK)
            ret = -DW_I2C_ENOACK;
        else if (cxt->abort_source & DW_IC_TX_ARB_LOST)
            ret = -DW_I2C_EARBLOST;
        else if (cxt->abort_source & DW_IC_TX_ABRT_GCALL_READ)
            ret = -DW_I2C_EINVAL;
        else if (cxt->abort_source & DW_IC_ABRT_SDA_STUCK_AT_LOW) {
            ret = -DW_I2C_ESDASTUCK;
            dw_i2c_sda_stuck_recover(adap);
        }
        else
            ret = -DW_I2C_EIO;

        goto done;
    }

    if (cxt->status)
        KLOG_E("i2c%d-adap, trans terminated early,int latency too high?", adap->id);

    ret = -DW_I2C_EIO;

done:
    return ret;
}

static  uint32_t dw_i2c_scl_hcnt(uint32_t ic_clk, uint32_t tSYMBOL, uint32_t tf,
                                 int cond, int offset)
{
    /*
     * DesignWare I2C core doesn't seem to have solid strategy to meet
     * the tHD;STA timing spec.  Configuring _HCNT based on tHIGH spec
     * will result in violation of the tHD;STA spec.
     */
    if (cond)
        /*
         * Conditional expression:
         *
         *   IC_[FS]S_SCL_HCNT + (1+4+3) >= IC_CLK * tHIGH
         *
         * This is based on the DW manuals, and represents an ideal
         * configuration.  The resulting I2C bus speed will be
         * faster than any of the others.
         *
         * If your hardware is free from tHD;STA issue, try this one.
         */
        return (ic_clk * tSYMBOL + 500000) / 1000000 - 8 + offset;
    else
        /*
         * Conditional expression:
         *
         *   IC_[FS]S_SCL_HCNT + 3 >= IC_CLK * (tHD;STA + tf)
         *
         * This is just experimental rule; the tHD;STA period turned
         * out to be proportinal to (_HCNT + 3).  With this setting,
         * we could meet both tHIGH and tHD;STA timing specs.
         *
         * If unsure, you'd better to take this alternative.
         *
         * The reason why we need to take into account "tf" here,
         * is the same as described in i2c_dw_scl_lcnt().
         */
        return (ic_clk * (tSYMBOL + tf) + 500000) / 1000000 - 3 + offset;
}

static uint32_t dw_i2c_scl_lcnt(uint32_t ic_clk, uint32_t tLOW, uint32_t tf,
                                int offset)
{
    /*
     * Conditional expression:
     *
     *   IC_[FS]S_SCL_LCNT + 1 >= IC_CLK * (tLOW + tf)
     *
     * DW I2C core starts counting the SCL CNTs for the LOW period
     * of the SCL clock (tLOW) as soon as it pulls the SCL line.
     * In order to meet the tLOW timing spec, we need to take into
     * account the fall time of SCL signal (tf).  Default tf value
     * should be 0.3 us, for safety.
     */
    return ((ic_clk * (tLOW + tf) + 500000) / 1000000) - 1 + offset;
}

static int dw_i2c_set_sda_hold(i2c_adap_dev_t *adap)
{
    int tmp;
    int lcnt;
    dw_i2c_context *cxt = adap->priv;

    /* Keep previous hold time setting if no one set it */
    //cxt->sda_hold_time = readl(adap->base + DW_IC_SDA_HOLD);

    lcnt = adap->speed == I2C_SSPEED ? cxt->ss_lcnt : cxt->fs_lcnt;
    tmp = min(lcnt / 2, (lcnt - 2));
    cxt->sda_hold_time = (tmp << DW_IC_SDA_HOLD_RX_SHIFT) + tmp;

    /*
     * Workaround for avoiding TX arbitration lost in case I2C
     * slave pulls SDA down "too quickly" after falling edge of
     * SCL by enabling non-zero SDA RX hold. Specification says it
     * extends incoming SDA low to high transition while SCL is
     * high but it appears to help also above issue.
     */
    if (!(cxt->sda_hold_time & 0xff0000))
        cxt->sda_hold_time |= 1 << DW_IC_SDA_HOLD_RX_SHIFT;

    writel(cxt->sda_hold_time, adap->base + DW_IC_SDA_HOLD);

    KLOG_I("i2c%d-adap, SDA Hold Time %#x", adap->id,
                cxt->sda_hold_time);
    return 0;
}

static int dw_i2c_set_timeout(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    cxt->scl_timeout = adap->clk * DW_I2C_STUCK_TIMEOUT;
    cxt->sda_timeout = adap->clk * DW_I2C_STUCK_TIMEOUT;
    writel(cxt->scl_timeout, adap->base + DW_IC_SCL_STUCK_TIMEOUT);
    writel(cxt->sda_timeout, adap->base + DW_IC_SDA_STUCK_TIMEOUT);

    return 0;
}

static int dw_i2c_set_timings(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    uint32_t sda_falling_time, scl_falling_time;
    uint32_t ic_clk_khz;

    /* Set standard and fast speed dividers for high/low periods */
    sda_falling_time = 300; /* ns */
    scl_falling_time = 300; /* ns */
    ic_clk_khz = adap->clk / 1000;

    if (adap->speed == I2C_SSPEED) {
        cxt->ss_hcnt = dw_i2c_scl_hcnt(ic_clk_khz,
                                       4000, /* tHD;STA = tHIGH = 4.0 us */
                                       sda_falling_time,
                                       0,    /* 0: DW default, 1: Ideal */
                                       0);   /* No offset */
        cxt->ss_lcnt = dw_i2c_scl_lcnt(ic_clk_khz,
                                       4700, /* tLOW = 4.7 us */
                                       scl_falling_time,
                                       0);   /* No offset */
        KLOG_I("i2c%d-adap, Standard Mode HCNT:LCNT = %d:%d",
                    adap->id, cxt->ss_hcnt, cxt->ss_lcnt);
    }
    else if (adap->speed == I2C_FSPEED) {
        cxt->fs_hcnt = dw_i2c_scl_hcnt(ic_clk_khz, 600, sda_falling_time, 0, 0);
        cxt->fs_lcnt = dw_i2c_scl_lcnt(ic_clk_khz, 1300, scl_falling_time, 0);

        KLOG_I("i2c%d-adap, Fast Mode HCNT:LCNT = %d:%d",
                    adap->id, cxt->fs_hcnt, cxt->fs_lcnt);
    }
    else if (adap->speed == I2C_PSPEED) {
        cxt->fs_hcnt = dw_i2c_scl_hcnt(ic_clk_khz, 260, sda_falling_time, 0, 0);
        cxt->fs_lcnt = dw_i2c_scl_lcnt(ic_clk_khz, 500, scl_falling_time, 0);

        KLOG_I("i2c%d-adap, Fast Mode Plus HCNT:LCNT = %d:%d",
                    adap->id, cxt->fs_hcnt, cxt->fs_lcnt);
    }
    else {
        KLOG_E("i2c%d-adap, unsupport i2c speed mode error",
                    adap->id);
        return -1;
    }

    if (adap->speed == I2C_SSPEED) {
        writel(cxt->ss_hcnt, adap->base + DW_IC_SS_SCL_HCNT);
        writel(cxt->ss_lcnt, adap->base + DW_IC_SS_SCL_LCNT);
    }
    else {
        writel(cxt->fs_hcnt, adap->base + DW_IC_FS_SCL_HCNT);
        writel(cxt->fs_lcnt, adap->base + DW_IC_FS_SCL_LCNT);
    }

    dw_i2c_set_sda_hold(adap);
    dw_i2c_set_timeout(adap);
    return 0;
}

static void dw_i2c_set_fifo_size(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;

    /*
     * Try to detect the FIFO depth if not set by interface driver,
     * the depth could be from 2 to 256 from HW spec.
     */
    uint32_t param = readl(adap->base + DW_IC_COMP_PARAM_1);
    cxt->tx_fifo_depth = ((param >> 16) & 0xff) + 1;
    cxt->rx_fifo_depth = ((param >> 8)  & 0xff) + 1;

    /* Configure Tx/Rx FIFO threshold levels */
    writel(cxt->tx_fifo_depth / 2, adap->base + DW_IC_TX_TL);
    writel(0, adap->base + DW_IC_RX_TL);
}

static void dw_i2c_cfg_master(i2c_adap_dev_t *adap)
{
    dw_i2c_context *cxt = adap->priv;
    cxt->master_cfg = DW_IC_CON_MASTER | DW_IC_CON_SLAVE_DISABLE |
                      DW_IC_CON_RESTART_EN | DW_IC_CON_BUS_CLEAR_FEATURE_CTRL;

    if (adap->speed == I2C_SSPEED)
        cxt->master_cfg |= DW_IC_CON_SPEED_STD;
    else
        cxt->master_cfg |= DW_IC_CON_SPEED_FAST;

    writel(cxt->master_cfg, adap->base + DW_IC_CON);
}

static int dw_i2c_master_init(i2c_adap_dev_t *adap)
{
    int ret;
    dw_i2c_context *cxt = adap->priv;

    dw_i2c_disable(adap);
    dw_i2c_disable_int(adap->base);

    ret = dw_i2c_set_timings(adap);

    if (ret)
        return ret;

    dw_i2c_set_fifo_size(adap);
    dw_i2c_cfg_master(adap);

    cxt->event = TTOS_CreateEvent();

    return 0;
}

static int semidrv_i2c_probe (struct device *dev)
{
    struct dw_i2c_priv_s  *priv       = NULL;
    struct i2c_bus_device *bus_device = NULL;
    i2c_adap_dev_t *adap = NULL;
    int ret = 0;
    priv = calloc (1, sizeof (struct dw_i2c_priv_s));
    if (!priv)
    {
        return -ENOMEM;
    }

    bus_device         = &priv->bus_device;
    bus_device->parent = dev;
    bus_device->ops    = &semidrv_i2c_host_ops;

    if (platform_get_resource_regs (dev, &bus_device->mmio, 4))
    { 
        free (priv);
        return -ENOMEM;
    }

    KLOG_I ("i2c paddr: 0x%llx, vaddr: 0x%llx, size: 0x%x",
            bus_device->mmio.paddr, bus_device->mmio.vaddr,
            bus_device->mmio.size);

	bus_device->irq  = ttos_pic_irq_alloc (dev, 0);
    bus_device->priv = priv;
	adap = &(priv->i2c_adap);
    adap->base = bus_device->mmio.vaddr;
    adap->irq = bus_device->irq;
    adap->clk = SDRV_I2C_CLK;	/* 默认时钟 */
    adap->enable = true;
    adap->use_7bit_addr = true; /* 默认使用7位地址 */
    adap->opmode = I2C_MASTER; /*配置master模式*/
    adap->speed = I2C_SSPEED;
    adap->retry = DW_I2C_RETRY;    
    adap->priv=&adap->i2c_cxt;

	ret = dw_i2c_master_init(adap);
    
    if(!USE_POLLING) 
    {
        ttos_pic_irq_install(adap->irq, dw_i2c_irq_handler, adap, 0, "i2c irq handle");
        ttos_pic_irq_unmask(adap->irq);
    }

    ret = TTOS_CreateMutex (1, 0, &priv->lock);
    if (TTOS_OK != ret)
    {
        KLOG_E ("I2C Mutex Create error %d", ret);
        return -1;
    }

    i2c_bus_register (bus_device);
 
    return 0;
}

static struct of_device_id i2c_table[] = {
    { .compatible = "snps,designware-i2c" },
    { /* end of list */ },
};

static struct driver i2c_init = { 
    .name = "i2c",
    .probe = semidrv_i2c_probe,
    .match_table = &i2c_table[0],
};

static int semidrive_i2c_init (void)
{
    platform_add_driver (&i2c_init);
}

INIT_EXPORT_DRIVER (semidrive_i2c_init, "semidrive i2c init");
