#include <driver/of.h>
#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/i2c/i2c.h>
#include <fcntl.h>
#include <fs/ioctl.h>
#include <ttos_init.h>
#include <errno.h>
#include <stdio.h>
#include <fs/fs.h>
#include <inttypes.h>

#undef  KLOG_TAG
#define KLOG_TAG    "i2c-switch"
#include <klog.h>
#include <time.h>

#define MAX_PORT_NUM 5

#define CHIP_NUM_REG 0x1300
#define CHIP_VER_REG 0x1301
#define SF2507_REG_PORT_STATUS_BASE 0x1352
#define SF2507_REG_PORT_STATUS(portNum) SF2507_REG_PORT_STATUS_BASE + (portNum) * 0x0001
#define SF2507_REG_PORT_STATUS_LINKSTATE_MASK 1 << 4
#define SF2507_REG_PORT_STATUS_SPEED_MASK 3 << 0
#define SF2507_REG_PORT_STATUS_DUPLEX_MASK 1 << 2

#define SF2507_REG_INDRECT_ACCESS_CTRL 0x1f00
#define SF2507_RW_OFFSET 1
#define SF2507_RW_MASK 0x2
#define SF2507_CMD_OFFSET 0
#define SF2507_CMD_MASK 0x1

#define PHY_EXT_REG0(portNum) 0x0 + (portNum) * 0X0020
#define PHY_EXT_REG0_SPEED_MASK1 1 << 6
#define PHY_EXT_REG0_SPEED_MASK2 1 << 13
#define PHY_EXT_REG0_SPEED_EXTRACT(regData) ((regData & PHY_EXT_REG0_SPEED_MASK1) >> 5) | ((regData & PHY_EXT_REG0_SPEED_MASK2) >> 13)
#define PHY_EXT_REG1(portNum) 0x1 + (portNum) * 0X0020
#define PHY_EXT_REG1_LINKSTATE_MASK 1 << 2
#define PHY_EXT_REG1_AUTO_NEGO_MASK 1 << 12

#define SF2507_REG_INDRECT_ACCESS_STATUS 0x1f01
#define SF2507_INDRECT_ACCESS_STATUS_OFFSET 2
#define SF2507_INDRECT_ACCESS_STATUS_MASK 0x7
#define SF2507_REG_INDRECT_ACCESS_ADDRESS 0x1f02
#define SF2507_REG_INDRECT_ACCESS_WRITE_DATA 0x1f03
#define SF2507_REG_INDRECT_ACCESS_READ_DATA 0x1f04

struct switch_priv_s
{
    struct i2c_device *dev;
    uint32_t addr;
    uint32_t offset;
    uint32_t instance_id;
};

static int fswitch_open(struct file *filep);
static int fswitch_close(struct file *filep);
static ssize_t fswitch_read(struct file *filep, char *buffer, size_t buflen);
static ssize_t fswitch_write(struct file *filep, const char *buffer, size_t buflen);
static int fswitch_ioctl(struct file *filep, unsigned int cmd, unsigned long arg);
static off_t fswitch_lseek(struct file *filep, off_t offset, int whence);

static const struct file_operations fops =
{
    .open = fswitch_open,
    .close = fswitch_close,
    .read = fswitch_read,
    .write = fswitch_write,
    .ioctl = fswitch_ioctl,
    .seek = fswitch_lseek,
};

static struct switch_priv_s *priv;

static int sf2507_init(struct file *filep)
{
    uint8_t buf[2];
    struct i2c_device *dev = filep->f_inode->i_private;
    uint8_t txBuf[2] = {0};
    short chipNumDat, chipVerDat;

    if (NULL == filep)
    {
        return -(EPERM);
    }

    txBuf[0] = (CHIP_NUM_REG >> 8) & 0xFF;
    txBuf[1] = CHIP_NUM_REG & 0xFF;
    i2c_write_read(dev, dev->addr, txBuf, 2, &buf, sizeof(buf));
    chipNumDat = (buf[0] << 8) | buf[1];

    txBuf[0] = (CHIP_VER_REG>> 8) & 0xFF;
    txBuf[1] = CHIP_VER_REG & 0xFF;
    i2c_write_read(dev, dev->addr, txBuf, 2, &buf, sizeof(buf));
    chipVerDat = (buf[0] << 8) | buf[1];
        
    if (0x1619 != chipNumDat || 0x2507 != chipVerDat) 
    {
        KLOG_E("Device chipNum and chipVer mismatch, expected 0x1619 0x2507, got 0x%x 0x%x", chipNumDat,chipVerDat);
        return -ENODEV;
    }

    return 0;
}

static int fswitch_open(struct file *filep) {
    struct i2c_device *dev = filep->f_inode->i_private;
    struct switch_priv_s *priv = dev->parent.priv;
    UNUSED_ARG(priv);
    KLOG_I("SWITCH %d opened", priv->instance_id);

    return sf2507_init(filep);
}

static int fswitch_close(struct file *filep) {
    struct i2c_device *dev = filep->f_inode->i_private;
    struct switch_priv_s *priv = dev->parent.priv;
    UNUSED_ARG(priv);
    KLOG_I("SWITCH %d closed", priv->instance_id);

    return 0;
}

static ssize_t fswitch_read(struct file *filep, char *buffer, size_t buflen) {
    struct i2c_device *dev = filep->f_inode->i_private;
    struct switch_priv_s *priv = dev->parent.priv;
    int ret;

    ret = i2c_read(dev, buffer, buflen, dev->addr);   
    if (ret != 0)
    {
        KLOG_E("i2c write failed");
        return -1;
    }

    UNUSED_ARG(priv);
    KLOG_I("SWITCH %d read", priv->instance_id);
    return buflen;
}

static ssize_t fswitch_write(struct file *filep, const char *buffer, size_t buflen) {

    struct i2c_device *dev = NULL;
    struct switch_priv_s *priv = NULL;
    int ret;
    char cmdBuf[2];

    if (NULL == filep)
    {
        return -(EINVAL);
    }

    dev = filep->f_inode->i_private;
    if (NULL == dev)
    {
        return -(EINVAL);
    }
    priv = dev->parent.priv;
    UNUSED_ARG(priv);
    ret = i2c_write(dev, buffer, buflen, dev->addr);

    if (ret != 0)
    {
        KLOG_E("i2c write failed");
        return -1;
    }
    
    KLOG_I("SWITCH %d write", priv->instance_id);
    return buflen;
}


static int fswitch_ioctl(struct file *filep, unsigned int cmd, unsigned long arg) {
    struct i2c_device *dev = filep->f_inode->i_private;
    struct switch_priv_s *priv = dev->parent.priv;
    int ret = 0;

    UNUSED_ARG(priv);
    KLOG_I("SWITCH %d ioctl", priv->instance_id);

    return ret;
}

static off_t fswitch_lseek(struct file *filep, off_t offset, int whence) {
    struct i2c_device *dev = filep->f_inode->i_private;
    struct switch_priv_s *priv = dev->parent.priv;

    off_t new_offset;

    priv->offset = new_offset;
    KLOG_I("SWITCH %d lseek to %"PRId64, priv->instance_id, new_offset);

    return new_offset;
}

static int i2c_switch_driver_probe(struct device *dev)
{
    struct switch_priv_s *priv;                                                                                       
    struct i2c_device *i2cdev = (struct i2c_device *)(dev);
    
    int ret;

    priv = (struct switch_priv_s *)calloc(1, sizeof(struct switch_priv_s));
    if (priv == NULL) {
        KLOG_E("No enough memory");
        return -ENOMEM;
    }

    priv->dev = i2cdev;
    priv->dev->parent.priv = priv;
    priv->offset = 0;

    ret = of_property_read_u32_array (dev->of_node, "reg", &priv->addr, 1);
    if (ret) {
        goto errout;
    }

    i2cdev->addr = priv->addr & 0xffff;

    KLOG_I("%s dev: %s  parent bus: %s", __func__, dev->driver->name, dev->bus->name);
    KLOG_I("i2c dev addr: 0x%x", i2cdev->addr);

    i2c_device_bind_path(dev, &fops, 0666);
    return 0;

errout:
    free(priv);
    return -1;
}

static struct of_device_id i2c_switch_table[] = {
     {.compatible = "sf2507,switch"},
    { /* end of list */ },
};

static struct driver i2c_switch_driver = {
    .name        = "switch",
    .probe       = i2c_switch_driver_probe,
    .match_table = &i2c_switch_table[0],
};

static int i2c_switch_init(void)
{
    return i2c_add_driver(&i2c_switch_driver);
}
INIT_EXPORT_SERVE_FS(i2c_switch_init, "i2c switch driver init");

