#include <bits/ioctl.h>
#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/gpio/gpio.h>
#include <driver/of.h>
#include <errno.h>
#include <fs/fs.h>
#include <inttypes.h>
#include <io.h>
#include <stdio.h>
#include <system/bitops.h>
#include <ttos_init.h>
#include <ttos_pic.h>

#undef KLOG_TAG
#define KLOG_TAG "GPIO_DWAPB"
#include <klog.h>

/* Default Number of Poll Waiters is 1 */
#ifndef CONFIG_GPIO_DWAPB_NPOLLWAITERS
#define CONFIG_GPIO_DWAPB_NPOLLWAITERS 1
#endif

#define GPIO_SWPORT_DR(p) (0x00 + (p)*0xc)
#define GPIO_SWPORT_DDR(p) (0x04 + (p)*0xc)
#define GPIO_INTEN 0x30
#define GPIO_INTMASK 0x34
#define GPIO_INTTYPE_LEVEL 0x38
#define GPIO_INT_POLARITY 0x3c
#define GPIO_INTSTATUS 0x40
#define GPIO_PORTA_DEBOUNCE 0x48
#define GPIO_PORTA_EOI 0x4c
#define GPIO_EXT_PORT(p) (0x50 + (p)*4)

#define DWAPB_MAX_PORTS 4
#define DWAPB_MAX_GPIOS 32

#define GPIOF_INPUT 0
#define GPIOF_OUTPUT 1

struct dwapb_bank_priv
{
    const char *name;
    uint32_t pins;
    struct device *dev;
};

struct gpio_dwapb_priv
{
    struct dwapb_bank_priv *bank;
    uint32_t nbanks;
    void *base;
    int irq;
};

enum
{
    IRQ_TYPE_NONE = 0x00000000,
    IRQ_TYPE_EDGE_RISING = 0x00000001,
    IRQ_TYPE_EDGE_FALLING = 0x00000002,
    IRQ_TYPE_EDGE_BOTH = (IRQ_TYPE_EDGE_FALLING | IRQ_TYPE_EDGE_RISING),
    IRQ_TYPE_LEVEL_HIGH = 0x00000004,
    IRQ_TYPE_LEVEL_LOW = 0x00000008,
};

static void dwapb_gpioid_to_pin(struct gpio_dwapb_priv *priv, uint32_t gpio_id, uint32_t *bank,
                                uint32_t *pin)
{
    size_t i;
    for (i = 0; i < DWAPB_MAX_PORTS; i++)
    {
        if (gpio_id > priv->bank[i].pins)
            gpio_id = gpio_id - priv->bank[i].pins;
        else
            break;
    }
    *pin = gpio_id;
    *bank = i;
}

static int dwapb_gpio_get_function(struct gpio_dwapb_priv *priv, uint32_t bank, uint32_t pin)
{
    uint32_t gpio;

    gpio = readl(priv->base + GPIO_SWPORT_DDR(bank));

    if (gpio & BIT(pin))
        return GPIOF_OUTPUT;
    else
        return GPIOF_INPUT;
}

static int dwapb_irq_set_type(struct gpio_dwapb_priv *priv, uint32_t type, uint32_t bank,
                              uint32_t pin)
{
    unsigned long level, polarity, flags;

    if (bank != 0)
    {
        /* 只有bank0能产生中断 */
        KLOG_W("ERROR, Invalid bank");
        return -1;
    }

    level = readl(priv->base + GPIO_INTTYPE_LEVEL);
    polarity = readl(priv->base + GPIO_INT_POLARITY);

    switch (type)
    {
    case IRQ_TYPE_EDGE_RISING:
        level |= BIT(pin);
        polarity |= BIT(pin);
        break;
    case IRQ_TYPE_EDGE_FALLING:
        level |= BIT(pin);
        polarity &= ~BIT(pin);
        break;
    case IRQ_TYPE_LEVEL_HIGH:
        level &= ~BIT(pin);
        polarity |= BIT(pin);
        break;
    case IRQ_TYPE_LEVEL_LOW:
        level &= ~BIT(pin);
        polarity &= ~BIT(pin);
        break;
    }

    writel(level, priv->base + GPIO_INTTYPE_LEVEL);
    writel(polarity, priv->base + GPIO_INT_POLARITY);

    return 0;
}

static void dwapb_gpio_int_handle(uint32_t irq, void *param)
{
    struct gpio_desc *desc = (struct gpio_desc *)param;
    struct gpio_dwapb_priv *priv = (struct gpio_dwapb_priv *)desc->parent->priv;
    // unsigned long irq_status = readl(priv->base + GPIO_INTSTATUS);
    uint32_t pin;
    uint32_t bank;

    dwapb_gpioid_to_pin(priv, desc->gpio_id, &bank, &pin);

    KLOG_D("GPIO bank%d pin%d, with irq%d\n", bank, pin, irq);

    if (desc->int_handle)
    {
        desc->int_handle(desc);
    }

    writel(BIT(pin), priv->base + GPIO_PORTA_EOI);
}

static void dwapb_gpio_dt_init(struct gpio_desc *desc) {}

static int dwapb_gpio_read(struct gpio_desc *desc)
{
    struct gpio_dwapb_priv *priv;
    uint32_t pin, bank, value;

    priv = (struct gpio_dwapb_priv *)desc->parent->priv;

    dwapb_gpioid_to_pin(priv, desc->gpio_id, &bank, &pin);

    if (dwapb_gpio_get_function(priv, bank, pin) == GPIOF_OUTPUT)
        value = readl(priv->base + GPIO_SWPORT_DR(bank));
    else
        value = readl(priv->base + GPIO_EXT_PORT(bank));

    return !!(value & BIT(pin));
}

static int dwapb_gpio_write(struct gpio_desc *desc, uint32_t state)
{
    struct gpio_dwapb_priv *priv;
    uint32_t pin, bank;

    priv = (struct gpio_dwapb_priv *)desc->parent->priv;

    dwapb_gpioid_to_pin(priv, desc->gpio_id, &bank, &pin);

    if (state)
        setbits_le32(priv->base + GPIO_SWPORT_DR(bank), 1 << pin);
    else
        clrbits_le32(priv->base + GPIO_SWPORT_DR(bank), 1 << pin);

    return 0;
}

static int dwapb_gpio_mode(struct gpio_desc *desc, uint32_t mode)
{
    struct gpio_dwapb_priv *priv;
    uint32_t pin, bank;

    priv = (struct gpio_dwapb_priv *)desc->parent->priv;

    dwapb_gpioid_to_pin(priv, desc->gpio_id, &bank, &pin);

    switch (mode)
    {
    case GPIO_MODE_INPUT:
        clrbits_le32(priv->base + GPIO_SWPORT_DDR(bank), 1 << pin);
        break;
    case GPIO_MODE_OUTPUT:
        setbits_le32(priv->base + GPIO_SWPORT_DDR(bank), 1 << pin);
        break;
    case GPIO_MODE_INT_EDGE:
        dwapb_irq_set_type(priv, IRQ_TYPE_EDGE_RISING, bank, pin);
        break;
    case GPIO_MODE_INT_LEVEL:
        dwapb_irq_set_type(priv, IRQ_TYPE_LEVEL_HIGH, bank, pin);
        break;
    default:
        KLOG_W("ERROR, Invalid value");
        break;
    }
    return 0;
}

static int dwapb_gpio_int_set(struct gpio_desc *desc, uint32_t state)
{
    struct gpio_dwapb_priv *priv;
    uint32_t pin, bank, value;

    if (desc == NULL)
    {
        KLOG_E("%s %s\n", __func__, "ERROR, Invalid parameter");
        return -1;
    }

    priv = (struct gpio_dwapb_priv *)desc->parent->priv;

    dwapb_gpioid_to_pin(priv, desc->gpio_id, &bank, &pin);

    if (bank != 0)
    {
        /* 只有bank0能产生中断 */
        KLOG_W("ERROR, Invalid bank");
        return 0;
    }

    if (state == GPIO_INT_ENABLE)
    {
        dwapb_irq_set_type(priv, IRQ_TYPE_EDGE_RISING, 0, pin);

        ttos_pic_irq_install(priv->irq, dwapb_gpio_int_handle, desc, 0, "gpio");

        ttos_pic_irq_unmask(priv->irq);

        value = readl(priv->base + GPIO_INTEN);
        value |= BIT(pin);
        writel(value, priv->base + GPIO_INTEN);

        value = readl(priv->base + GPIO_INTMASK);
        value &= ~BIT(pin);
        writel(value, priv->base + GPIO_INTMASK);
    }
    else
    {
        value = readl(priv->base + GPIO_INTEN);
        value &= ~BIT(pin);
        writel(value, priv->base + GPIO_INTEN);

        value = readl(priv->base + GPIO_INTMASK);
        value |= BIT(pin);
        writel(value, priv->base + GPIO_INTMASK);

        ttos_pic_irq_mask(priv->irq);
    }

    return 0;
}

static struct gpio_obj_ops g_gpio_obj_ops = {
    .dt_init = dwapb_gpio_dt_init,
    .read = dwapb_gpio_read,
    .write = dwapb_gpio_write,
    .mode = dwapb_gpio_mode,
    .int_set = dwapb_gpio_int_set,
};

static int dwapb_gpio_get_dtb(struct device *dev, struct gpio_dwapb_priv *priv)
{
    struct dwapb_bank_priv *bank;
    struct device_node *child;
    int i = 0;
    int ret = 0;

    for_each_child_of_node(dev->of_node, child)
    {
        priv->nbanks++;
    }

    priv->bank = calloc(1, sizeof(priv->bank) * priv->nbanks);
    if (!priv->bank)
    {
        return -ENOMEM;
    }

    for_each_child_of_node(dev->of_node, child)
    {
        if (gpio_controller_check(child->device))
            continue;

        bank = &priv->bank[i];
        bank->dev = child->device;
        if (of_property_read_string(child, "bank-name", &bank->name))
        {
            if (!bank->name)
            {
                ret = -ENOMEM;
                goto errout;
            }
        }

        if (of_property_read_u32(child, "ngpios", &bank->pins) &&
            of_property_read_u32(child, "snps,nr-gpios", &bank->pins))
        {
            KLOG_E("failed to get number of gpios for %s", bank->name);
            bank->pins = DWAPB_MAX_GPIOS;
        }

        if (i == 0)
            priv->irq = ttos_pic_irq_alloc(bank->dev, 0);

        KLOG_D("%s pins:%d irq:%d", bank->name, bank->pins, priv->irq);

        i++;
    }

    return ret;

errout:
    free(priv->bank);
    return ret;
}

static int dwapb_gpio_probe(struct device *dev)
{
    struct gpio_obj *gpio;
    struct gpio_dwapb_priv *priv;
    int ret = 0;
    KLOG_I("gpio_probe %s", dev->name);

    gpio = calloc(1, sizeof(struct gpio_obj));
    if (gpio == NULL)
    {
        KLOG_E("No enough memory\n");
        return -ENOMEM;
    }

    priv = calloc(1, sizeof(*priv));
    if (!priv)
    {
        ret = -ENOMEM;
        goto errout;
    }

    ret = dwapb_gpio_get_dtb(dev, priv);
    if (ret != 0)
    {
        goto errout;
    }

    if (platform_get_resource_reg(dev, &gpio->mmio.paddr, &gpio->mmio.size))
    {
        ret = -ENODEV;
        goto errout;
    }

    KLOG_I("reg: 0x%" PRIx64 ", size: 0x%" PRIxPTR, gpio->mmio.paddr, gpio->mmio.size);

    for (size_t i = 0; i < priv->nbanks; i++)
    {
        gpio->ngpio += priv->bank[i].pins;
        priv->bank[i].dev->priv = gpio;
    }

    gpio->priv = priv;
    gpio->parent = dev;
    gpio->ops = &g_gpio_obj_ops;
    gpio->start_no = 0;
    gpio_controller_register(gpio);
    priv->base = (void *)gpio->mmio.vaddr;

    return ret;
errout1:
    free(priv);
errout:
    free(gpio);
    return ret;
}

static struct of_device_id dwapb_of_match[] = {
    {.compatible = "snps,dw-apb-gpio"},
    {/* end of list */},
};

static struct driver dwapb_gpio_driver = {
    .name = "gpio-dwapb",
    .probe = dwapb_gpio_probe,
    .match_table = dwapb_of_match,
};

static int __dwapb_gpio_init(void)
{
    platform_add_driver(&dwapb_gpio_driver);
}

INIT_EXPORT_PRE_DRIVER(__dwapb_gpio_init, "dwapb gpio driver init");
