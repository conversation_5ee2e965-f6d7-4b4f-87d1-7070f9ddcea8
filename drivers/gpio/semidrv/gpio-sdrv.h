#ifndef __GPIO_SDRV_H__
#define __GPIO_SDRV_H__
#include <driver/gpio/gpio.h>

#define GPIO_NAME_MAX           256
#define SDRV_GPIO_NPORTS        5
#define MAX_SDRV_GPIO_CON       6
#define SDRV_NPORT_GPIO_PIN     32
#define SDRV_MAX_GPIO_PIN       155

#define GPIO_IN          0
#define GPIO_OUT         1
#define GPIO_DIRECTION_INPUT  0
#define GPIO_DIRECTION_OUTPUT 1

#define GPIO_DIR_PORT_1		0x2000
#define GPIO_DIR_PORT_2		0x2010
#define GPIO_DIR_PORT_SIZE \
	(GPIO_DIR_PORT_2 - GPIO_DIR_PORT_1)
#define GPIO_DIR_PORT_X(n) \
	(GPIO_DIR_PORT_1 + ((n) * GPIO_DIR_PORT_SIZE))

#define GPIO_DATA_IN_PORT_1		0x2200
#define GPIO_DATA_IN_PORT_2		0x2210
#define GPIO_DATA_IN_PORT_SIZE \
	(GPIO_DATA_IN_PORT_2 - GPIO_DATA_IN_PORT_1)
#define GPIO_DATA_IN_PORT_X(n) \
	(GPIO_DATA_IN_PORT_1 + ((n) * GPIO_DATA_IN_PORT_SIZE))

#define GPIO_DATA_OUT_PORT_1		0x2400
#define GPIO_DATA_OUT_PORT_2		0x2410
#define GPIO_DATA_OUT_PORT_SIZE \
	(GPIO_DATA_OUT_PORT_2 - GPIO_DATA_OUT_PORT_1)
#define GPIO_DATA_OUT_PORT_X(n) \
	(GPIO_DATA_OUT_PORT_1 + ((n) * GPIO_DATA_OUT_PORT_SIZE))

#define GPIO_INT_EN_PORT_1		0x2600
#define GPIO_INT_EN_PORT_2		0x2610
#define GPIO_INT_EN_PORT_SIZE \
	(GPIO_INT_EN_PORT_2 - GPIO_INT_EN_PORT_1)
#define GPIO_INT_EN_PORT_X(n) \
	(GPIO_INT_EN_PORT_1 + ((n) * GPIO_INT_EN_PORT_SIZE))

#define GPIO_INT_MASK_PORT_1		0x2800
#define GPIO_INT_MASK_PORT_2		0x2810
#define GPIO_INT_MASK_PORT_SIZE \
	(GPIO_INT_MASK_PORT_2 - GPIO_INT_MASK_PORT_1)
#define GPIO_INT_MASK_PORT_X(n) \
	(GPIO_INT_MASK_PORT_1 + ((n) * GPIO_INT_MASK_PORT_SIZE))

#define GPIO_INT_TYPE_PORT_1		0x2a00
#define GPIO_INT_TYPE_PORT_2		0x2a10
#define GPIO_INT_TYPE_PORT_SIZE \
	(GPIO_INT_TYPE_PORT_2 - GPIO_INT_TYPE_PORT_1)
#define GPIO_INT_TYPE_PORT_X(n) \
	(GPIO_INT_TYPE_PORT_1 + ((n) * GPIO_INT_TYPE_PORT_SIZE))

#define GPIO_INT_POL_PORT_1		0x2c00
#define GPIO_INT_POL_PORT_2		0x2c10
#define GPIO_INT_POL_PORT_SIZE \
	(GPIO_INT_POL_PORT_2 - GPIO_INT_POL_PORT_1)
#define GPIO_INT_POL_PORT_X(n) \
	(GPIO_INT_POL_PORT_1 + ((n) * GPIO_INT_POL_PORT_SIZE))

#define GPIO_INT_BOTH_EDGE_PORT_1		0x2e00
#define GPIO_INT_BOTH_EDGE_PORT_2		0x2e10
#define GPIO_INT_BOTH_EDGE_PORT_SIZE \
	(GPIO_INT_BOTH_EDGE_PORT_2 - GPIO_INT_BOTH_EDGE_PORT_1)
#define GPIO_INT_BOTH_EDGE_PORT_X(n) \
	(GPIO_INT_BOTH_EDGE_PORT_1 + ((n) * GPIO_INT_BOTH_EDGE_PORT_SIZE))

#define GPIO_INT_STATUS_PORT_1		0x3000
#define GPIO_INT_STATUS_PORT_2		0x3010
#define GPIO_INT_STATUS_PORT_SIZE \
	(GPIO_INT_STATUS_PORT_2 - GPIO_INT_STATUS_PORT_1)
#define GPIO_INT_STATUS_PORT_X(n) \
	(GPIO_INT_STATUS_PORT_1 + ((n) * GPIO_INT_STATUS_PORT_SIZE))

#define GPIO_INT_STATUS_UNMASK_PORT_1		0x3200
#define GPIO_INT_STATUS_UNMASK_PORT_2		0x3210
#define GPIO_INT_STATUS_UNMASK_PORT_SIZE \
	(GPIO_INT_STATUS_UNMASK_PORT_2 - GPIO_INT_STATUS_UNMASK_PORT_1)
#define GPIO_INT_STATUS_UNMASK_PORT_X(n) \
	(GPIO_INT_STATUS_UNMASK_PORT_1 + \
	((n) * GPIO_INT_STATUS_UNMASK_PORT_SIZE))

#define GPIO_INT_EDGE_CLR_PORT_1		0x3400
#define GPIO_INT_EDGE_CLR_PORT_2		0x3410
#define GPIO_INT_EDGE_CLR_PORT_SIZE \
	(GPIO_INT_EDGE_CLR_PORT_2 - GPIO_INT_EDGE_CLR_PORT_1)
#define GPIO_INT_EDGE_CLR_PORT_X(n) \
	(GPIO_INT_EDGE_CLR_PORT_1 + ((n) * GPIO_INT_EDGE_CLR_PORT_SIZE))

#define GPIO_INT_LEV_SYNC_PORT_1		0x3600
#define GPIO_INT_LEV_SYNC_PORT_2		0x3610
#define GPIO_INT_LEV_SYNC_PORT_SIZE \
	(GPIO_INT_LEV_SYNC_PORT_2 - GPIO_INT_LEV_SYNC_PORT_1)
#define GPIO_INT_LEV_SYNC_PORT_X(n) \
	(GPIO_INT_LEV_SYNC_PORT_1 + ((n) * GPIO_INT_LEV_SYNC_PORT_SIZE))

#define GPIO_INT_DEB_EN_PORT_1		0x3800
#define GPIO_INT_DEB_EN_PORT_2		0x3810
#define GPIO_INT_DEB_EN_PORT_SIZE \
	(GPIO_INT_DEB_EN_PORT_2 - GPIO_INT_DEB_EN_PORT_1)
#define GPIO_INT_DEB_EN_PORT_X(n) \
	(GPIO_INT_DEB_EN_PORT_1 + ((n) * GPIO_INT_DEB_EN_PORT_SIZE))

#define GPIO_INT_CLK_SEL_PORT_1		0x3a00
#define GPIO_INT_CLK_SEL_PORT_2		0x3a10
#define GPIO_INT_CLK_SEL_PORT_SIZE \
	(GPIO_INT_CLK_SEL_PORT_2 - GPIO_INT_CLK_SEL_PORT_1)
#define GPIO_INT_CLK_SEL_PORT_X(n) \
	(GPIO_INT_CLK_SEL_PORT_1 + ((n) * GPIO_INT_CLK_SEL_PORT_SIZE))

#define GPIO_INT_PCLK_ACTIVE_PORT_1		0x3c00
#define GPIO_INT_PCLK_ACTIVE_PORT_2		0x3c10
#define GPIO_INT_PCLK_ACTIVE_PORT_SIZE \
	(GPIO_INT_PCLK_ACTIVE_PORT_2 - GPIO_INT_PCLK_ACTIVE_PORT_1)
#define GPIO_INT_PCLK_ACTIVE_PORT_X(n) \
	(GPIO_INT_PCLK_ACTIVE_PORT_1 + ((n) * GPIO_INT_PCLK_ACTIVE_PORT_SIZE))

/* GPIO */
#define FGPIO1_BASE_ADDR (0x30000000U)
#define FGPIO2_BASE_ADDR (0x30400000U)
#define FGPIO3_BASE_ADDR (0x30410000U)
#define FGPIO4_BASE_ADDR (0x30420000U)
#define FGPIO5_BASE_ADDR (0x30430000U)
struct gpio_config_s {
    unsigned int pin;        // GPIO引脚编号
    unsigned int direction;  // 输入或输出方向
    unsigned int value;      // GPIO引脚的值
    unsigned int mux;        // GPIO引脚的复用功能
    unsigned int irqmode;    // GPIO中断模式
};

struct gpioHidpincfg
{
    unsigned int mGpioCe;
    unsigned int mGpioClk;
    unsigned int mGpioData;
    unsigned int mGpioPl;
    unsigned int mdlyCnt;
    unsigned int mShiftNum;
};
struct gpioLedpincfg
{
    unsigned int mDataPin;
    unsigned int mSHPin;
    unsigned int mSTPin;
    unsigned int mOEPin;
    unsigned int mMRPin;
    unsigned int mdlyCnt;
    unsigned int mShiftNum;
};

enum gpio_int_mode {
    GPIO_INT_MODE_DISABLE = 0,  // 禁用中断
    GPIO_INT_MODE_RISING,       // 上升沿触发
    GPIO_INT_MODE_FALLING,      // 下降沿触发
    GPIO_INT_MODE_LOWLEVEL,     // 低电平触发
    GPIO_INT_MODE_HIGHLEVEL,    // 高电平触发
    GPIO_INT_MODE_BOTH,         // 上升沿 + 下降沿都触发（双沿）
    GPIO_INT_MODE_MAX = 0xFF    // 最大值，用于边界检查或无效标记
};

#define GPIO_DIRECTION_IN  0
#define GPIO_DIRECTION_OUT 1
#define GPIO_TYPE_INT 2

#define GPIO_IOC_MAGIC 'G'
#define GPIO_IOC_SET_DIRECTION _IOW(GPIO_IOC_MAGIC, 1, struct gpio_config_s)
#define GPIO_IOC_GET_DIRECTION _IOR(GPIO_IOC_MAGIC, 2, struct gpio_config_s)
#define GPIO_IOC_SET_VALUE _IOW(GPIO_IOC_MAGIC, 3, struct gpio_config_s)
#define GPIO_IOC_GET_VALUE _IOR(GPIO_IOC_MAGIC, 4, struct gpio_config_s)
#define GPIO_IOC_SET_CONFIG _IOW(GPIO_IOC_MAGIC, 5, struct gpio_config_s)
#define GPIO_IOC_GET_CONFIG _IOR(GPIO_IOC_MAGIC, 6, struct gpio_config_s)
#define GPIO_IOC_GET_MUX _IOR(GPIO_IOC_MAGIC, 7, struct gpio_config_s)
#define GPIO_IOC_SET_MUX _IOW(GPIO_IOC_MAGIC, 8, struct gpio_config_s)

#define GPIO_IOC_SET_INT_MODE _IOW(GPIO_IOC_MAGIC, 9, struct gpio_config_s)
#define GPIO_IOC_EN_INT _IOW(GPIO_IOC_MAGIC, 10, struct gpio_config_s)
#define GPIO_IOC_LED_INIT _IOW(GPIO_IOC_MAGIC, 11, struct gpioLedpincfg)
#define GPIO_IOC_LED_OUTPUT _IOW(GPIO_IOC_MAGIC, 12, int)
#define GPIO_IOC_HID_INIT _IOW(GPIO_IOC_MAGIC, 13, struct gpioHidpincfg)
#define GPIO_IOC_HID_INPUT _IOW(GPIO_IOC_MAGIC, 14, int)

static struct gpioHidpincfg gpioHidpincfg_t =
{
    .mGpioCe = 62,
    .mGpioClk = 61,
    .mGpioData = 63,
    .mGpioPl = 60,
    .mdlyCnt = 1,
    .mShiftNum = 32
};
static struct gpioLedpincfg gpioLedpincfg_t=
{
    .mSTPin = 68,
    .mDataPin = 69,
    .mSHPin = 70,
    .mMRPin = 71,
    .mOEPin = 72,
    .mdlyCnt = 1,
    .mShiftNum = 16
};
#endif /* __GPIO_SDRV_H__ */