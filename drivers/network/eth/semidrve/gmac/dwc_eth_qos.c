#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/of.h>
#include <ttos_pic.h>
#include <ttos_init.h>
#include <ttosMM.h>
#include <cache.h>
#include <io.h>
#include <fs/ioctl.h>
#include <completion.h>
#include <net/ethernet_dev.h>
#include <net/phy_dev.h>
#include <errno.h>
#include <net/if.h>
#include "eth_dma.h"
#include "dwc_eth_qos.h"


#include <stdio.h>
#include <errno.h>
#include <kmalloc.h>
#include <inttypes.h>
#include <page.h>

#undef KLOG_LEVEL
#undef KLOG_TAG
#define KLOG_LEVEL  KLOG_DEBUG
#define KLOG_TAG    "GMAC"
#include <klog.h>
#include <fs/fs.h>

#define GMAC_NET_NAME "GMAC"

//#define __DEBUG_GMAC__

struct eth_desc_memanager {
    volatile int32_t desc_num;
    volatile uint32_t index;
    volatile uint32_t total_num;
    unsigned int desc_size;
    unsigned int data_size;
    uint64_t dma_vaddr;
    uint64_t dma_paddr;
    uint64_t data_vaddr;
    uint64_t data_paddr;
};

typedef struct dwgmac {
    ETH_DEV                      ethdev;
    devaddr_region_t            base;
    uint32_t                    irq;
    T_TTOS_CompletionControl    completion;
    struct eth_desc_memanager   tx_desc;
    struct eth_desc_memanager   rx_desc;
    struct dma_chan             *chan[DMA_CHANNEL_NB_MAX];  /* DMA控制器 */
    void                        *priv;
    void                        *netdev_cfg;
    uint64_t                    rx_size;
} *dwgmac_t;


typedef struct gmac_desc
{
    uint32_t des0;
    uint32_t des1;
    uint32_t des2;
    uint32_t des3;
    uint64_t res[6];//DMA最小对齐要求（64 字节)
} gmac_desc_t;

static int gmac_ifup(ETH_DEV *eth);
static int gmac_ifdown(ETH_DEV *eth);
static int gmac_net_ioctl (ETH_DEV *ethdev, int cmd, unsigned long arg);
static int gmac_send(ETH_DEV *eth, ETH_NETPKT *pkt);
static int gmac_receive(ETH_DEV *eth);
static int gmac_get_macaddr (ETH_DEV *ethdev, char *hwaddr);
static int gmac_set_macaddr (ETH_DEV *ethdev, const char *hwaddr);
static int gmac_mdio_read (void *dev, unsigned char phy_addr, unsigned char reg_addr, unsigned short *data_val);
static int gmac_mdio_write (void *dev, unsigned char phy_addr, unsigned char reg_addr, unsigned short data_val);
static int gmac_link_update (ETH_DEV *ethdev);

static struct eth_dev_funcs gmac_net_func =
{
    .ifup         = gmac_ifup,
    .ifdown       = gmac_ifdown,
    .unload       = NULL,
    .ioctl        = gmac_net_ioctl,
    .send         = gmac_send,
    .receive      = gmac_receive,
    .mCastAddrAdd = NULL,
    .mCastAddrDel = NULL,
    .mCastAddrGet = NULL,
    .pollSend     = NULL,
    .pollRcv      = NULL,
    .mac_get      = gmac_get_macaddr,
    .mac_set      = gmac_set_macaddr,
    .link_update  = gmac_link_update
};

static ETH_DRV_PARAM gmac_drv_param[] =
{
    {"mdio_type", ETH_PARAM_INT32, {(void *)clause22}},
    {NULL, ETH_PARAM_END_OF_LIST, {NULL}}
};


static MDIO_FUNCS gmac_mdio_funcs =
{
    .mdio_read = gmac_mdio_read,
    .mdio_write = gmac_mdio_write,
    .mdio45_read = NULL,
    .mdio45_write = NULL
};

static ETH_DRV_INFO gmac_net_drv_info =
{
    {NULL, NULL},
    GMAC_NET_NAME,
    "SemiDrive gmac driver",
    fdt,
    gmac_drv_param,
    0,
    0,
    &gmac_net_func,
    &gmac_mdio_funcs
};

#ifdef  __DEBUG_GMAC__
static dwgmac_t dbg_gmac[2];
#endif

static void gmac_mac_start_tx(dwgmac_t gmac)
{
    setbits_le32(&gmac->chan[0]->tx_control, DMA_CONTROL_ST);
    setbits_le32(gmac->base.vaddr + GMAC_CONFIG, GMAC_CONFIG_TE);
}

static void gmac_mac_stop_tx(dwgmac_t gmac)
{
    clrbits_le32(&gmac->chan[0]->tx_control, DMA_CONTROL_ST);
    clrbits_le32(gmac->base.vaddr + GMAC_CONFIG, GMAC_CONFIG_TE);
}

static void gmac_mac_start_rx(dwgmac_t gmac)
{
    setbits_le32(&gmac->chan[0]->rx_control, DMA_CONTROL_SR);
    setbits_le32(gmac->base.vaddr + GMAC_CONFIG, GMAC_CONFIG_RE);
}

static void gmac_mac_stop_rx(dwgmac_t gmac)
{
    clrbits_le32(&gmac->chan[0]->rx_control, DMA_CONTROL_SR);
    clrbits_le32(gmac->base.vaddr + GMAC_CONFIG, GMAC_CONFIG_RE);
}

static void gmac_mac_interrupt_disable(dwgmac_t gmac, uint32_t state)
{
    clrbits_le32(gmac->base.vaddr + GMAC_INT_EN, state);
}

static volatile gmac_desc_t *eqos_get_desc(dwgmac_t eqos, unsigned int num, bool rx, bool isPhy)
{
    void* desc = NULL;

    if(rx)
    {
        if(isPhy)
        {
            desc = (unsigned char *)eqos->rx_desc.dma_paddr + num * eqos->rx_desc.desc_size;
        }
        else
        {
            desc = (unsigned char *)eqos->rx_desc.dma_vaddr + num * eqos->rx_desc.desc_size;
        }
    }
    else
    {
        if(isPhy)
        {
            desc = (unsigned char *)eqos->tx_desc.dma_paddr + num * eqos->tx_desc.desc_size;
        }
        else
        {
            desc = (unsigned char *)eqos->tx_desc.dma_vaddr + num * eqos->tx_desc.desc_size;
        }
    }

    return (volatile gmac_desc_t *)desc;
}

static unsigned char * eqos_get_dma_buf(dwgmac_t eqos, unsigned int num,bool rx , bool isPhy)
{

    unsigned char* addr = NULL;
    if(rx)
    {
        if(isPhy)
        {
            addr = ((unsigned char *)eqos->rx_desc.data_paddr + num * eqos->rx_desc.data_size);
        }
        else
        {
            addr = ((unsigned char *)eqos->rx_desc.data_vaddr + num * eqos->rx_desc.data_size);
        }
    }
    else
    {
        if(isPhy)
        {
            addr = ((unsigned char *)eqos->tx_desc.data_paddr + num * eqos->tx_desc.data_size);
        }
        else
        {
            addr = ((unsigned char *)eqos->tx_desc.data_vaddr + num * eqos->tx_desc.data_size);
        }
    }
    return addr;
}




void dwmac_enable_dma_rx_int(dwgmac_t gmac, uint32_t channel, bool enable)
{
    uint32_t val = readl(&gmac->chan[0]->intr_ena);

    val |= DMA_CHAN_INTR_DEFAULT_MASK;
    if (enable)
    {
        val |= DMA_CHAN_INTR_ENA_RIE;
    }
    else
    {
        val &= ~DMA_CHAN_INTR_ENA_RIE;
    }
//    dwc_write_reg(val, regbase + DMA_CHAN_INTR_ENA(channel));
    writel(val, &gmac->chan[0]->intr_ena);
}

uint32_t dwmac_get_mac_int_status(virt_addr_t regbase)
{
    return readl(regbase + MAC_INT_STATUS);
}
uint32_t dwmac_get_mac_phyif_control_status(virt_addr_t regbase)
{
    return readl(regbase + MAC_PHYIF_CONTROL_STATUS);
}

uint32_t dwmac_get_mac_an_status(virt_addr_t regbase)
{
    return readl(regbase + MAC_AN_STATUS);
}

uint32_t dwmac_get_mac_pmt_control_status(virt_addr_t regbase)
{
    return readl(regbase + MAC_PMT_CONTROL_STATUS);
}

uint32_t dwmac_get_mac_lpi_control_status(virt_addr_t regbase)
{
    return readl(regbase + MAC_LPI_CONTROL_STATUS);
}

uint32_t dwmac_get_mtl_int_status(virt_addr_t regbase)
{
    return readl(regbase + MTL_INT_STATUS);
}

uint32_t dwmac_get_mtl_q_int_status(virt_addr_t regbase, uint32_t channel)
{
    return readl(regbase + MTL_CHAN_INT_CTRL(channel));
}

void dwmac_clr_mtl_q_int_status(virt_addr_t regbase, uint32_t channel, uint32_t val)
{
    writel(val, regbase + MTL_CHAN_INT_CTRL(channel));
}

static void gmac_irq_handle(uint32_t irq, void *param)
{
    dwgmac_t gmac = (dwgmac_t) param;
	int status = 0;
	int detail = 0;
	int sub_sts = 0;
	int eth_unit = gmac->ethdev.eth_unit;
	virt_addr_t reg_base = gmac->base.vaddr;
	status = readl(gmac->base.vaddr + 0x1008);

	if(status & 0x7F)
	{
		    clrbits_le32(&gmac->chan[0]->intr_ena, DMA_CHAN_INTR_ENA_RIE);
	    // TTOS_ReleaseCompletion(&gmac->completion);
	    eth_release_rx_sem(&gmac->ethdev);

	    setbits_le32(&gmac->chan[0]->status, 0);
	}


	if (status & (1 << 16)) 
	{
        /* MTL interrupt */
        sub_sts = dwmac_get_mtl_int_status(reg_base);
        if (sub_sts & 0xFFFFFF00) {
            KLOG_D("ETH%d mtl int status 0x%x\n",
                        eth_unit, sub_sts);
            ASSERT(0);
        }
        for (int i = 0; i < ETH_MAX_DMA_CHANNEL; i++) {
            if (sub_sts & (1 << i)) {
                detail = dwmac_get_mtl_q_int_status(reg_base, i);
                dwmac_clr_mtl_q_int_status(reg_base, i, detail);
                if (detail & (1 << 16))
                    KLOG_D( "ETH%d mtl queue %d rx overflow\n",
                                eth_unit, i);
                if (detail & (1 << 1))
                    KLOG_D( "ETH%d mtl queue %d ABS updated\n",
                                eth_unit, i);
                if (detail & (1 << 1))
                    KLOG_D( "ETH%d mtl queue %d tx underflow\n",
                                eth_unit, i);
            }
        }
    }

	if(status & (1<<17))
	{
		for (int i = 0; i < ETH_MAX_DMA_CHANNEL; i++)
		{
	        /* MAC interrupt */
	        sub_sts = dwmac_get_mac_int_status(reg_base);
	        KLOG_D("%s %d mac int status 0x%x\n", __func__, irq, sub_sts);
	        if (sub_sts & (1 << 0)) {
	            /* RGMII/SMII PHY link status changed. Read of MAC_PHYIF_Control_Status
	             * register will clear the interrupt status.
	             */
	            detail = dwmac_get_mac_phyif_control_status(reg_base);
	            if (detail & (1 << 19))
	                KLOG_D("ETH%d phy link up\n", eth_unit);
	            else
	                KLOG_D("ETH%d phy link down\n", eth_unit);
	                
	        }
	        if (sub_sts & (3 << 1)) {
	            /* TBI/RTBI/SGMII PHY link status changed, or TBI/RTBI/SGMII PHY
	             * auto-negotiation is completed. Read of MAC_AN_Status register
	             * will clear the interrupt status.
	             */
	            detail = dwmac_get_mac_an_status(reg_base);
	            KLOG_D( "ETH%d mac an status 0x%x\n",
	                        eth_unit, detail);
	        }
	        if (sub_sts & (1 << 4)) {
	            /* A magic packet or Wake-on-LAN packet is received.
	             * Read of MAC_PMT_Control_Status register will clear
	             * the interrupt status.
	             */
	            detail = dwmac_get_mac_pmt_control_status(reg_base);
	            KLOG_D("ETH%d mac pmt ctrl status 0x%x\n",
	                        eth_unit, detail);
	        }
	        if (sub_sts & (1 << 5)) {
	            /* LPI state entry or exit in the MAC Transmitter or Receiver.
	             * Read of MAC_LPI_Control_Status register will clear the
	             * interrupt status.
	             */
	            detail = dwmac_get_mac_lpi_control_status(reg_base);
	            KLOG_D("ETH%d mac lpi ctrl status 0x%x\n",
	                        eth_unit, detail);
	        }
	    }
	    
	}
    
}

static void gmac_desc_init(struct eth_desc_memanager *desc_mm,
                            uint32_t is_read, uint32_t desc_num)
{
    volatile gmac_desc_t *desc;
    uint32_t size, i;
    uint64_t buf_cache;
    uint64_t buf_phy;


    desc_mm->desc_size = sizeof(gmac_desc_t);  /* 记录描述符大小 */
    desc_mm->data_size = DESC_DATA_SIZE_MAX;
    size = desc_mm->desc_size * desc_num;

    desc_mm->index       = 0;
    desc_mm->total_num   = desc_num;
    desc_mm->data_paddr  = (uint64_t)pages_alloc(page_bits(PAGE_ALIGN(DESC_DATA_SIZE_MAX * desc_num)), ZONE_DMA32);
    desc_mm->data_vaddr  = (uint64_t) page_address(desc_mm->data_paddr);
    desc_mm->dma_paddr   = (uint64_t)pages_alloc(page_bits(PAGE_ALIGN(size)), ZONE_DMA32);
    desc_mm->dma_vaddr   = (uint64_t) page_address(desc_mm->dma_paddr);

    ttosSetPageAttribute(desc_mm->dma_vaddr, PAGE_ALIGN(size), MT_NCACHE | MT_KERNEL);
    /* 清理数据 */
    memset((void*)desc_mm->data_vaddr, 0, PAGE_ALIGN(DESC_DATA_SIZE_MAX * desc_num));

    buf_cache = desc_mm->data_vaddr;
    buf_phy   = desc_mm->data_paddr;

    KLOG_I("%s desc:", is_read ? "RX" : "TX");
    KLOG_I("addr phy     :  0x%" PRIx64 "\n", desc_mm->dma_paddr);
    KLOG_I("addr uncache :  0x%" PRIx64 "\n", desc_mm->dma_vaddr);
    KLOG_I("buf cache    :  0x%" PRIx64 "\n", desc_mm->data_vaddr);
    KLOG_I("buf phy      :  0x%" PRIx64 "\n", desc_mm->data_paddr);

    for (i = 0; i < desc_num; i++)
    {
        desc = (volatile gmac_desc_t *)(desc_mm->dma_vaddr + i * desc_mm->desc_size);
        if (is_read)
        {
            desc->des0        = (uintptr_t) buf_phy;
            desc->des1        = 0;
            desc->des2        = 0;
            desc->des3        = RDES3_OWN | RDES3_INT_ON_COMPLETION_EN |
                                RDES3_BUFFER1_VALID_ADDR;

            buf_phy += DESC_DATA_SIZE_MAX;
        }
        else
        {
            desc->des0 = 0;
            desc->des1 = 0;
            desc->des2 = 0;
            desc->des3 = 0;
        }
    }
}

int32_t dwmac_dma_reset(void  *regbase)
{
    int32_t limit;
    uint32_t value = readl(regbase + DMA_BUS_MODE);

    /* DMA SW reset */
    value |= DMA_BUS_MODE_SFT_RESET;
    writel(value, regbase + DMA_BUS_MODE);
    limit = 50;

    while (limit--) {
        if (!(readl(regbase + DMA_BUS_MODE) & DMA_BUS_MODE_SFT_RESET))
            break;
        usleep(1000);
    }
    printk("gmac_dma_reset limit=%d!!!!!!!!!!\n",50 - limit);
    if (limit <= 0) {
        return -1;
    }

    return 0;
}

static void mac_mtl_dma_register_init(dwgmac_t gmac)
{
    dma_chan_t dma_chan = gmac->chan[0];

    u32 val, tx_fifo_sz, rx_fifo_sz, tqs, rqs, pbl;
	u64 desc_pad;
    /* Enable Store and Forward mode for TX */
	/* Program Tx operating mode */
	setbits_le32(gmac->base.vaddr + MTL_TXQ0_OPERATION_MODE,
		     MTL_TXQ0_OPERATION_MODE_TSF |
		     (MTL_TXQ0_OPERATION_MODE_TXQEN_ENABLED <<
		      MTL_TXQ0_OPERATION_MODE_TXQEN_SHIFT));

	/* Transmit Queue weight */
	//writel(0x10, &eqos->mtl_regs->txq0_quantum_weight);

	/* Enable Store and Forward mode for RX, since no jumbo frame */
	setbits_le32(gmac->base.vaddr + MTL_RXQ0_OPERATION_MODE,
		     MTL_RXQ0_OPERATION_MODE_RSF |
		     MTL_RXQ0_OPERATION_MODE_FEP |
		     MTL_RXQ0_OPERATION_MODE_FUP);

	/* Transmit/Receive queue fifo size; use all RAM for 1 queue */
	val = readl(gmac->base.vaddr + GMAC_HW_FEATURE1);
	tx_fifo_sz = (val >> MAC_HW_FEATURE1_TXFIFOSIZE_SHIFT) &
		MAC_HW_FEATURE1_TXFIFOSIZE_MASK;
	rx_fifo_sz = (val >> MAC_HW_FEATURE1_RXFIFOSIZE_SHIFT) &
		MAC_HW_FEATURE1_RXFIFOSIZE_MASK;

	/*our gmac tx/rx fifo is less than 10k ,so tx_fifo_sz/rx_fifo_sz must =< 6*/
	if (tx_fifo_sz > 6)
		tx_fifo_sz = 6;
	if (rx_fifo_sz > 6)
		rx_fifo_sz = 6;

	/*
	 * r/tx_fifo_sz is encoded as log2(n / 128). Undo that by shifting.
	 * r/tqs is encoded as (n / 256) - 1.
	 */
	tqs = (128 << tx_fifo_sz) / 256 - 1;
	rqs = (128 << rx_fifo_sz) / 256 - 1;

	clrsetbits_le32(gmac->base.vaddr + MTL_TXQ0_OPERATION_MODE,
			MTL_TXQ0_OPERATION_MODE_TQS_MASK <<
			MTL_TXQ0_OPERATION_MODE_TQS_SHIFT,
			tqs << MTL_TXQ0_OPERATION_MODE_TQS_SHIFT);
	clrsetbits_le32(gmac->base.vaddr + MTL_RXQ0_OPERATION_MODE,
			MTL_RXQ0_OPERATION_MODE_RQS_MASK <<
			MTL_RXQ0_OPERATION_MODE_RQS_SHIFT,
			rqs << MTL_RXQ0_OPERATION_MODE_RQS_SHIFT);

	/* Flow control used only if each channel gets 4KB or more FIFO */
	if (rqs >= ((4096 / 256) - 1)) {
		u32 rfd, rfa;

		setbits_le32(gmac->base.vaddr + MTL_RXQ0_OPERATION_MODE,
			     MTL_RXQ0_OPERATION_MODE_EHFC);

		/*
		 * Set Threshold for Activating Flow Contol space for min 2
		 * frames ie, (1500 * 1) = 1500 bytes.
		 *
		 * Set Threshold for Deactivating Flow Contol for space of
		 * min 1 frame (frame size 1500bytes) in receive fifo
		 */
		if (rqs == ((4096 / 256) - 1)) {
			/*
			 * This violates the above formula because of FIFO size
			 * limit therefore overflow may occur inspite of this.
			 */
			rfd = 0x3;	/* Full-3K */
			rfa = 0x1;	/* Full-1.5K */
		} else if (rqs == ((8192 / 256) - 1)) {
			rfd = 0x6;	/* Full-4K */
			rfa = 0xa;	/* Full-6K */
		} else if (rqs == ((16384 / 256) - 1)) {
			rfd = 0x6;	/* Full-4K */
			rfa = 0x12;	/* Full-10K */
		} else {
			rfd = 0x6;	/* Full-4K */
			rfa = 0x1E;	/* Full-16K */
		}

		clrsetbits_le32(gmac->base.vaddr + MTL_RXQ0_OPERATION_MODE,
				(MTL_RXQ0_OPERATION_MODE_RFD_MASK <<
				 MTL_RXQ0_OPERATION_MODE_RFD_SHIFT) |
				(MTL_RXQ0_OPERATION_MODE_RFA_MASK <<
				 MTL_RXQ0_OPERATION_MODE_RFA_SHIFT),
				(rfd <<
				 MTL_RXQ0_OPERATION_MODE_RFD_SHIFT) |
				(rfa <<
				 MTL_RXQ0_OPERATION_MODE_RFA_SHIFT));
	}

	/* Configure MAC */
	clrsetbits_le32(gmac->base.vaddr + GMAC_RXQ_CTRL0,
			MAC_RXQ_CTRL0_RXQ0EN_MASK <<
			MAC_RXQ_CTRL0_RXQ0EN_SHIFT,
			0x2 <<
			MAC_RXQ_CTRL0_RXQ0EN_SHIFT);

	/* Multicast and Broadcast Queue Enable */
	setbits_le32(gmac->base.vaddr + GMAC_RXQ_CTRL1, 0x00100000);
	/* enable promise mode */
	setbits_le32(gmac->base.vaddr + GMAC_PACKET_FILTER, BIT(31) | BIT(0));

	setbits_le32(gmac->base.vaddr + GMAC_CONFIG, GMAC_CONFIG_CST | GMAC_CONFIG_ACS);

	/* Configure DMA */
	/* Enable OSP mode */
	setbits_le32(&dma_chan->tx_control, DMA_CONTROL_OSP);

	/* RX buffer size. Must be a multiple of bus width */
	clrsetbits_le32(&dma_chan->rx_control,
			DMA_CH0_RX_CONTROL_RBSZ_MASK <<
			DMA_CH0_RX_CONTROL_RBSZ_SHIFT,
			DESC_DATA_SIZE_MAX <<
			DMA_CH0_RX_CONTROL_RBSZ_SHIFT);

	desc_pad = (sizeof(gmac_desc_t) - 4*sizeof(uint32_t)) / 0x10; //DSL位,dma对齐后跳转描述符长度
	
	setbits_le32(&dma_chan->control,
		     DMA_CH0_CONTROL_PBLX8 |
		     (desc_pad << DMA_CH0_CONTROL_DSL_SHIFT));

	/*
	 * Burst length must be < 1/2 FIFO size.
	 * FIFO size in tqs is encoded as (n / 256) - 1.
	 * Each burst is n * 8 (PBLX8) * 16 (AXI width) == 128 bytes.
	 * Half of n * 256 is n * 128, so pbl == tqs, modulo the -1.
	 */
	pbl = tqs + 1;
	if (pbl > 32)
		pbl = 32;
	clrsetbits_le32(&dma_chan->tx_control,
			DMA_CH0_TX_CONTROL_TXPBL_MASK <<
			DMA_CH0_TX_CONTROL_TXPBL_SHIFT,
			pbl << DMA_CH0_TX_CONTROL_TXPBL_SHIFT);

	clrsetbits_le32(&dma_chan->rx_control,
			DMA_CH0_RX_CONTROL_RXPBL_MASK <<
			DMA_CH0_RX_CONTROL_RXPBL_SHIFT,
			8 << DMA_CH0_RX_CONTROL_RXPBL_SHIFT);

	/* DMA performance configuration */
	val = (2 << DMA_SYSBUS_MODE_RD_OSR_LMT_SHIFT) |
		DMA_SYSBUS_MODE_EAME | DMA_SYSBUS_MODE_BLEN16 |
		DMA_SYSBUS_MODE_BLEN8 | DMA_SYSBUS_MODE_BLEN4;
	writel(val, gmac->base.vaddr + DMA_SYS_BUS_MODE);

    setbits_le32(&dma_chan->status, 0x3fffc7);
    setbits_le32(&dma_chan->intr_ena, DMA_CHAN_INTR_ENA_RIE | DMA_CHAN_INTR_ENA_NIE_4_10/*  | DMA_CHAN_INTR_ENA_RBUE */);

	writel( ~0, gmac->base.vaddr + 0x800);
	writel( ~0, gmac->base.vaddr + 0x70c);
	writel( ~0, gmac->base.vaddr + 0x710);
}

static void gmac_init(dwgmac_t gmac)
{
    dma_chan_t dma_chan = gmac->chan[0];
    struct eth_desc_memanager desc_mm;

    /* 关闭网卡设备所有中断 */
    gmac_mac_interrupt_disable(gmac, GMAC_INT_ALL);
    gmac_mac_stop_tx(gmac);
    gmac_mac_stop_rx(gmac);

    /* Disable TX/RX DMA */
    clrbits_le32(&dma_chan->tx_control, DMA_CONTROL_ST);
    clrbits_le32(&dma_chan->rx_control, DMA_CONTROL_SR);


    /* 初始化网卡描述符 */
    gmac_desc_init(&gmac->rx_desc, 1, EQOS_DESCRIPTORS_RX);
    gmac_desc_init(&gmac->tx_desc, 0, EQOS_DESCRIPTORS_TX);

    /* 设置发送DMA描述符 */
    desc_mm = gmac->tx_desc;
    writel(0, &dma_chan->tx_base_addr_hi);/* 物理地址为4GB以内时可以这样写 */
    writel((uint32_t) desc_mm.dma_paddr, &dma_chan->tx_base_addr);
    writel((uint32_t)(uint64_t) eqos_get_desc(gmac, EQOS_DESCRIPTORS_TX - 1, false, true), &dma_chan->tx_end_addr);
    writel(gmac->tx_desc.total_num - 1, &dma_chan->tx_ring_len);

    /* 设置接收DMA描述符 */
    desc_mm = gmac->rx_desc;
    writel(0, &dma_chan->rx_base_addr_hi);  /* 物理地址为4GB以内时可以这样写 */
    writel((uint32_t) desc_mm.dma_paddr, &dma_chan->rx_base_addr);
    writel((uint32_t)(uint64_t) eqos_get_desc(gmac, EQOS_DESCRIPTORS_RX - 1, true, true), &dma_chan->rx_end_addr);
    writel(gmac->rx_desc.total_num - 1, &dma_chan->rx_ring_len);
}

static int gmac_send(ETH_DEV *eth, ETH_NETPKT *pkt)
{
    dwgmac_t gmac = eth->dev_ctrl;
    struct dma_chan *chan = gmac->chan[0];
    volatile gmac_desc_t *tx_desc;
    unsigned char* vaddr;
    unsigned char* paddr;
    int ret = 0;
	int i = 0;
    uint32_t offset = 0;

    tx_desc = eqos_get_desc(gmac, gmac->tx_desc.index, false, false);

    vaddr = eqos_get_dma_buf(gmac, gmac->tx_desc.index, false, false);
    paddr = eqos_get_dma_buf(gmac, gmac->tx_desc.index, false, true);

    smp_mb();

    for (i = 0; i < 100000; i++)
    {
        if ((tx_desc->des3 & TDES3_OWN) == 0)
        {
            goto start_tx;
        }
    }
    ret = -1;
    goto tx_out;

start_tx:

    memcpy((void *) vaddr, pkt->buf, pkt->len);
    cache_dcache_clean((size_t)vaddr, pkt->len);

    tx_desc->des0 = (uint32_t) ((uint64_t)paddr);
    tx_desc->des1 = 0;
    tx_desc->des2 = pkt->len & TDES2_BUFFER1_SIZE_MASK;
    smp_wmb();
    tx_desc->des3 = TDES3_OWN | TDES3_FIRST_DESCRIPTOR |
                TDES3_LAST_DESCRIPTOR |
                (0x3 << TDES3_CHECKSUM_INSERTION_SHIFT) |
                (pkt->len & TDES3_PACKET_SIZE_MASK);

    gmac->tx_desc.index++;
    gmac->tx_desc.index %= EQOS_DESCRIPTORS_TX;

    smp_wmb();
    

    writel((uint32_t)(uint64_t)((gmac_desc_t *) eqos_get_desc(gmac, gmac->tx_desc.index, false, true)),
             &gmac->chan[0]->tx_end_addr);

    eth_netpkt_free (pkt);

    return 0;

tx_out:
    return ret;
}

static int gmac_receive(ETH_DEV *eth)
{
    dwgmac_t gmac = (dwgmac_t) eth;
    volatile gmac_desc_t *desc = NULL;
    unsigned char* vaddr = NULL;
    unsigned char* paddr = NULL;
    uint32_t len;

    desc = eqos_get_desc(gmac, gmac->rx_desc.index, true, false);
    vaddr = eqos_get_dma_buf(gmac, gmac->rx_desc.index, true, false);
    paddr = eqos_get_dma_buf(gmac, gmac->rx_desc.index, true, true);

    smp_mb();

    if (desc->des3 & RDES3_OWN)
    {
         writel((uint32_t)(uint64_t)((gmac_desc_t *) eqos_get_desc(gmac, EQOS_DESCRIPTORS_RX-1, true, true) ),
                     &gmac->chan[0]->rx_end_addr);
        setbits_le32(&gmac->chan[0]->intr_ena,  DMA_CHAN_INTR_ENA_RIE );
        return 0;
    }

    len = desc->des3 & RDES3_PACKET_SIZE_MASK;


    ETH_NETPKT *pkt = eth_netpkt_alloc (with_netstack,len);

	smp_mb();
    cache_dcache_invalidate((size_t)vaddr, len);

    memcpy(pkt->buf, (void *) vaddr, len);
    smp_mb();
    cache_dcache_invalidate((size_t)vaddr, len);
#ifdef __DEBUG_GMAC__
    int ii = 0;
    printk("recv[%d][vaddr: %lx]desc index[%d]==============>[%lx][%lx]\n", pkt->len, vaddr, gmac->rx_desc.index, desc->des0,paddr);
    for(ii = 0; ii < pkt->len; ii++)
    {
        if(ii != 0 && ii % 16 == 0)
        {
            printk("\n");
        }
        printk("%x ", pkt->buf[ii]);
    }
    printk("\nrecv[%d]<==============\n", pkt->len);
#endif
    desc->des0 = (uint32_t)((unsigned long)paddr);
    desc->des1 = 0;
    desc->des2 = 0;

	smp_mb();
    
    desc->des3 = RDES3_OWN | RDES3_INT_ON_COMPLETION_EN | RDES3_BUFFER1_VALID_ADDR;

    gmac->rx_desc.index++;
    gmac->rx_desc.index %= EQOS_DESCRIPTORS_RX;
    gmac->rx_desc.desc_num--;

    ETH_DATA_TO_STACK(eth, pkt);

    return len;
}


static int gmac_ifup(ETH_DEV *eth)
{
    KLOG_I("%s,%d\n", __FUNCTION__,__LINE__);
    dwgmac_t gmac = (dwgmac_t) eth->dev_ctrl;
    gmac_mac_start_tx(gmac);
    gmac_mac_start_rx(gmac);
    return 0;
}

static int gmac_ifdown(ETH_DEV *eth)
{
    KLOG_I("%s,%d\n", __FUNCTION__,__LINE__);
    dwgmac_t gmac = (dwgmac_t) eth->dev_ctrl;
    gmac_mac_stop_rx(gmac);
    gmac_mac_stop_tx(gmac);
    return 0;
}

static int gmac_set_macaddr (ETH_DEV *ethdev, const char *hwaddr)
{
    dwgmac_t gmac = (dwgmac_t) ethdev;
    ETH_CFG_INFO *cfg_info;
    unsigned int val = 0;
    cfg_info = gmac->netdev_cfg;

    if (hwaddr && cfg_info)
    {
        memcpy(cfg_info->mac_addr, (void *) hwaddr, 6);

        /* 将mac地址设置到网卡寄存器 */
        val = (hwaddr[5] << 8) | (hwaddr[4]);
        writel(val, gmac->base.vaddr + GMAC_ADDR_HIGH(0));
        val = (hwaddr[3] << 24) |
            (hwaddr[2] << 16) |
            (hwaddr[1] << 8) |
            (hwaddr[0]);
        writel(val, gmac->base.vaddr + GMAC_ADDR_LOW(0));

        cfg_info->is_default = true;
    }

    return 0;
}

static int gmac_get_macaddr (ETH_DEV *ethdev, char *hwaddr)
{
    dwgmac_t gmac = (dwgmac_t) ethdev;
    const unsigned char *prop_addr;
    struct device *dev = NULL;
    ETH_CFG_INFO *cfg_info;
    cfg_info = gmac->netdev_cfg;
    dev = dev_priv_get(gmac);

    if(dev && cfg_info)
    {
        if(cfg_info->is_default)
        {
            /* 获取设备树中指定的MAC地址 */
            prop_addr = of_get_property(dev->of_node,
                        "local-mac-address", NULL);
            memcpy(hwaddr, prop_addr, 6);
            cfg_info->is_default = true;
        }
        else
        {
            memcpy((void *) hwaddr, cfg_info->mac_addr, 6);
        }
        KLOG_I("%x %x %x %x %x %x \n", hwaddr[0],hwaddr[1],hwaddr[2],hwaddr[3],hwaddr[4],hwaddr[5]);
    }
    if (prop_addr == NULL)
    {
            hwaddr[0] = 0x0;
            hwaddr[1] = 0x0;
            hwaddr[2] = 0x0;
            hwaddr[3] = (uint8_t)rand();
            hwaddr[4] = (uint8_t)rand();
            hwaddr[5] = (uint8_t)rand();
    }
    return 0;
}

/**
 *  设备ioctl示例
 */
static int gmac_net_ioctl (ETH_DEV *ethdev, int cmd, unsigned long arg)
{
    switch (cmd)
    {
        case SIOCSIFHWADDR:
            return gmac_set_macaddr((ETH_DEV *) ethdev,
                    (const char *) ((struct ifreq *) arg)->ifr_hwaddr.sa_data);
        case SIOCSIFPHYREAD:
        	break;
        case SIOCSIFPHYWRITE:
        	break;
        default:
            return -ENOTSUP;
    }
}


static void gmac_netdev_init(struct device *dev)
{
    dwgmac_t gmac = (dwgmac_t) dev->priv;

	eth_device_init (&gmac_net_drv_info, &gmac->ethdev, dev);
}

#if 0
/* 以太网设备数据接收任务 */
static void eth_linkup_task ( dwgmac_t gmac)
{
    uint32_t sts = 0;
    unsigned long t = (unsigned long)(gmac->base.vaddr + MAC_PHYIF_CONTROL_STATUS);
    while(1)
    {
        sts = readl(t);  /* 获取phy的状态信息 */
        if (sts & (1 << 19))
        {
//            KLOG_I("%s phy link up\n", gmac->ethdev.netdev->name);
            printk("%s phy link up\n", gmac->ethdev.netdev->name);
        }
        else if (sts == 0)
        {
//            KLOG_I("%s phy link up\n", gmac->ethdev.netdev->name);
                    printk("%s phy link up\n", gmac->ethdev.netdev->name);
        }

        TTOS_SleepTask(120);
    }
}
#endif
static int gmac_probe(struct device *dev)
{
    dwgmac_t gmac;
    struct eth_cfg_info *cfg_info = NULL;
    TASK_ID tid;
    gmac = malloc(sizeof(struct dwgmac));
    memset(gmac, 0, sizeof(struct dwgmac));
    dev_priv_set(gmac, dev);
    dev_priv_set(dev, gmac);
    cfg_info = malloc(sizeof(struct eth_cfg_info));
    gmac->netdev_cfg = cfg_info;
    memset(cfg_info, 0, sizeof(struct eth_cfg_info));
    cfg_info->is_default = true;    /* 在第一次获取网卡mac地址时，读取设备树的mac地址 */

    platform_get_resource_regs(dev, &gmac->base, 1);


    gmac->irq = ttos_pic_irq_alloc(dev, 0);
    ttos_pic_irq_mask(gmac->irq);


    gmac->chan[0] = (dma_chan_t) (gmac->base.vaddr +
                (uintptr_t) dma_chanx_base_addr(NULL, 0));

    KLOG_W("gmac: %p", gmac);
    KLOG_I("dev: %s", dev->of_node->full_name);

    gmac_init(gmac);
    gmac_netdev_init(dev);
  	mac_mtl_dma_register_init(gmac);

    ttos_pic_irq_install(gmac->irq, gmac_irq_handle, gmac, 0, dev->name);
    ttos_pic_irq_trigger_mode_set(gmac->irq, PIC_IRQ_TRIGGER_EDGE);
    ttos_pic_irq_unmask(gmac->irq);


#if 0
    TTOS_CreateTaskEx((unsigned char *)"linkup", 200, TRUE, TRUE, (void (*)())eth_linkup_task, gmac, 0x1000, &tid);
#endif

#ifdef  __DEBUG_GMAC__
    static int i = 0;
    if(i == 0)
    {
        dbg_gmac[0] = gmac;
    }
    else if(i == 1)
    {
        dbg_gmac[1] = gmac;
    }
    i++;
#endif

    return 0;
}


/**********************************************************************************************************/
static unsigned int mdio_nobusy_wait (ETH_DEV *ethdev, unsigned int limit)
{
    dwgmac_t gmac = (dwgmac_t) ethdev;
    volatile unsigned int k = 0;

    while ((readl (gmac->base.vaddr + GMAC_MDIO_ADDR) & GMII_ADDR_BUSY) && (k < limit))
    {
        k++;
    }

    return k;
}

static int gmac_mdio_read (void *dev, unsigned char phy_addr, unsigned char reg_addr, unsigned short *data_val)
{
    ETH_DEV *ethdev = (ETH_DEV *)dev;
    dwgmac_t gmac = (dwgmac_t) ethdev;

    unsigned int k;
    unsigned int addr_val;

    if (ethdev == NULL)
    {
        return (-1);
    }

    if (data_val == NULL)
    {
        return (-1);
    }

    if (phy_addr > 31)
    {
        return (-1);
    }

    TTOS_ObtainMutex (ethdev->phy_mutex, TTOS_WAIT_FOREVER);

    addr_val = GMII_ADDR_BUSY;

    addr_val |= ((unsigned int)phy_addr & GMII_PHY_ADDR_MASK) << GMII_PHY_ADDR_SHIFT;
    addr_val |= ((unsigned int)reg_addr & GMII_REG_ADDR_MASK) << GMII_REG_ADDR_SHIFT;
    addr_val |= (GMII_ADDR_CSR_100_150_MHZ & GMII_CSR_CLK_MASK) << GMII_CSR_CLK_SHIFT;

    addr_val |= GMII_ADDR_READ;

    k = mdio_nobusy_wait (ethdev, MDIO_BUSY_WAIT_TIMEOUT);
    if (k == MDIO_BUSY_WAIT_TIMEOUT)
    {
        TTOS_ReleaseMutex (ethdev->phy_mutex);

        return (-1);
    }

    writel (addr_val, gmac->base.vaddr + GMAC_MDIO_ADDR);

    k = mdio_nobusy_wait (ethdev, MDIO_BUSY_WAIT_TIMEOUT);
    if (k == MDIO_BUSY_WAIT_TIMEOUT)
    {
        TTOS_ReleaseMutex (ethdev->phy_mutex);

        return (-1);
    }

    *data_val = (unsigned short)readl (gmac->base.vaddr + GMAC_MDIO_DATA);

    TTOS_ReleaseMutex (ethdev->phy_mutex);

    return OK;
}

static int gmac_mdio_write (void *dev, unsigned char phy_addr, unsigned char reg_addr, unsigned short data_val)
{
    ETH_DEV *ethdev = (ETH_DEV *)dev;
    dwgmac_t gmac = (dwgmac_t) ethdev;

    unsigned int k;
    unsigned int addr_val;

    if (dev == NULL)
    {
        return (-1);
    }

    if (phy_addr > 31)
    {
        return (-1);
    }

    TTOS_ObtainMutex (ethdev->phy_mutex, TTOS_WAIT_FOREVER);

    addr_val = GMII_ADDR_BUSY;
    addr_val |= ((unsigned int)phy_addr & GMII_PHY_ADDR_MASK) << GMII_PHY_ADDR_SHIFT;
    addr_val |= ((unsigned int)reg_addr & GMII_REG_ADDR_MASK) << GMII_REG_ADDR_SHIFT;
    addr_val |= (GMII_ADDR_CSR_100_150_MHZ & GMII_CSR_CLK_MASK) << GMII_CSR_CLK_SHIFT;
    addr_val |= GMII_ADDR_WRITE;

    /* Wait for the busy bit to become clear */

    k = mdio_nobusy_wait (ethdev, MDIO_BUSY_WAIT_TIMEOUT);
    if (k == MDIO_BUSY_WAIT_TIMEOUT)
    {
        TTOS_ReleaseMutex (ethdev->phy_mutex);

        return (-1);
    }

    writel (data_val, gmac->base.vaddr + GMAC_MDIO_DATA);
    writel (addr_val, gmac->base.vaddr + GMAC_MDIO_ADDR);

    k = mdio_nobusy_wait (ethdev, MDIO_BUSY_WAIT_TIMEOUT);
    if (k == MDIO_BUSY_WAIT_TIMEOUT)
    {
        TTOS_ReleaseMutex (ethdev->phy_mutex);

        return (-1);
    }

    TTOS_ReleaseMutex (ethdev->phy_mutex);

    return OK;
}

static int gmac_link_update (ETH_DEV *ethdev)
{
    dwgmac_t gmac = (dwgmac_t) ethdev;
    unsigned int old_status;
    unsigned int old_media;
    unsigned int gmac_control;
    unsigned int clk_rate = 0;
    unsigned int speed;

    if ((ethdev == NULL))
    {
        return (-1);
    }

    TTOS_ObtainMutex (ethdev->phy_mutex, TTOS_WAIT_FOREVER);

    /* 如果网卡是DOWN状态则无需处理PHY状态的变化 */
    if (!(netdev_is_up (ethdev->netdev)))
    {
        TTOS_ReleaseMutex (ethdev->phy_mutex);
        return (OK);
    }

    old_status = ethdev->phy_status;
    old_media  = ethdev->phy_media;

    if (ethdev->phydev == NULL)
    {
        ethdev->phy_status = IFM_ACTIVE | IFM_AVALID;
        ethdev->phy_media  = IFM_ETHER | IFM_FDX | IFM_1000_T;
    }
    else
    {
        if (phy_mode_get (ethdev->phydev, &ethdev->phy_media, &ethdev->phy_status) == ERROR)
        {
            KLOG_E("phy_mode_get() failed");
            TTOS_ReleaseMutex (ethdev->phy_mutex);

            return (-1);
        }
    }

    /* 链接状态无变化则直接返回 */
    if ((old_status == ethdev->phy_status) && (old_media == ethdev->phy_media))
    {
        KLOG_I("PHY's status and media no change");
        TTOS_ReleaseMutex (ethdev->phy_mutex);

        return (OK);
    }

    gmac_control = readl (gmac->base.vaddr + GMAC_CONFIG);

    if (ethdev->phy_media & IFM_FDX)
    {
        gmac_control |= GMAC_CONFIG_DM;
    }
    else
    {
        gmac_control &= ~GMAC_CONFIG_DM;
    }

    switch (IFM_SUBTYPE (ethdev->phy_media))
    {
        case IFM_1000_T:
            gmac_control &= ~(GMAC_CONFIG_PS | GMAC_CONFIG_FES);
            speed = 1000;
            //clk_rate = GMAC_1G_CLK;
            break;

        case IFM_100_TX:
            gmac_control |= (GMAC_CONFIG_FES | GMAC_CONFIG_PS);
            //clk_rate = GMAC_100M_CLK;
            speed = 100;
            break;

        case IFM_10_T:
            gmac_control &= ~GMAC_CONFIG_FES;
            gmac_control |= GMAC_CONFIG_PS;
            //clk_rate = GMAC_10M_CLK;
            speed = 10;
            break;

        default:
            break;
    }

    //TODO: 是否需要设置对应的时钟速率？
    //gmac_txclk_set (ethdev, clk_rate);

    writel (gmac_control, gmac->base.vaddr + GMAC_CONFIG);

    if (!(netdev_is_up(ethdev->netdev)))
    {
        TTOS_ReleaseMutex (ethdev->phy_mutex);
        return (OK);
    }

    /* 根据链接变化进行相应处理 */
    if ((ethdev->phy_status & IFM_ACTIVE) && !(old_status & IFM_ACTIVE))
    {
        gmac_control |= (GMAC_CONFIG_RE | GMAC_CONFIG_TE);

        writel (gmac_control, gmac->base.vaddr + GMAC_CONFIG);

        KLOG_I(">>>>> LINK IS UP <<<<< Speed: [%dMb] <%s>", speed, ((ethdev->phy_media & IFM_FDX) ? "Full Duplex" :"Half duplex"));

        netdev_set_link_up (ethdev->netdev);
    }
    else if (!(ethdev->phy_status & IFM_ACTIVE) && old_status & IFM_ACTIVE)
    {
        gmac_control &= ~(GMAC_CONFIG_RE | GMAC_CONFIG_TE);
        writel (gmac_control, gmac->base.vaddr + GMAC_CONFIG);

        KLOG_I(">>>>> LINK IS DOWN <<<<<");

        netdev_set_link_down (ethdev->netdev);
    }

    TTOS_ReleaseMutex (ethdev->phy_mutex);

    return (OK);
}

/**********************************************************************************************************/



static struct of_device_id dwgmac_table[] = {
    {.compatible = "semidrive,dwc-qos-ethernet",},
    { /* end of list */ },
};

static struct driver semidrive_driver = {
    .name        = "gmac",
    .probe       = gmac_probe,
    .match_table = dwgmac_table,
};

static int32_t semidrive_driver_init(void)
{
    return platform_add_driver(&semidrive_driver);
}
INIT_EXPORT_DRIVER(semidrive_driver_init, "dwgmac driver");

#ifdef __DEBUG_GMAC__

#include <shell.h>
#include <lwip/pbuf.h>
static void gmac_test(int argc, const char *argv[])
{
    char *buf = NULL;
    char buf1[60]  =
    {
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x14, 0x97, 0xA3, 0xB6, 0xDC, 0x08, 0x06, 0x00, 0x01,
        0x08, 0x00, 0x06, 0x04, 0x00, 0x01, 0x00, 0x14, 0x97, 0xA3, 0xB6, 0xDC, 0x0a, 0x0a, 0x01, 0x17,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    };
    struct pbuf p;
    uint32_t i, count;
    ETH_NETPKT pkt;


    p.tot_len = 60;
    p.len = p.tot_len;
    p.payload = malloc(p.tot_len);
    p.next = NULL;

    buf = p.payload;
    memcpy(buf, buf1, p.len);
    pkt.len = 60;
    pkt.buf = buf1;
    printk("p.len:%d, p.tot_len:%d, p:0x%lx\n",p.len , p.tot_len,&p);
	pkt.flags = 0x0;    /* 不释放 */
	gmac_send(&(dbg_gmac[0]->ethdev), (ETH_NETPKT *)&pkt);


}
SHELL_EXPORT_CMD (SHELL_CMD_PERMISSION (0)
                    | SHELL_CMD_TYPE (SHELL_TYPE_CMD_MAIN)
                    | SHELL_CMD_DISABLE_RETURN,
                    gmac, gmac_test, gmac);





#endif
