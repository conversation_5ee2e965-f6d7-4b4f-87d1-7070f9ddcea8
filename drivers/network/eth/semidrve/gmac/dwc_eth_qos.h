/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Copyright 2020
 */

#ifndef _DWC_ETH_QOS_H
#define _DWC_ETH_QOS_H

#include <io.h>
#include <sys/types.h>
#include <driver/clk.h>
#include <driver/phy/phy.h>

#endif
#ifndef __DWMAC4_H__
#define __DWMAC4_H__


#define DESC_DATA_SIZE_MAX  2048

#define EQOS_DESCRIPTORS_TX    1024
#define EQOS_DESCRIPTORS_RX    1024

#define MAC_PHYIF_CONTROL_STATUS    0x000000f8
#define MAC_INT_STATUS          0x000000b0



/*  MAC registers */
#define GMAC_CONFIG                     0x00000000
#define GMAC_EXT_CONFIG                 0x00000004
#define GMAC_PACKET_FILTER              0x00000008
#define GMAC_HASH_TAB(x)                (0x10 + (x) * 4)
#define GMAC_VLAN_TAG                   0x00000050
#define GMAC_VLAN_TAG_DATA              0x00000054
#define GMAC_VLAN_HASH_TABLE            0x00000058
#define GMAC_RX_FLOW_CTRL               0x00000090
#define GMAC_VLAN_INCL                  0x00000060
#define GMAC_QX_TX_FLOW_CTRL(x)         (0x70 + x * 4)
#define GMAC_TXQ_PRTY_MAP0              0x98
#define GMAC_TXQ_PRTY_MAP1              0x9C
#define GMAC_RXQ_CTRL0                  0x000000a0
#define GMAC_RXQ_CTRL1                  0x000000a4
#define GMAC_RXQ_CTRL2                  0x000000a8
#define GMAC_RXQ_CTRL3                  0x000000ac
#define GMAC_INT_STATUS                 0x000000b0
#define GMAC_INT_EN                     0x000000b4
#define GMAC_1US_TIC_COUNTER            0x000000dc
#define GMAC_PCS_BASE                   0x000000e0
#define GMAC_PHYIF_CONTROL_STATUS       0x000000f8
#define GMAC_PMT                        0x000000c0
#define GMAC_DEBUG                      0x00000114
#define GMAC_HW_FEATURE0                0x0000011c
#define GMAC_HW_FEATURE1                0x00000120
#define GMAC_HW_FEATURE2                0x00000124
#define GMAC_HW_FEATURE3                0x00000128
#define GMAC_MDIO_ADDR                  0x00000200
#define GMAC_MDIO_DATA                  0x00000204
#define GMAC_GPIO_STATUS                0x0000020C
#define GMAC_ARP_ADDR                   0x00000210
#define GMAC_ADDR_HIGH(reg)             (0x300 + reg * 8)
#define GMAC_ADDR_LOW(reg)              (0x304 + reg * 8)
#define GMAC_L3L4_CTRL(reg)             (0x900 + (reg) * 0x30)
#define GMAC_L4_ADDR(reg)               (0x904 + (reg) * 0x30)
#define GMAC_L3_ADDR0(reg)              (0x910 + (reg) * 0x30)
#define GMAC_L3_ADDR1(reg)              (0x914 + (reg) * 0x30)
#define GMAC_TIMESTAMP_STATUS           0x00000b20


/* MAC config */
#define GMAC_CONFIG_ARPEN       BIT(31)
#define GMAC_CONFIG_SARC        GENMASK(30, 28)
#define GMAC_CONFIG_SARC_SHIFT      28
#define GMAC_CONFIG_IPC         BIT(27)
#define GMAC_CONFIG_IPG         GENMASK(26, 24)
#define GMAC_CONFIG_IPG_SHIFT       24
#define GMAC_CONFIG_2K          BIT(22)
#define GMAC_CONFIG_CST         BIT(21)
#define GMAC_CONFIG_ACS         BIT(20)
#define GMAC_CONFIG_BE          BIT(18)
#define GMAC_CONFIG_JD          BIT(17)
#define GMAC_CONFIG_JE          BIT(16)
#define GMAC_CONFIG_PS          BIT(15)
#define GMAC_CONFIG_FES         BIT(14)
#define GMAC_CONFIG_FES_SHIFT       14
#define GMAC_CONFIG_DM          BIT(13)
#define GMAC_CONFIG_LM          BIT(12)
#define GMAC_CONFIG_DCRS        BIT(9)
#define GMAC_CONFIG_TE          BIT(1)
#define GMAC_CONFIG_RE          BIT(0)


/*  MAC Interrupt bitmap*/
#define GMAC_INT_RGSMIIS        BIT(0)
#define GMAC_INT_PCS_LINK       BIT(1)
#define GMAC_INT_PCS_ANE        BIT(2)
#define GMAC_INT_PCS_PHYIS      BIT(3)
#define GMAC_INT_PMT_EN         BIT(4)
#define GMAC_INT_LPI_EN         BIT(5)
#define GMAC_INT_TSIE           BIT(12)
#define GMAC_INT_ALL            0xFFFFFFFF

#define GMAC_PCS_IRQ_DEFAULT    (GMAC_INT_RGSMIIS | GMAC_INT_PCS_LINK | \
                                GMAC_INT_PCS_ANE)

#define GMAC_INT_DEFAULT_ENABLE (GMAC_INT_PMT_EN | GMAC_INT_LPI_EN | \
                                GMAC_INT_TSIE)





/* Define the max channel number used for tx (also rx).
 * dwmac4 accepts up to 8 channels for TX (and also 8 channels for RX
 */
#define DMA_CHANNEL_NB_MAX          1

#define DMA_BUS_MODE                0x00001000
#define DMA_SYS_BUS_MODE            0x00001004
#define DMA_STATUS                  0x00001008
#define DMA_DEBUG_STATUS_0          0x0000100c
#define DMA_DEBUG_STATUS_1          0x00001010
#define DMA_DEBUG_STATUS_2          0x00001014
#define DMA_AXI_BUS_MODE            0x00001028
#define DMA_TBS_CTRL                0x00001050

/* DMA Bus Mode bitmap */
#define DMA_BUS_MODE_DCHE           BIT(19)
#define DMA_BUS_MODE_INTM_MASK      GENMASK(17, 16)
#define DMA_BUS_MODE_INTM_SHIFT     16
#define DMA_BUS_MODE_INTM_MODE1     0x1
#define DMA_BUS_MODE_SFT_RESET      BIT(0)

/* DMA SYS Bus Mode bitmap */
#define DMA_BUS_MODE_SPH            BIT(24)
#define DMA_BUS_MODE_PBL            BIT(16)
#define DMA_BUS_MODE_PBL_SHIFT          16
#define DMA_BUS_MODE_RPBL_SHIFT         16
#define DMA_BUS_MODE_MB             BIT(14)
#define DMA_BUS_MODE_FB             BIT(0)

/* DMA Interrupt top status */
#define DMA_STATUS_MAC              BIT(17)
#define DMA_STATUS_MTL              BIT(16)
#define DMA_STATUS_CHAN7            BIT(7)
#define DMA_STATUS_CHAN6            BIT(6)
#define DMA_STATUS_CHAN5            BIT(5)
#define DMA_STATUS_CHAN4            BIT(4)
#define DMA_STATUS_CHAN3            BIT(3)
#define DMA_STATUS_CHAN2            BIT(2)
#define DMA_STATUS_CHAN1            BIT(1)
#define DMA_STATUS_CHAN0            BIT(0)

/* DMA debug status bitmap */
#define DMA_DEBUG_STATUS_TS_MASK    0xf
#define DMA_DEBUG_STATUS_RS_MASK    0xf

/* DMA AXI bitmap */
#define DMA_AXI_EN_LPI              BIT(31)
#define DMA_AXI_LPI_XIT_FRM         BIT(30)
#define DMA_AXI_WR_OSR_LMT          GENMASK(27, 24)
#define DMA_AXI_WR_OSR_LMT_SHIFT    24
#define DMA_AXI_RD_OSR_LMT          GENMASK(19, 16)
#define DMA_AXI_RD_OSR_LMT_SHIFT    16

#define DMA_AXI_OSR_MAX         0xf
#define DMA_AXI_MAX_OSR_LIMIT ((DMA_AXI_OSR_MAX << DMA_AXI_WR_OSR_LMT_SHIFT) | \
                (DMA_AXI_OSR_MAX << DMA_AXI_RD_OSR_LMT_SHIFT))

#define DMA_SYS_BUS_MB              BIT(14)
#define DMA_AXI_1KBBE               BIT(13)
#define DMA_SYS_BUS_AAL             BIT(12)
#define DMA_SYS_BUS_EAME            BIT(11)
#define DMA_AXI_BLEN256             BIT(7)
#define DMA_AXI_BLEN128             BIT(6)
#define DMA_AXI_BLEN64              BIT(5)
#define DMA_AXI_BLEN32              BIT(4)
#define DMA_AXI_BLEN16              BIT(3)
#define DMA_AXI_BLEN8               BIT(2)
#define DMA_AXI_BLEN4               BIT(1)
#define DMA_SYS_BUS_FB              BIT(0)

#define DMA_BURST_LEN_DEFAULT       (DMA_AXI_BLEN256 | DMA_AXI_BLEN128 | \
                                     DMA_AXI_BLEN64 | DMA_AXI_BLEN32 | \
                                     DMA_AXI_BLEN16 | DMA_AXI_BLEN8 | \
                                     DMA_AXI_BLEN4)

#define DMA_AXI_BURST_LEN_MASK      0x000000FE

/* DMA TBS Control */
#define DMA_TBS_FTOS                GENMASK(31, 8)
#define DMA_TBS_FTOV                BIT(0)
#define DMA_TBS_DEF_FTOS            (DMA_TBS_FTOS | DMA_TBS_FTOV)

/* Following DMA defines are channel-oriented */
#define DMA_CHAN_BASE_ADDR          0x00001100
//#define DMA_CHAN_BASE_ADDR          0x00001000
#define DMA_CHAN_BASE_OFFSET        0x80

static inline uint32_t dma_chanx_base_addr(void* addrs,const uint32_t x)
{
    uint32_t addr;

        // if (addrs)
        //     addr = addrs->dma_chan + (x * addrs->dma_chan_offset);
        // else
    addr = DMA_CHAN_BASE_ADDR + (x * DMA_CHAN_BASE_OFFSET);

    return addr;
}

typedef struct dma_chan
{
    uint32_t control;               /* 0x0  */
    uint32_t tx_control;            /* 0x4  */
    uint32_t rx_control;            /* 0x8  */
    uint32_t res1;                  /* 0xC  */
    uint32_t tx_base_addr_hi;       /* 0x10 */
    uint32_t tx_base_addr;          /* 0x14 */
    uint32_t rx_base_addr_hi;       /* 0x18 */
    uint32_t rx_base_addr;          /* 0x1c */
    uint32_t tx_end_addr;           /* 0x20 */
    uint32_t res2;                  /* 0x24 */
    uint32_t rx_end_addr;           /* 0x28 */
    uint32_t tx_ring_len;           /* 0x2c */
    uint32_t rx_ring_len;           /* 0x30 */
    uint32_t intr_ena;              /* 0x34 */
    uint32_t rx_watchdog;           /* 0x38 */
    uint32_t slot_ctrl_status;      /* 0x3c */
    uint32_t res3;                  /* 0x40 */
    uint32_t cur_tx_desc;           /* 0x44 */
    uint32_t res4;                  /* 0x48 */
    uint32_t cur_rx_desc;           /* 0x4c */
    uint32_t res5;                  /* 0x50 */
    uint32_t cur_tx_buf_addr;       /* 0x54 */
    uint32_t res6;                  /* 0x58 */
    uint32_t cur_rx_buf_addr;       /* 0x5c */
    uint32_t status;                /* 0x60 */
} *dma_chan_t;


/* DMA Control X */
#define DMA_CONTROL_SPH             BIT(24)
#define DMA_CONTROL_MSS_MASK        GENMASK(13, 0)

/* DMA Tx Channel X Control register defines */
#define DMA_CONTROL_EDSE            BIT(28)
#define DMA_CONTROL_TSE             BIT(12)
#define DMA_CONTROL_OSP             BIT(4)
#define DMA_CONTROL_ST              BIT(0)

/* DMA Rx Channel X Control register defines */
#define DMA_CONTROL_SR              BIT(0)
#define DMA_RBSZ_MASK               GENMASK(14, 1)
#define DMA_RBSZ_SHIFT              1

/* Interrupt status per channel */
#define DMA_CHAN_STATUS_REB         GENMASK(21, 19)
#define DMA_CHAN_STATUS_REB_SHIFT   19
#define DMA_CHAN_STATUS_TEB         GENMASK(18, 16)
#define DMA_CHAN_STATUS_TEB_SHIFT   16
#define DMA_CHAN_STATUS_NIS         BIT(15)
#define DMA_CHAN_STATUS_AIS         BIT(14)
#define DMA_CHAN_STATUS_CDE         BIT(13)
#define DMA_CHAN_STATUS_FBE         BIT(12)
#define DMA_CHAN_STATUS_ERI         BIT(11)
#define DMA_CHAN_STATUS_ETI         BIT(10)
#define DMA_CHAN_STATUS_RWT         BIT(9)
#define DMA_CHAN_STATUS_RPS         BIT(8)
#define DMA_CHAN_STATUS_RBU         BIT(7)
#define DMA_CHAN_STATUS_RI          BIT(6)
#define DMA_CHAN_STATUS_TBU         BIT(2)
#define DMA_CHAN_STATUS_TPS         BIT(1)
#define DMA_CHAN_STATUS_TI          BIT(0)

#define DMA_CHAN_STATUS_MSK_COMMON  (DMA_CHAN_STATUS_NIS | \
                                     DMA_CHAN_STATUS_AIS | \
                                     DMA_CHAN_STATUS_CDE | \
                                     DMA_CHAN_STATUS_FBE)

#define DMA_CHAN_STATUS_MSK_RX      (DMA_CHAN_STATUS_REB | \
                                     DMA_CHAN_STATUS_ERI | \
                                     DMA_CHAN_STATUS_RWT | \
                                     DMA_CHAN_STATUS_RPS | \
                                     DMA_CHAN_STATUS_RBU | \
                                     DMA_CHAN_STATUS_RI | \
                                     DMA_CHAN_STATUS_MSK_COMMON)

#define DMA_CHAN_STATUS_MSK_TX      (DMA_CHAN_STATUS_ETI | \
                                     DMA_CHAN_STATUS_TBU | \
                                     DMA_CHAN_STATUS_TPS | \
                                     DMA_CHAN_STATUS_TI | \
                                     DMA_CHAN_STATUS_MSK_COMMON)

/* Interrupt enable bits per channel */
#define DMA_CHAN_INTR_ENA_NIE       BIT(16)
#define DMA_CHAN_INTR_ENA_AIE       BIT(15)
#define DMA_CHAN_INTR_ENA_NIE_4_10  BIT(15)
#define DMA_CHAN_INTR_ENA_AIE_4_10  BIT(14)
#define DMA_CHAN_INTR_ENA_CDE       BIT(13)
#define DMA_CHAN_INTR_ENA_FBE       BIT(12)
#define DMA_CHAN_INTR_ENA_ERE       BIT(11)
#define DMA_CHAN_INTR_ENA_ETE       BIT(10)
#define DMA_CHAN_INTR_ENA_RWE       BIT(9)
#define DMA_CHAN_INTR_ENA_RSE       BIT(8)
#define DMA_CHAN_INTR_ENA_RBUE      BIT(7)
#define DMA_CHAN_INTR_ENA_RIE       BIT(6)
#define DMA_CHAN_INTR_ENA_TBUE      BIT(2)
#define DMA_CHAN_INTR_ENA_TSE       BIT(1)
#define DMA_CHAN_INTR_ENA_TIE       BIT(0)

#define DMA_CHAN_INTR_NORMAL        (DMA_CHAN_INTR_ENA_NIE | \
                                     DMA_CHAN_INTR_ENA_RIE | \
                                     DMA_CHAN_INTR_ENA_TIE)

#define DMA_CHAN_INTR_ABNORMAL      (DMA_CHAN_INTR_ENA_AIE | \
                                     DMA_CHAN_INTR_ENA_FBE)
/* DMA default interrupt mask for 4.00 */
#define DMA_CHAN_INTR_DEFAULT_MASK  (DMA_CHAN_INTR_NORMAL | \
                                     DMA_CHAN_INTR_ABNORMAL)
#define DMA_CHAN_INTR_DEFAULT_RX    (DMA_CHAN_INTR_ENA_RIE)
#define DMA_CHAN_INTR_DEFAULT_TX    (DMA_CHAN_INTR_ENA_TIE)

#define DMA_CHAN_INTR_NORMAL_4_10   (DMA_CHAN_INTR_ENA_NIE_4_10 | \
                                     DMA_CHAN_INTR_ENA_RIE | \
                                     DMA_CHAN_INTR_ENA_TIE)

#define DMA_CHAN_INTR_ABNORMAL_4_10 (DMA_CHAN_INTR_ENA_AIE_4_10 | \
                                     DMA_CHAN_INTR_ENA_FBE)
/* DMA default interrupt mask for 4.10a */
#define DMA_CHAN_INTR_DEFAULT_MASK_4_10 (DMA_CHAN_INTR_NORMAL_4_10 | \
                                         DMA_CHAN_INTR_ABNORMAL_4_10)
#define DMA_CHAN_INTR_DEFAULT_RX_4_10   (DMA_CHAN_INTR_ENA_RIE)
#define DMA_CHAN_INTR_DEFAULT_TX_4_10   (DMA_CHAN_INTR_ENA_TIE)

/* channel 0 specific fields */
#define DMA_CHAN0_DBG_STAT_TPS          GENMASK(15, 12)
#define DMA_CHAN0_DBG_STAT_TPS_SHIFT    12
#define DMA_CHAN0_DBG_STAT_RPS          GENMASK(11, 8)
#define DMA_CHAN0_DBG_STAT_RPS_SHIFT    8

#define DMA_SYSBUS_MODE_RD_OSR_LMT_SHIFT		16
#define DMA_SYSBUS_MODE_RD_OSR_LMT_MASK		0xf
#define DMA_SYSBUS_MODE_EAME			BIT(11)
#define DMA_SYSBUS_MODE_BLEN16			BIT(3)
#define DMA_SYSBUS_MODE_BLEN8			BIT(2)
#define DMA_SYSBUS_MODE_BLEN4			BIT(1)

#define DMA_CH0_CONTROL_DSL_SHIFT			18
#define DMA_CH0_CONTROL_PBLX8			BIT(16)

#define DMA_CH0_TX_CONTROL_TXPBL_SHIFT		16
#define DMA_CH0_TX_CONTROL_TXPBL_MASK		0x3f
#define DMA_CH0_TX_CONTROL_OSP			BIT(4)
#define DMA_CH0_TX_CONTROL_ST			BIT(0)

#define DMA_CH0_RX_CONTROL_RXPBL_SHIFT		16
#define DMA_CH0_RX_CONTROL_RXPBL_MASK		0x3f
#define DMA_CH0_RX_CONTROL_RBSZ_SHIFT		1
#define DMA_CH0_RX_CONTROL_RBSZ_MASK		0x3fff
#define DMA_CH0_RX_CONTROL_SR			BIT(0)

#define MTL_TXQ0_OPERATION_MODE              0xd00
#define MTL_RXQ0_OPERATION_MODE              0xd30
#define MTL_TXQ0_OPERATION_MODE_TQS_SHIFT		16
#define MTL_TXQ0_OPERATION_MODE_TQS_MASK		0x1ff
#define MTL_TXQ0_OPERATION_MODE_TXQEN_SHIFT	2
#define MTL_TXQ0_OPERATION_MODE_TXQEN_MASK		3
#define MTL_TXQ0_OPERATION_MODE_TXQEN_ENABLED	2
#define MTL_TXQ0_OPERATION_MODE_TSF		BIT(1)
#define MTL_TXQ0_OPERATION_MODE_FTQ		BIT(0)

#define MTL_TXQ0_DEBUG_TXQSTS			BIT(4)
#define MTL_TXQ0_DEBUG_TRCSTS_SHIFT		1
#define MTL_TXQ0_DEBUG_TRCSTS_MASK			3

#define MTL_RXQ0_OPERATION_MODE_RQS_SHIFT		20
#define MTL_RXQ0_OPERATION_MODE_RQS_MASK		0x3ff
#define MTL_RXQ0_OPERATION_MODE_RFD_SHIFT		14
#define MTL_RXQ0_OPERATION_MODE_RFD_MASK		0x3f
#define MTL_RXQ0_OPERATION_MODE_RFA_SHIFT		8
#define MTL_RXQ0_OPERATION_MODE_RFA_MASK		0x3f
#define MTL_RXQ0_OPERATION_MODE_EHFC		BIT(7)
#define MTL_RXQ0_OPERATION_MODE_RSF		BIT(5)
#define MTL_RXQ0_OPERATION_MODE_FEP		BIT(4)
#define MTL_RXQ0_OPERATION_MODE_FUP		BIT(3)

#define MTL_RXQ0_DEBUG_PRXQ_SHIFT			16
#define MTL_RXQ0_DEBUG_PRXQ_MASK			0x7fff
#define MTL_RXQ0_DEBUG_RXQSTS_SHIFT		4
#define MTL_RXQ0_DEBUG_RXQSTS_MASK			3

#define MAC_RXQ_CTRL0_RXQ0EN_SHIFT			0
#define MAC_RXQ_CTRL0_RXQ0EN_MASK			3

#define MAC_HW_FEATURE1_TXFIFOSIZE_SHIFT		6
#define MAC_HW_FEATURE1_TXFIFOSIZE_MASK		0x1f
#define MAC_HW_FEATURE1_RXFIFOSIZE_SHIFT		0
#define MAC_HW_FEATURE1_RXFIFOSIZE_MASK		0x1f

#define GMII_PHY_ADDR_SHIFT (21)
#define GMII_PHY_ADDR_MASK  (0x1f)      /* Bit 21-25 mask */

#define GMII_REG_ADDR_SHIFT (16)
#define GMII_REG_ADDR_MASK  (0x1f)      /* Bit 16-20 mask */

#define GMII_CSR_CLK_SHIFT  (8)
#define GMII_CSR_CLK_MASK   (0xf)       /* Bit 8-11 mask */
#define GMII_CSR_150_250M   (0x4)       /* 0100:CSR ckicj = 150-250MHz */

#define GMII_ADDR_READ      (3U << 2)   /* MII Read, bit 3:2 11 */
#define GMII_ADDR_WRITE     (1U << 2)   /* GW MII Write, bit 3:2 10*/

#define GMII_ADDR_BUSY      (1 << 0)    /* GB MII Busy */
#define MDIO_BUSY_WAIT_TIMEOUT 10000U    /* mdio busy wait timeout (count) */

#define GMII_ADDR_CSR_60_100_MHZ    (0)
#define GMII_ADDR_CSR_100_150_MHZ   (1)
#define GMII_ADDR_CSR_20_35_MHZ     (2)
#define GMII_ADDR_CSR_35_60_MHZ     (3)
#define GMII_ADDR_CSR_150_250_MHZ   (4)
#define GMII_ADDR_CSR_250_300_MHZ   (5)
#define GMII_ADDR_CSR_300_500_MHZ   (6)

#define ETH_MAX_DMA_CHANNEL 1
#define MAC_AN_STATUS           0x000000e4
#define MAC_PMT_CONTROL_STATUS  0x000000c0
#define MAC_LPI_CONTROL_STATUS  0x000000d0
#define MTL_INT_STATUS          0x00000c20
#define MTL_CHAN_INT_CTRL(x)        (0x2c)


#define MMC_RX_INTERRUPT_MASK               0x70C
#define MMC_TX_INTERRUPT_MASK               0x710
#define MMC_IPC_RX_INTERRUPT_MASK           0x800

#define SIOCSIFPHYREAD						0xFFFF0000
#define SIOCSIFPHYWRITE						0xFFFF0001

struct phy_status
{
	unsigned int phy_addr;
	unsigned int reg_addr;
	unsigned int val;
	int result;
};

#endif  /* __DWMAC4_H__ */
