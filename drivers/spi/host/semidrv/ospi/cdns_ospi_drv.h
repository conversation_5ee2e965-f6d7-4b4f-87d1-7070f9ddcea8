/**
 * @file cdns_ospi_drv.h
 * @brief Cadence ospi driver header.
 *
 * @copyright Copyright (c) 2021  Semidrive Semiconductor.
 *            All rights reserved.
 */

#ifndef CDNS_OSPI_DRV_H_
#define CDNS_OSPI_DRV_H_

#ifdef __cplusplus
extern "C" {
#endif
#include <ttos.h>
#include <sys/types.h>
//#include <cmsis_os2.h>
#include <spi_nor.h>

#define CONFIG_OSPI	1

typedef MUTEX_ID osMutexId_t;
typedef void *osEventFlagsId_t;
typedef void *osEventFlagsAttr_t;

typedef void* osMutexAttr_t;

#define osStatus_t	int
#define osWaitForever         TTOS_MUTEX_WAIT_FOREVER ///< Wait forever timeout value.
// Flags options (\ref osThreadFlagsWait and \ref osEventFlagsWait).
#define osFlagsWaitAny        0x00000000U ///< Wait for any flag (default).
#define osFlagsWaitAll        0x00000001U ///< Wait for all flags.
#define osFlagsNoClear        0x00000002U ///< Do not clear flags which have been specified to wait for.


uint32_t osEventFlagsSet (osEventFlagsId_t ef_id, uint32_t flags);
osStatus_t osMutexRelease (osMutexId_t mutex_id);
uint32_t osEventFlagsSet (osEventFlagsId_t ef_id, uint32_t flags);;
osStatus_t osMutexAcquire (osMutexId_t mutex_id, uint32_t timeout);
osStatus_t osMutexRelease (osMutexId_t mutex_id);
uint32_t osEventFlagsWait (osEventFlagsId_t ef_id, uint32_t flags, uint32_t options, uint32_t timeout);
uint32_t osEventFlagsClear (osEventFlagsId_t ef_id, uint32_t flags);

osMutexId_t osMutexNew (const osMutexAttr_t *attr);
osEventFlagsId_t osEventFlagsNew (const osEventFlagsAttr_t *attr);
const char *osEventFlagsGetName (osEventFlagsId_t ef_id);
uint32_t osEventFlagsGet (osEventFlagsId_t ef_id);
void irq_enable(uint32_t irq);
void irq_disable(uint32_t irq);
int irq_attach(uint32_t irq, void* handler, void *arg);


enum cospi_flash_phy_mode {
    COSPI_PHY_NONE = 0,
    COSPI_PHY_BYPASS_DLL,
    COSPI_PHY_MASTER_DLL,
};

enum cospi_xfer_mode {
    COSPI_XFER_NONE = 0,
    COSPI_XFER_READ,
    COSPI_XFER_WRITE,
};

struct cospi_config {
    uint8_t id;
    addr_t apb_base;
    addr_t ahb_base;
    unsigned int irq;
    unsigned long ref_clk;
};

struct cospi_pdata {
#if 1
    uint8_t id;
    addr_t apb_base;
    addr_t ahb_base;
    unsigned int irq;
    unsigned long ref_clk_hz;
#endif

    unsigned int sclk;
    uint8_t dma_bus_width;
    uint32_t dma_burst_size;
    uint32_t dma_max_burst;
    bool cs_decoded;
    uint8_t current_cs;
    uint8_t block_power_index;
    uint32_t page_size;
    bool dqs_en;
    bool rclk_loopback;
    uint8_t master_delay;
    uint8_t capture_delay;

    uint8_t *xfer_buf;
    uint32_t remaining_size;

    uint8_t inst_type;
    uint8_t addr_bytes;
    uint8_t sram_size_nbit;
    uint8_t fifo_depth;
    uint8_t fifo_width;
    uint32_t trigger_address;
    uint32_t trigger_range_size;
    enum cospi_flash_phy_mode phy_mode;
    bool phy_training_succ;

    osMutexId_t bus_mutex;
    osEventFlagsId_t bus_event;
};

int cospi_host_init(struct spi_nor_host *host, struct cospi_pdata *cospi,
                    struct cospi_config *config);

#ifdef __cplusplus
}
#endif
#endif /* CDNS_OSPI_DRV_H_ */

