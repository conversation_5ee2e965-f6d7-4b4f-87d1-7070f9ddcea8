#include "cdns_ospi_drv.h"
#include <ttos.h>
#define osOK 0
osMutexId_t osMutexNew (const osMutexAttr_t *attr) 
{
	MUTEX_ID mutexID;
	TTOS_CreateMutex (1, 0, &mutexID);
	return mutexID;
}

osStatus_t osMutexRelease (osMutexId_t mutex_id)
{
	TTOS_ReleaseMutex (mutex_id);
	return osOK;
}

osStatus_t osMutexAcquire (osMutexId_t mutex_id, uint32_t timeout)
{
	TTOS_ObtainMutex(mutex_id, timeout);//TTOS_MUTEX_WAIT_FOREVER
	return osOK;
}


uint32_t osEventFlagsSet (osEventFlagsId_t ef_id, uint32_t flags)
{
	//
	//printk("SSSSSSSSSS %s,%d\n",__FUNCTION__,__LINE__);
	*(int*)ef_id = flags;
	return 0;
}


uint32_t osEventFlagsWait (osEventFlagsId_t ef_id, uint32_t flags, uint32_t options, uint32_t timeout)
{
	//
	printk("SSSSSSSSSS %s,%d\n",__FUNCTION__,__LINE__);
	return 0;
}

uint32_t osEventFlagsClear (osEventFlagsId_t ef_id, uint32_t flags) 
{
	//
	//printk("SSSSSSSSSS %s,%d\n",__FUNCTION__,__LINE__);
	*(int*)ef_id = 0;
	return 0;
}



osEventFlagsId_t osEventFlagsNew (const osEventFlagsAttr_t *attr)
{
	osEventFlagsId_t ret = 0;
	//printk("SSSSSSSSSS %s,%d\n",__FUNCTION__,__LINE__);
	osEventFlagsId_t * id = calloc(1, sizeof(int));
	return id;
}
const char *osEventFlagsGetName (osEventFlagsId_t ef_id)
{
	printk("SSSSSSSSSS %s,%d\n",__FUNCTION__,__LINE__);
	return NULL;
}
uint32_t osEventFlagsGet (osEventFlagsId_t ef_id)
{
	//printk("SSSSSSSSSS %s,%d\n",__FUNCTION__,__LINE__);
	return *(int*)ef_id;
}

void irq_enable(uint32_t irq)
{
    //irq_lld_enable(irq);
}

void irq_disable(uint32_t irq)
{
    //irq_lld_disable(irq);
}
int irq_attach(uint32_t irq, void* handler, void *arg)
{
	//
}
