
#ifndef __DBG_H__
#define __DBG_H__

extern int printk (const char *fmt, ...);

/* SSDK debug level */
#define SSDK_EMERG     0  /* System is unusable */
#define SSDK_ALERT     1  /* Action must be taken immediately */
#define SSDK_CRIT      2  /* Critical conditions */
#define SSDK_ERR       3  /* Error conditions */
#define SSDK_WARNING   4  /* Warning conditions */
#define SSDK_NOTICE    5  /* Normal, but significant, condition */
#define SSDK_INFO      6  /* Informational message */
#define SSDK_DEBUG     7  /* Debug-level message */

#define DBG(fmt, args...)	//printk(fmt, ##args)


#if defined(INFO_LEVEL)
#define INFO(fmt, args...) printk(fmt, ##args)
#define WARN(fmt, args...) printk(fmt, ##args)
#define FATAL(fmt, args...) printk(fmt, ##args)
#else
#define INFO(fmt, args...)  DBG(fmt, ##args)
#define WARN(fmt, args...)  DBG(fmt, ##args)
#define FATAL(fmt, args...)  printk(fmt, ##args)//DBG(fmt, ##args)
#endif

#define ssdk_printf(level, fmt, args...)	if(level < SSDK_WARNING) printk(fmt, ##args);

#endif

