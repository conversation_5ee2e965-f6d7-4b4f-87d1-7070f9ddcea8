#ifndef __SEMIDRV_QSPI_H__
#define __SEMIDRV_QSPI_H__

#include <ttos.h>
#include "cdns_ospi_drv.h"

#define NORFALSH_GET_RW_OFFSET      (0UL)
#define NORFALSH_SET_RW_OFFSET      (1UL)
#define NORFALSH_ERASE_BLOCK_64KB   (2UL)

typedef struct ospi_cltr
{
	struct spi_nor nor;
	struct cospi_pdata ospi;
	struct spi_nor_host snhost;
}ospi_cltr_t;

typedef struct sdrv_qspi_flash
{
	void* dev;	/* 设备 */
	ospi_cltr_t ctrl;
	MUTEX_ID lock;
	char *tmpdata;
	uintptr_t apb;
	uintptr_t ahb;
	uintptr_t cs;					/* 片选 */
	uintptr_t fl_size;				/* flash 大小 */
    char name[32];                                  /**< flash chip name */
    uint8_t mf_id;                               /**< manufacturer ID */
    uint8_t type_id;                             /**< memory type ID */
    uint8_t capacity_id;                         /**< capacity ID */
    uint32_t capacity;                           /**< flash capacity (bytes) */
    uint16_t write_mode;                         /**< write mode @see sfud_write_mode */
    uint32_t erase_gran;                         /**< erase granularity (bytes) */
    uint8_t erase_gran_cmd;                      /**< erase granularity size block command */
}sdrv_qspi_flash_t;





#endif /* __SEMIDRV_QSPI_H__ */
