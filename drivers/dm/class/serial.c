/**
 * @file    drivers/dm/class/serial.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 83800ccc 2024-07-09 增加serial_register 以管理minor号
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <driver/class/chardev.h>
#include <driver/device.h>
#include <driver/of.h>
#include <errno.h>
#include <stdio.h>

static int g_serial_minor = 0;

int serial_register(struct device *dev)
{
    char *path;
    const char *eupath;
    struct char_class_type *class = (struct char_class_type *)dev->class;
    int ret;
    dev->minor = g_serial_minor++;
    asprintf(&path, "/dev/ttyS%d", dev->minor);
    if (path == NULL)
    {
        return -ENOMEM;
    }

    eupath = earlycon_uart_of_path();
#ifdef __x86_64__
    if (eupath && !strcmp(eupath, path))
#else
    if (eupath && !strcmp(eupath, dev->of_node->full_name))
#endif
    {
        class->isconsole = true;
    }
    else
    {
        class->isconsole = false;
    }

    ret = char_register(path, dev);

    if (class->isconsole)
    {
        symlink(path, "/dev/console");
    }
    free(path);
    return ret;
}
