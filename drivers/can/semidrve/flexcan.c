
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include <fs/fs.h>
#include <ttosMM.h>
#include <wqueue.h>
#include <uaccess.h>
#include <inttypes.h>
#include <ttos_pic.h>
#include <fs/ioctl.h>
#include <driver/of.h>
#include <ttos_init.h>
#include <barrier.h>
#include <driver/driver.h>
#include <driver/device.h>
#include <driver/devbus.h>
#include <io.h>
#include "../can.h"
#include "flexcan.h"

#undef KLOG_TAG
#define KLOG_TAG "FlexcanDrv"
#include <klog.h>



/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

#define CAN_FRAME_TYPE      0
#define CANFD_FRAME_TYPE    1


/* If processing is not done at the interrupt level, then work queue support
 * is required.
 */

#define CANWORK                     (&default_work_queue)
#define CANRCVWORK                  (&high_pri_work_queue)

#define RXMBCOUNT                   CONFIG_FLEXCAN_RXMB
#define TXMBCOUNT                   CONFIG_FLEXCAN_TXMB
#define TOTALMBCOUNT                (RXMBCOUNT + TXMBCOUNT)

#define IFLAG1_RX                   ((1 << RXMBCOUNT)-1)
#define IFLAG1_TX                   (((1 << TXMBCOUNT)-1) << RXMBCOUNT)

#define POOL_SIZE                   1

#define MSG_DATA                    sizeof(struct timeval)

#define TSEG_MIN                    2

/* Classical can CTRL1 bit timing */

#define TSEG1_MAX                   16
#define TSEG2_MAX                   8
#define NUMTQ_MIN                   8
#define NUMTQ_MAX                   25

/* CAN FD CBT and FDCBT bit timing */

#define TSEG1_FD_MAX                96
#define TSEG2_FD_MAX                32
#define NUMTQ_FD_MIN                8
#define NUMTQ_FD_MAX                129

#define TSEG1_FD_DATAMAX            39
#define TSEG2_FD_DATAMAX            8
#define NUMTQ_FD_DATAMIN            5
#define NUMTQ_FD_DATAMAX            48


#define DEBUGASSERT(x)             if (!(x)) {KLOG_E("Assert failed. %s:%d", __FILE__, __LINE__);}


# if (CONFIG_TRANS_RECV_QUEUE_NUM == 0)
# error "CONFIG_TRANS_RECV_QUEUE_NUM must be greater than zero "
#endif

#if (CONFIG_FLEXCAN_RXMB + CONFIG_FLEXCAN_TXMB) > 21
# error Only 21 MB are allowed to be used
#endif

#ifdef CONFIG_FLEXCAN_SUPPORT_CANFD
#  define FRAME_POOL_SIZE ((sizeof(struct canfd_frame) + MSG_DATA) * POOL_SIZE)
#else
#  define FRAME_POOL_SIZE (sizeof(struct can_frame) * POOL_SIZE)
#endif


#define FLECAN_ERRSETS      (CAN_ESR1_WAKINT | CAN_ESR1_ERRINT | CAN_ESR1_BOFFINT | \
                             CAN_ESR1_TWRNINT | CAN_ESR1_RWRNINT | CAN_ESR1_BIT0ERR | \
                             CAN_ESR1_BIT1ERR | CAN_ESR1_ACKERR | CAN_ESR1_CRCERR | \
                             CAN_ESR1_FRMERR | CAN_ESR1_STFERR)


/* 拟实现一种模拟SocketCan通过error message frame向用户态报告硬件busoff等状态的机制，待完善 */
//#define CONFIG_OPEN_ERROR_MESG_FRAME

#define TRANS_MAX_TIMEOUT            5 /* 5 ms*/



/*
 * Ioctl command sets
 */
#define _FLEXCAN_IOC_MAGIC_                   'C'
#define IOCTL_CAN_SET_MODE                    _IOWR(_FLEXCAN_IOC_MAGIC_, 1, enum work_status)
#define IOCTL_CAN_SET_LOOPBACK                _IOWR(_FLEXCAN_IOC_MAGIC_, 2, enum can_loopback_cfg)
#define IOCTL_CAN_SET_QUEUE                   _IOWR(_FLEXCAN_IOC_MAGIC_, 3, unsigned long)
#define IOCTL_CAN_CLEAR_QUEUE                 _IOWR(_FLEXCAN_IOC_MAGIC_, 4, void *)
#define IOCTL_CAN_GET_HWREPORT                _IOWR(_FLEXCAN_IOC_MAGIC_, 5, struct can_ioctl_hwreport_s)
#define IOCTL_CAN_GET_BITRATE                 _IOWR(_FLEXCAN_IOC_MAGIC_, 6, struct can_ioctl_bitrate_s)
#define IOCTL_CAN_SET_BITRATE                 _IOWR(_FLEXCAN_IOC_MAGIC_, 7, struct can_ioctl_bitrate_s)
#define IOCTL_CAN_GET_BITRATE_FINE_TUNING     _IOWR(_FLEXCAN_IOC_MAGIC_, 8, struct can_ioctl_forcetiming_s)
#define IOCTL_CAN_SET_BITRATE_FINE_TUNING     _IOWR(_FLEXCAN_IOC_MAGIC_, 9, struct can_ioctl_forcetiming_s)

#ifndef _IOC_SIZE
# define _IOC_SIZESHIFT          16
# define _IOC_SIZEMASK           0x1FFF
#define _IOC_SIZE(cmd)            (((cmd) >> _IOC_SIZESHIFT) & _IOC_SIZEMASK)
#endif


/****************************************************************************
 * Private Types
 ****************************************************************************/

enum work_status
{
    CFG_STA = 0,
    RUN_STA,
};


enum can_loopback_cfg
{
    CAN_DISABLE_ALL = 0x01,
    CAN_LOOPBACK = 0x02,
    CAN_SELF_RECEPT = 0x04,
};


struct can_ioctl_bitrate_s
{
    uint32_t arbi_bitrate; /* Classic CAN / Arbitration phase bitrate bit/s */
    uint32_t data_bitrate; /* Data phase bitrate bit/s */
    uint16_t arbi_samplep; /* Classic CAN / Arbitration phase input % */
    uint16_t data_samplep; /* Data phase sample point % */
};


struct can_ioctl_filter_s
{
  uint32_t fid1;  /* 11- or 29-bit ID (context dependent).  For dual match or
                   * for the lower address in a range of addresses  */
  uint32_t fid2;  /* 11- or 29-bit ID.  For dual match, address mask or for
                   * upper address in address range  */
  uint8_t  ftype; /* See CAN_FILTER_* definitions */
  uint8_t  fprio; /* See CAN_MSGPRIO_* definitions */
};


struct can_ioctl_hwreport_s
{
    uint32_t hw_ecr;
    uint32_t hw_esr;
    size_t sf_packetloss;
};


struct can_ioctl_forcetiming_s
{
    uint16_t arbi_presdiv;
    uint8_t arbi_jumpwidth;
    uint8_t arbi_propseg;
    uint8_t arbi_pseg1;
    uint8_t arbi_pseg2;

    uint16_t data_presdiv;
    uint8_t data_jumpwidth;
    uint8_t data_propseg;
    uint8_t data_pseg1;
    uint8_t data_pseg2;
};


struct mb_s
{
  volatile uint32_t cs;
  volatile uint32_t id;
  volatile uint32_t data[];
};


/* FlexCAN Device hardware configuration */
struct flexcan_timeseg
{
    uint32_t bitrate;
    int32_t samplep;
    uint16_t presdiv;
    uint8_t jumpwidth;
    uint8_t propseg;
    uint8_t pseg1;
    uint8_t pseg2;
};


struct flexcan_descp
{
    volatile uint8_t own;           // 1: 脏
    uint8_t type;                   //  0:can frame   1:canfd frame
    uint8_t frame[FRAME_POOL_SIZE];
};


struct flexcan_rb
{
    uint32_t num;                 /* num */
    uint32_t rp;                  /* Read pointer */
    uint32_t wp;                  /* Write pointer */
    struct flexcan_descp *desp;   /* A pointer to the buffer of RX descriptors */
};


/* The flexcan_driver_s encapsulates all state information for a single
 * hardware interface
 */

struct flexcan_driver_s
{
    uint8_t name[32];
    devaddr_region_t region[1];   /* flexcan address info */
    uintptr_t base;               /* register base address (VA) */
    uint32_t irq;                 /* irq number */
    uint32_t clk_freq;            /* Peripheral clock frequency */
    bool loopback;                /* loopback true:enable false:disable */
    bool selfrecept;              /* Self-Reception true:enable false:disable */
    bool canfd_capable;
    enum work_status cursta;

    SEMA_ID  txsema;
#ifdef CONFIG_TX_QUEUE
    struct flexcan_rb txrb;
#endif
    struct flexcan_rb rxrb;

    struct work_s rcvwork;              /* For deferring interrupt work to the wq */
    struct work_s irqwork;              /* For deferring interrupt work to the wq */
    struct work_s pollwork;             /* For deferring poll work to the work wq */
    struct work_s errwork;

    struct flexcan_timeseg arbi_timing; /* Timing for arbitration phase */
    struct flexcan_timeseg data_timing; /* Timing for data phase */

    size_t packetloss;                  /* Packet loss number */

};



/****************************************************************************
 * Private Data
 ****************************************************************************/
extern struct wqueue default_work_queue;
extern struct wqueue high_pri_work_queue;
extern const uint8_t g_can_dlc_to_len[16];
extern const uint8_t g_len_to_can_dlc[65];

static DEFINE_SPINLOCK (flex_reglock);

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static int flexcan_desp_init (struct flexcan_rb *rb, size_t num);
static int flexcan_desp_clear (struct flexcan_rb *rb);
static int flexcan_desp_free (struct flexcan_rb *rb);
static int flexcan_write_desp (struct flexcan_rb *rb, struct mb_s *rf);
static int flexcan_read_desp (struct flexcan_rb *rb, void *pbuf, size_t len);
    
/* Common TX logic */
static bool flexcan_txringfull(struct flexcan_driver_s *priv);
static int flexcan_transmit(struct flexcan_driver_s *priv, const uint8_t *buffer, uint8_t nbytes);

/* Helper functions */
static bool flexcan_setenable(uintptr_t base, bool enable);
static bool flexcan_setfreeze(uintptr_t base, bool freeze);
static bool flexcan_waitmcr_change(uintptr_t base, uint32_t mask, bool target_state);
static volatile struct mb_s *flexcan_get_mb(struct flexcan_driver_s *priv, int mbi);

/* Interrupt handling */
static void flexcan_rxdone_work(void *arg);
static void flexcan_receive(struct flexcan_driver_s *priv);
static void flexcan_txdone(struct flexcan_driver_s *priv, uint32_t flags);
static void  flexcan_flexcan_interrupt(uint32_t irq, void *arg);

/* callback functions */
static int flexcan_ifup(struct flexcan_driver_s *dev);
static int flexcan_ifdown(struct flexcan_driver_s * priv);
static int flexcan_ioctl_handler(struct flexcan_driver_s *dev, int cmd,
                      unsigned long arg);

/* Initialization */
static int  flexcan_initialize(struct flexcan_driver_s *priv);
static void flexcan_reset(struct flexcan_driver_s *priv);

/****************************************************************************
 * Private Functions
 ****************************************************************************/

static void modifyreg32(uintptr_t addr, uint32_t clearbits, uint32_t setbits)
{
    long       flags;
    uint32_t   regaddr;
    uint32_t   regval;

    regaddr = (uint32_t)addr;

    spin_lock_irqsave(&flex_reglock, flags);
    regval  = readl(addr);
    regval &= ~clearbits;
    regval |= setbits;
    writel(regval, addr);
    spin_unlock_irqrestore(&flex_reglock, flags);
}



/****************************************************************************
 * Name: flexcan_get_rootclock
 *
 * Description:
 *   Get bus clock going into peripherals.
 *
 ****************************************************************************/
static void flexcan_get_rootclock(uint32_t *clk)
{
    *clk = (uint32_t) CONFIG_FLEXCAN_BUS_CLK;
    return;
}


/****************************************************************************
 * Name: flexcan_bitratetotimeseg
 *
 * Description:
 *   Convert bitrate to timeseg
 *
 * Input Parameters:
 *   timeseg - structure to store bit timing
 *                  bit timings (recommended value: 1)
 *   can_fd_data - if set to calculate CAN FD data bit timings,
 *                  otherwise calculate classical or arbitration can
 *                  timings
 *
 * Returned Value:
 *   return 0 on success, negated error number on failure
 *
 ****************************************************************************/

static uint32_t flexcan_bitratetotimeseg(struct flexcan_driver_s *priv,
                                      struct flexcan_timeseg *timeseg,
                                      bool can_fd_data)
{
#if defined(CONFIG_FLEXCAN_SUPPORT_CANFD)
    /* Max SEG1 & SEG2 values in TQ */

    const int32_t TSEG1MAX = can_fd_data ? TSEG1_FD_DATAMAX : TSEG1_FD_MAX;
    const int32_t TSEG2MAX = can_fd_data ? TSEG2_FD_DATAMAX : TSEG2_FD_MAX;

    /* Min and max bit length in TQ */

    const int32_t NUMTQMIN = can_fd_data ? NUMTQ_FD_DATAMIN : NUMTQ_FD_MIN;
    const int32_t NUMTQMAX = can_fd_data ? NUMTQ_FD_DATAMAX : NUMTQ_FD_MAX;

    /* Max register field values */

    /* Max register field value for presdiv */

    const uint32_t PRESDIVMAX = can_fd_data ?
    CAN_FDCBT_FPRESDIV_MASK >> CAN_FDCBT_FPRESDIV_SHIFT :
    CAN_CBT_EPRESDIV_MASK >> CAN_CBT_EPRESDIV_SHIFT;

    /* Max register field values from PSEG and PROPSEG */

    const int32_t PSEGMAX = can_fd_data ?
    CAN_FDCBT_FPSEG1_MASK >> CAN_FDCBT_FPSEG1_SHIFT :
    CAN_CBT_EPSEG1_MASK >> CAN_CBT_EPSEG1_SHIFT;
    const int32_t PROPSEGMAX = can_fd_data ?
    (CAN_FDCBT_FPROPSEG_MASK >> CAN_FDCBT_FPROPSEG_SHIFT) - 1 :
    CAN_CBT_EPROPSEG_MASK >> CAN_CBT_EPROPSEG_SHIFT;
#else
    /* Max SEG1 & SEG2 values in TQ */

    const int32_t TSEG1MAX = TSEG1_MAX;
    const int32_t TSEG2MAX = TSEG2_MAX;

    /* Min and max bit length in TQ */

    const int32_t NUMTQMIN = NUMTQ_MIN;
    const int32_t NUMTQMAX = NUMTQ_MAX;

    /* Max register field values */

    /* Max register field value for presdiv */

    const uint32_t PRESDIVMAX = CAN_CTRL1_PRESDIV_MASK >> CAN_CTRL1_PRESDIV_SHIFT;

    /* Max register field values from PSEG and PROPSEG */

    const int32_t PSEGMAX = CAN_CTRL1_PSEG1_MASK >> CAN_CTRL1_PSEG1_SHIFT;
    const int32_t PROPSEGMAX = CAN_CTRL1_PROPSEG_MASK >> CAN_CTRL1_PROPSEG_SHIFT;
#endif

    int32_t presdiv = PRESDIVMAX;
    int32_t tmppresdiv;
    int32_t numtq;
    int32_t tmpnumtq;
    int32_t tmpsample;
    int32_t tseg1;
    int32_t tseg2;
    int32_t tmppseg1;
    int32_t tmppseg2;
    int32_t tmppropseg;
    int32_t bitrate = 0;

    int32_t bitrate_tmp = 0;
    int32_t bitrate_err = INT32_MAX;

    for (tmppresdiv = 0; tmppresdiv < PRESDIVMAX; tmppresdiv++)
    {
        tmpnumtq = (priv->clk_freq / ((tmppresdiv + 1) * timeseg->bitrate));

        /* if number of time quanta per bit is too high, continue */

        if (tmpnumtq > NUMTQMAX)
        {
            continue;
        }

        /* if number of time quanta per bit is too small, break out */

        if (tmpnumtq < NUMTQMIN)
        {
            break;
        }

        bitrate_tmp = priv->clk_freq / ((tmppresdiv + 1) * tmpnumtq);
        if (abs(bitrate - bitrate_tmp) < bitrate_err)
        {
            bitrate_err = abs(bitrate - bitrate_tmp);
            bitrate = bitrate_tmp;
            numtq = tmpnumtq;
            presdiv = tmppresdiv;
        }
    }

    if (bitrate != timeseg->bitrate)
    {
        KLOG_W("bitrate set to %" PRId32 " instead of %" PRId32 "\n",
                bitrate, timeseg->bitrate);
    }

    /* Compute time segments based on the value of the sampling point */

    tseg1 = (numtq * timeseg->samplep / 100) - 1;
    tseg2 = numtq - 1 - tseg1;

    /* Adjust time segment 1 and time segment 2 */

    while (tseg1 >= TSEG1MAX || tseg2 < TSEG_MIN)
    {
        tseg2++;
        tseg1--;
    }

    if (tseg1 > TSEG1MAX || tseg2 > TSEG2MAX ||
        tseg2 < TSEG_MIN || tseg1 < TSEG_MIN)
    {
        KLOG_E("tseg1 %" PRId32 ", max %" PRId32 "\n", tseg1, TSEG1MAX);
        KLOG_E("tseg2 %" PRId32 ", max %" PRId32 "\n", tseg2, TSEG2MAX);
        return -EINVAL;
    }

    DEBUGASSERT(1 + tseg1 + tseg2 == numtq);

    tmppseg2 = tseg2 - 1;

    /* Start from pseg1 = pseg2 and adjust until propseg is valid */

    tmppseg1 = tmppseg2;
    tmppropseg = tseg1 - tmppseg1 - 2;

    while (tmppropseg <= 0)
    {
        tmppropseg++;
        tmppseg1--;
    }

    while (tmppropseg >= PROPSEGMAX)
    {
        tmppropseg--;
        tmppseg1++;
    }

    if (tmppseg1 > PSEGMAX || tmppseg2 > PSEGMAX)
    {
        KLOG_E("tmppseg1 %" PRId32 ", max %" PRId32 "\n", tmppseg1, PSEGMAX);
        KLOG_E("tmppseg2 %" PRId32 ", max %" PRId32 "\n", tmppseg2, PSEGMAX);
        return -EINVAL;
    }

    tmpsample = (1 + tseg1) * 100 / numtq;

    /* Allow 5% tolerance in sample point */

    if (abs(tmpsample - timeseg->samplep) <= 5)
    {
        if (can_fd_data)
        {
            timeseg->propseg = tmppropseg + 1;
        }
        else
        {
            timeseg->propseg = tmppropseg;
        }

        timeseg->pseg1 = tmppseg1;
        timeseg->pseg2 = tmppseg2;
        timeseg->presdiv = presdiv;
        timeseg->samplep = tmpsample;
        
        if (can_fd_data)
        {
            /* Canfd data timing: resynchorinzation jump width same as PSEG2 */
            timeseg->jumpwidth = timeseg->pseg2;
        }
        else{
            /* Arbitration timing: jump width == 1 */
            timeseg->jumpwidth = 1;
        }

        return 0;
    }

    KLOG_E("sample point %" PRId32 ", configured %" PRId32 "\n",
            tmpsample, timeseg->samplep);

    return -EINVAL;
}


/****************************************************************************
 * Name: flexcan_forcetimeseg
 *
 * Description:
 *   Force arbitration and data timing
 *
 * Input Parameters:
 *   timing - structure to store bit timing
 *
 * Returned Value:
 *   return 0 on success, negated error number on failure
 *
 ****************************************************************************/

static int flexcan_forcetimeseg(struct flexcan_driver_s *priv,
                                     struct can_ioctl_forcetiming_s *timing)
{
    priv->arbi_timing.jumpwidth = timing->arbi_jumpwidth;
    priv->arbi_timing.presdiv = timing->arbi_presdiv;
    priv->arbi_timing.propseg = timing->arbi_propseg;
    priv->arbi_timing.pseg1 = timing->arbi_pseg1;
    priv->arbi_timing.pseg2 = timing->arbi_pseg2;
    
    priv->data_timing.jumpwidth = timing->data_jumpwidth;
    priv->data_timing.presdiv = timing->data_presdiv;
    priv->data_timing.propseg = timing->data_propseg;
    priv->data_timing.pseg1 = timing->data_pseg1;
    priv->data_timing.pseg2 = timing->data_pseg2;

    return 0;
}


/****************************************************************************
 * Name: flexcan_get_forcetimeseg
 *
 * Description:
 *   Get force arbitration and data timing
 *
 * Input Parameters:
 *   timing - structure to store bit timing
 *
 * Returned Value:
 *   return 0 on success, negated error number on failure
 *
 ****************************************************************************/

static int flexcan_get_forcetimeseg(struct flexcan_driver_s *priv,
                                     struct can_ioctl_forcetiming_s *timing)
{
    timing->arbi_jumpwidth = priv->arbi_timing.jumpwidth;
    timing->arbi_presdiv = priv->arbi_timing.presdiv;
    timing->arbi_propseg = priv->arbi_timing.propseg;
    timing->arbi_pseg1 = priv->arbi_timing.pseg1;
    timing->arbi_pseg2 = priv->arbi_timing.pseg2;

    timing->data_jumpwidth = priv->data_timing.jumpwidth;
    timing->data_presdiv = priv->data_timing.presdiv;
    timing->data_propseg = priv->data_timing.propseg;
    timing->data_pseg1 = priv->data_timing.pseg1;
    timing->data_pseg2 = priv->data_timing.pseg2;

    return 0;
}


/****************************************************************************
 * Function: flexcan_txringfull
 *
 * Description:
 *   Check if all of the TX descriptors are in use.
 *
 * Input Parameters:
 *   priv  - Reference to the driver state structure
 *
 * Returned Value:
 *   true is the TX ring is full; false if there are free slots at the
 *   head index.
 *
 ****************************************************************************/

static bool flexcan_txringfull(struct flexcan_driver_s *priv)
{
    int mbi;

    for (mbi = RXMBCOUNT; mbi < TOTALMBCOUNT; mbi++)
    {
        volatile struct mb_s *mb = flexcan_get_mb(priv, mbi);
        if (CAN_MB_CS_CODE(mb->cs) != CAN_TXMB_DATAORREMOTE)
        {
            return false;
        }
    }

    return true;
}

/****************************************************************************
 * Function: flexcan_transmit
 *
 * Description:
 *   Start hardware transmission.  Called either from the txdone interrupt
 *   handling or from watchdog based polling.
 *
 * Input Parameters:
 *   priv  - Reference to the driver state structure
 *
 * Returned Value:
 *   0 on success; a negated errno on failure
 *
 * Assumptions:
 *   May or may not be called from an interrupt handler.  In either case,
 *   global interrupts are disabled, either explicitly or indirectly through
 *   interrupt handling logic.
 *
 ****************************************************************************/

static int flexcan_transmit(struct flexcan_driver_s *priv, const uint8_t *buffer, uint8_t nbytes)
{
    volatile struct mb_s *mb;
    uint32_t mbi = 0;
    uint32_t *frame_data_word;
    uint32_t i;
    uint32_t cs = 0;
    canid_t can_id;
    uint32_t can_dlc;
    uint8_t len;

    for (mbi = RXMBCOUNT; mbi < TOTALMBCOUNT; mbi++)
    {
        /* Check whether message buffer is not currently transmitting */

        mb = flexcan_get_mb(priv, mbi);
        if (CAN_MB_CS_CODE(mb->cs) != CAN_TXMB_DATAORREMOTE)
        {
            break;
        }
    }

    if (mbi >= TOTALMBCOUNT)
    {
        KLOG_W("No TX MB available mbi %" PRIi32 "\n", mbi);
        return (-EAGAIN);
    }

    if (nbytes == sizeof(struct can_frame))
    {
        struct can_frame *frame = (struct can_frame *)buffer;
        can_id = frame->can_id;
        len = CAN_MAX_DLEN;
        can_dlc = frame->can_dlc;
        if (can_dlc > CAN_MAX_DLEN)
        {
            can_dlc = CAN_MAX_DLEN;
        }
        frame_data_word = (uint32_t *)&frame->data[0];
    }
#ifdef CONFIG_FLEXCAN_SUPPORT_CANFD
  else if (nbytes == sizeof(struct canfd_frame))
    {
      struct canfd_frame *frame = (struct canfd_frame *)buffer;
      cs |= CAN_MB_CS_EDL;
      cs |= frame->flags & CANFD_BRS ? CAN_MB_CS_BRS : 0;
      can_id = frame->can_id;
      len = frame->len;
      can_dlc = g_len_to_can_dlc[len];
      frame_data_word = (uint32_t *)&frame->data[0];
    }
#endif
    else {
        return (-EINVAL);
    }

    if (can_id & CAN_EFF_FLAG)
    {
        cs |= CAN_MB_CS_IDE;
        mb->id = can_id & CAN_MB_ID_ID_MASK;
    }
    else
    {
        mb->id = ((can_id & CAN_SFF_MASK) << CAN_MB_ID_ID_STD_SHIFT) &
        CAN_MB_ID_ID_STD_MASK;
    }

    cs |= (can_id & CAN_RTR_FLAG) ? CAN_MB_CS_RTR : 0;
    cs |= (can_dlc << CAN_MB_CS_DLC_SHIFT) & CAN_MB_CS_DLC_MASK;

    for (i = 0; i < (len + 4 - 1) / 4; i++)
    {
        mb->data[i] = __builtin_bswap32(frame_data_word[i]);
    }

    /* Go */
    cs |= CAN_TXMB_DATAORREMOTE << CAN_MB_CS_CODE_SHIFT;
    mb->cs = cs;

    return 0;
}


/****************************************************************************
 * Function: flexcan_get_oldest_mbi
 *
 * Description:
 *   Find the oldest MB in the message buffers, based on timestamp
 *
 * Input Parameters:
 *   priv  - Reference to the driver state structure
 *   flags - Bitmask of MBs which should be checked
 *
 * Returned Value:
 *   index of the MB with oldest received data
 *
 * Assumptions:
 *   Always called with at least one RX buffer to be checked
 *
 ****************************************************************************/

static int flexcan_get_oldest_mbi(struct flexcan_driver_s *priv, uint32_t flags)
{
    int mbi;
    int oldest = -1;
    bool first = true;
    uint16_t t;
    uint16_t t_oldest;
    volatile struct mb_s *mb;

     /* When this is called, there is always at least one received buffer */

    DEBUGASSERT((flags & IFLAG1_RX) != 0);

    for (mbi = 0; mbi < RXMBCOUNT; mbi++)
    {
        if (flags & (1 << mbi))
        {
            mb = flexcan_get_mb(priv, mbi);
            t = CAN_MB_CS_TIMESTAMP(mb->cs);

            if ((int16_t)(t_oldest - t) > 0 || first)
            {
                first = false;
                t_oldest = t;
                oldest = mbi;
            }
        }
    }

    return oldest;
}

/****************************************************************************
 * Function: flexcan_receive
 *
 * Description:
 *   An interrupt was received indicating the availability of a new RX packet
 *
 * Input Parameters:
 *   priv  - Reference to the driver state structure
 *
 * Returned Value:
 *   None
 *
 * Assumptions:
 *   Global interrupts are disabled by interrupt handling logic.
 *
 ****************************************************************************/

static void flexcan_receive(struct flexcan_driver_s *priv)
{
    int ret;
    int mbi;
    volatile struct mb_s *rf;

    uint32_t flags = readl(priv->base + FLEX_CAN_IFLAG1_OFFSET);
    flags &= IFLAG1_RX;

    while (flags != 0)
    {
        mbi = flexcan_get_oldest_mbi(priv, flags);

        /* Make sure the MB is locked */
        do
        {
            rf = flexcan_get_mb(priv, mbi);
        }
        while ((CAN_MB_CS_CODE(rf->cs) & CAN_RXMB_BUSY_BIT) != 0);

        DEBUGASSERT(CAN_MB_CS_CODE(rf->cs) != CAN_RXMB_EMPTY);

        if (CAN_MB_CS_CODE(rf->cs) == CAN_RXMB_OVERRUN)
        {
            KLOG_W("RX overrun\n");
        }

        /* write to description */
        ret = flexcan_write_desp(&priv->rxrb, (struct mb_s *)rf);
        if (ret !=0 ){
            priv->packetloss++;
        }

        /* Clear MB interrupt flag */
        writel(1 << mbi, priv->base + FLEX_CAN_IFLAG1_OFFSET);

        /* Re-activate the buffer */
        rf->cs = (CAN_RXMB_EMPTY << CAN_MB_CS_CODE_SHIFT) | CAN_MB_CS_IDE;

        /* Re-enable interrupt */
        modifyreg32(priv->base + FLEX_CAN_IMASK1_OFFSET, 0, 1 << mbi);

        flags &= ~(1 << mbi);
    }
}

/****************************************************************************
 * Function: flexcan_txdone
 *
 * Description:
 *   Check transmit interrupt flags and clear them
 *
 * Input Parameters:
 *   priv  - Reference to the driver state structure
 *
 * Returned Value:
 *   None.
 *
 ****************************************************************************/

static void flexcan_txdone(struct flexcan_driver_s *priv, uint32_t flags)
{
    volatile struct mb_s *mb;
    uint32_t mbi;
    uint32_t mb_bit;
    int code;

    /* Process TX completions */
    for (mbi = RXMBCOUNT; mbi < TOTALMBCOUNT; mbi++)
    {
        mb_bit = 1 << mbi;
        if (flags & mb_bit)
        {
            mb = flexcan_get_mb(priv, mbi);
            code = CAN_MB_CS_CODE(mb->cs);

            /* Clear interrupt */

            writel(mb_bit, priv->base + FLEX_CAN_IFLAG1_OFFSET);

            /* After RTR transmission, the MB transitions into RX MB.
            * Check for RX empty w. busy bit or RX full - this should
            * not happen.
            */

            if (((code & ~CAN_RXMB_BUSY_BIT) == CAN_RXMB_EMPTY &&
                (code & CAN_RXMB_BUSY_BIT) != 0) ||
                code == CAN_RXMB_FULL)
            {
                /* Received something in this buffer?
                * This should only happen if we sent RTR and then did
                * run out of RX MBs (which are at lower indecies).
                * Or perhaps this shouldn't happen at all when AEN=1. This
                * is unclear in the RM.
                */
                KLOG_E("RCV in TX MB, code %x\n", code);
            }

            /* Only possible TX codes after transmission are ABORT or
            * INACTIVE. If it transitioned to RX MB after RTR sent,
            * inactivate it.
            */

            if (code != CAN_TXMB_ABORT && code != CAN_TXMB_INACTIVE)
            {
                mb->cs = CAN_TXMB_INACTIVE << CAN_MB_CS_CODE_SHIFT;
            }

            if (code == CAN_TXMB_ABORT)
            {
            }
            else
            {
            }
        }
    }
    TTOS_ReleaseSema (priv->txsema);
}


/****************************************************************************
 * Function: flexcan_rxdone_work
 *
 * Description:
 *   Three interrupt sources will vector this this function:
 *   1. CAN MB transmit interrupt handler
 *   2. CAN MB receive interrupt handler
 *   3.
 *
 * Input Parameters:
 *   irq     - Number of the IRQ that generated the interrupt
 *   context - Interrupt register state save info (architecture-specific)
 *
 * Returned Value:
 *   0 on success
 *
 * Assumptions:
 *
 ****************************************************************************/

static void flexcan_rxdone_work(void *arg)
{
    struct flexcan_driver_s *priv = (struct flexcan_driver_s *)arg;
    flexcan_receive(priv);
}



#ifdef CONFIG_OPEN_ERROR_MESG_FRAME
/*
 *  错误处理
 */
static void flexcan_exception_work(void *arg)
{
    uint8_t *data;
    uint32_t esr1;
    uint32_t errbits = 0;
    struct canfd_frame err_mesg_frame;
    struct flexcan_driver_s *priv = (struct flexcan_driver_s *)arg;
    
    data = &err_mesg_frame.data[0];
    memset(data, 0, sizeof(err_mesg_frame.data));

    esr1 = readl(priv->base + FLEX_CAN_ESR1_OFFSET);
    KLOG_E("ESR1 %x\n", esr1);
    KLOG_E("ERRSR: %x\n", readl(priv->base + FLEX_CAN_ERRSR_OFFSET));
    KLOG_E("RERRAR: %x\n", readl(priv->base + FLEX_CAN_RERRAR_OFFSET));

    errbits |= CAN_ERR_FLAG;

    if (esr1 & CAN_ESR1_BOFFINT)
    {
        errbits |= CAN_ERR_BUSOFF;
    }
    if (esr1 & (CAN_ESR1_TWRNINT | CAN_ESR1_TXWRN))
    {
        errbits |= CAN_ERR_CRTL;
        data[1] |= CAN_ERR_CRTL_TX_WARNING;
    }
    if (esr1 & (CAN_ESR1_RWRNINT | CAN_ESR1_RXWRN))
    {
        errbits |= CAN_ERR_CRTL;
        data[1] |= CAN_ERR_CRTL_RX_WARNING;
    }
    if (esr1 & CAN_ESR1_ATP)
    {
        /* Error passive flag */
        data[1] |= (CAN_ERR_CRTL_RX_PASSIVE | CAN_ERR_CRTL_TX_PASSIVE);
        errbits |= CAN_ERR_CRTL;
    }

    /* Last error code */
    if (esr1 & CAN_ESR1_ERRINT)
    {
        if (esr1 & CAN_ESR1_STFERR)
        {
            /* Stuff Error */
            errbits |= CAN_ERR_PROT;
            data[2] |= CAN_ERR_PROT_STUFF;
        }
        else if (esr1 & CAN_ESR1_FRMERR)
        {
            /* Format Error */
            errbits |= CAN_ERR_PROT;
            data[2] |= CAN_ERR_PROT_FORM;
        }
        else if (esr1 & CAN_ESR1_ACKERR)
        {
            /* Acknowledge Error */
            errbits |= CAN_ERR_ACK;
        }
        else if (esr1 & CAN_ESR1_BIT1ERR)
        {
            /* Bit recessive Error */
            errbits |= CAN_ERR_PROT;
            data[2] |= CAN_ERR_PROT_BIT1;
        }
        else if (esr1 & CAN_ESR1_BIT0ERR)
        {
            /* Bit dominant Error */
            errbits |= CAN_ERR_PROT;
            data[2] |= CAN_ERR_PROT_BIT0;
        }
        else if (esr1 & CAN_ESR1_CRCERR)
        {
            /* Receive CRC Error */
            errbits |= CAN_ERR_PROT;
            data[3] |= CAN_ERR_PROT_LOC_CRC_SEQ;
        }
    }

    /*
     * 写入接收缓冲
     */
    // ...

    /* Clear the interrupt */
    writel(esr1 & (CAN_ESR1_PTA | CAN_ESR1_ATP | CAN_ESR1_ERROVR |
                    CAN_ESR1_ERRINTFAST | CAN_ESR1_BOFFDONEINT |
                    CAN_ESR1_TWRNINT | CAN_ESR1_RWRNINT |
                    CAN_ESR1_BOFFINT | CAN_ESR1_ERRINT | CAN_ESR1_WAKINT),
            priv->base + FLEX_CAN_ESR1_OFFSET);
    
    return;
}

#endif 



/****************************************************************************
 * Function: flexcan_flexcan_interrupt
 *
 * Description:
 *   Three interrupt sources will vector this this function:
 *   1. CAN MB transmit interrupt handler
 *   2. CAN MB receive interrupt handler
 *   3.
 *
 * Input Parameters:
 *   irq     - Number of the IRQ that generated the interrupt
 *   context - Interrupt register state save info (architecture-specific)
 *
 * Returned Value:
 *   0 on success
 *
 * Assumptions:
 *
 ****************************************************************************/

static void flexcan_flexcan_interrupt(uint32_t irq, void *arg)
{
    struct flexcan_driver_s *priv = (struct flexcan_driver_s *)arg;
    uint32_t esr1;

    uint32_t flags = readl(priv->base + FLEX_CAN_IFLAG1_OFFSET);
    if (flags & IFLAG1_RX)
    {
        work_queue(&priv->rcvwork, flexcan_rxdone_work, priv, 0, CANRCVWORK);

        /* Mask RX interrupts until handled in the work queue */
        modifyreg32(priv->base + FLEX_CAN_IMASK1_OFFSET,
                    flags & IFLAG1_RX, 0);
    }

    if (flags & IFLAG1_TX)
    {
        flexcan_txdone(priv, flags);
    }

#ifdef CONFIG_OPEN_ERROR_MESG_FRAME
    /* Error interrupt */
    esr1 = readl(priv->base + FLEX_CAN_ESR1_OFFSET);
    if (esr1 & FLECAN_ERRSETS)
    {
        /* Clear the interrupt */
        writel(esr1 & (CAN_ESR1_PTA | CAN_ESR1_ATP | CAN_ESR1_ERROVR |
                        CAN_ESR1_ERRINTFAST | CAN_ESR1_BOFFDONEINT |
                        CAN_ESR1_TWRNINT | CAN_ESR1_RWRNINT |
                        CAN_ESR1_BOFFINT | CAN_ESR1_ERRINT | CAN_ESR1_WAKINT),
                priv->base + FLEX_CAN_ESR1_OFFSET);

        work_queue(&priv->errwork, flexcan_exception_work, priv, 0, CANWORK);
    }
#endif

    return;
}


static bool flexcan_setenable(uintptr_t base, bool enable)
{
  if (enable)
    {
      modifyreg32(base + FLEX_CAN_MCR_OFFSET, CAN_MCR_MDIS, 0);
    }
  else
    {
      modifyreg32(base + FLEX_CAN_MCR_OFFSET, 0, CAN_MCR_MDIS);
    }

  return flexcan_waitmcr_change(base, CAN_MCR_LPMACK, !enable);
}

static bool flexcan_setfreeze(uintptr_t base, bool freeze)
{
    if (freeze)
    {
        modifyreg32(base + FLEX_CAN_MCR_OFFSET, 0, CAN_MCR_HALT | CAN_MCR_FRZ);
    }
    else
    {
        modifyreg32(base + FLEX_CAN_MCR_OFFSET, CAN_MCR_HALT | CAN_MCR_FRZ, 0);
    }

    return flexcan_waitmcr_change(base, CAN_MCR_FRZACK, freeze);
}

static bool flexcan_waitmcr_change(uintptr_t base, uint32_t mask,
                                bool target_state)
{
    const uint32_t timeout = 1000;
    uint32_t wait_ack;
    bool state;

    for (wait_ack = 0; wait_ack < timeout; wait_ack++)
    {
        state = (readl(base + FLEX_CAN_MCR_OFFSET) & mask) != 0;
        if (state == target_state)
        {
            return true;
        }

        TTOS_SleepTask(1);
    }

  return false;
}

/****************************************************************************
 * Function: flexcan_get_mb
 *
 * Description:
 *   Get message buffer start address by buffer index. Message buffers
 *   are allocated in 512-byte ramblocks.
 *
 * Input Parameters:
 *   priv - Reference to the private FLEXCAN driver state structure
 *   mbi  - Message buffer index
 *
 * Returned Value:
 *   None
 *
 * Assumptions:
 *
 ****************************************************************************/

static volatile struct mb_s *flexcan_get_mb(struct flexcan_driver_s *priv,
                                            int mbi)
{
    uintptr_t mb_offset;
    size_t data_bytes = priv->canfd_capable ? 64 : 8;
    size_t mb_bytes = sizeof(struct mb_s) + data_bytes;
    int mbs_per_block = 512 / mb_bytes;          /* n of buffers in one ramblock */
    int ramblock = mbi / mbs_per_block;          /* ramblock in which the mb resides */
    int mb_off = mbi - ramblock * mbs_per_block; /* idx of the mb within ramblock */

    mb_offset = FLEX_CAN_MB_OFFSET + (ramblock * 512) + mb_off * mb_bytes;

    DEBUGASSERT(mb_offset < FLEX_CAN_MB_END);

    return (volatile struct mb_s *)(priv->base + mb_offset);
}


/****************************************************************************
 * Function: flexcan_ifup
 *
 * Description:
 *   Bring up the CAN interface
 * 
 * Input Parameters:
 *   dev  - Reference to the driver state structure
 *
 * Returned Value:
 *   0 or ERROR
 *
 * Assumptions:
 *
 ****************************************************************************/

static int flexcan_ifup(struct flexcan_driver_s *dev)
{
    int ret;
    struct flexcan_driver_s *priv = (struct flexcan_driver_s *)dev;
    
    TTOS_CreateSemaEx (TXMBCOUNT, &priv->txsema);

    if (flexcan_initialize(priv) != 0)
    {
        KLOG_E("initialize failed");
        return -EBUSY;
    }

    /* Set interrupts */

    ttos_pic_irq_unmask(priv->irq);

    return 0;
}

/****************************************************************************
 * Function: flexcan_ifdown
 *
 * Description:
 *   Stop the CAN interface.
 *
 * Input Parameters:
 *   dev  - Reference to the driver state structure
 *
 * Returned Value:
 *   None
 *
 * Assumptions:
 *
 ****************************************************************************/

static int flexcan_ifdown(struct flexcan_driver_s * priv)
{
    /* Disable interrupts */

    ttos_pic_irq_mask(priv->irq);

    flexcan_reset(priv);

    return 0;
}


/****************************************************************************
 * Function: flexcan_ioctl
 *
 * Description:
 *   PHY ioctl command handler
 *
 * Input Parameters:
 *   dev  - Reference to the driver state structure
 *   cmd  - ioctl command
 *   arg  - Argument accompanying the command
 *
 * Returned Value:
 *   Zero (0) on success; a negated errno value on failure.
 *
 * Assumptions:
 *
 ****************************************************************************/


static int flexcan_ioctl_handler(struct flexcan_driver_s *priv, int cmd,
                      unsigned long arg)
{
    int ret;
    unsigned int request;
    uint32_t objsize = 0;

    request = (unsigned int)cmd;

    if (priv->cursta != CFG_STA && request != IOCTL_CAN_SET_MODE && request != IOCTL_CAN_GET_HWREPORT)
    {
        return (-EPERM);
    }

    switch (request)
    {
        case IOCTL_CAN_SET_MODE:
        {
            enum work_status sta;
            objsize = _IOC_SIZE(cmd);
            ret = (objsize == sizeof(sta)) ? 0 : (-EINVAL);
            if (ret == 0)
            {
                sta = (enum work_status)arg;
                if (sta == CFG_STA)
                {
                    ret = flexcan_ifdown(priv);
                    if (ret == 0)
                    {
                        priv->cursta = CFG_STA;
                    }
                }
                /* If you are already in the running state, you are not allowed to enter again. 
                   You should enter the configuration state first. */
                else if (sta == RUN_STA && priv->cursta != RUN_STA)
                {
                    ret = flexcan_ifup(priv);
                    if (ret == 0)
                    {
                        priv->cursta = RUN_STA;
                    }
                }
                else
                {
                    ret = (-EPERM);
                }
            }
        }
        break;

        /* Configuring loopback and self-reception mode */
        case IOCTL_CAN_SET_LOOPBACK:
        {
            enum can_loopback_cfg loopback_cfg = ( enum can_loopback_cfg)arg;
            ret = 0;
            if (loopback_cfg & (~(CAN_DISABLE_ALL | CAN_LOOPBACK | CAN_SELF_RECEPT)) )
            {
                ret = -EINVAL;
            }
            if (loopback_cfg == CAN_DISABLE_ALL)
            {
                priv->selfrecept = false;
                priv->loopback = false;
            }
            else
            {
                priv->loopback =  loopback_cfg & CAN_LOOPBACK ? true : false;
                priv->selfrecept = loopback_cfg & CAN_SELF_RECEPT ? true : false;
            }

        }
        break;

        case IOCTL_CAN_SET_QUEUE:
        {
            unsigned long  len;
            len = arg;
            ret = flexcan_desp_free(&priv->rxrb);
            if (ret == 0)
            {
                ret = flexcan_desp_init(&priv->rxrb, len);
            }
        }
        break;

        case IOCTL_CAN_CLEAR_QUEUE:
        {
            ret = flexcan_desp_clear(&priv->rxrb);
        }
        break;

        case IOCTL_CAN_GET_HWREPORT:
        {
            struct can_ioctl_hwreport_s hwreport;
            objsize = _IOC_SIZE(cmd);
            ret = (objsize == sizeof(hwreport)) ? 0 : (-EINVAL);
            if (ret == 0)
            {
                hwreport.hw_ecr = readl(priv->base + FLEX_CAN_ECR_OFFSET);
                hwreport.hw_esr = readl(priv->base + FLEX_CAN_ESR1_OFFSET);
                hwreport.sf_packetloss = priv->packetloss;
                ret = copy_to_user((void *)arg, (void *)&hwreport, objsize);
            }
        }
        break;

        case IOCTL_CAN_GET_BITRATE: /* Get bitrate from a CAN controller */
        {
            struct can_ioctl_bitrate_s req;
            objsize = _IOC_SIZE(cmd);
            ret = (objsize == sizeof(req)) ? 0 : (-EINVAL);
            if (ret == 0)
            {
                req.arbi_bitrate = priv->arbi_timing.bitrate; /* bit/s */
                req.arbi_samplep = priv->arbi_timing.samplep;
                if (priv->canfd_capable)
                {
                    req.data_bitrate = priv->data_timing.bitrate; /* bit/s */
                    req.data_samplep = priv->data_timing.samplep;
                }
                else
                {
                    req.data_bitrate = 0;
                    req.data_samplep = 0;
                }
                ret = copy_to_user((void *)arg, (void *)&req, objsize);
            }
        }
        break;

        case IOCTL_CAN_SET_BITRATE: /* Set bitrate of a CAN controller */
        {
            struct can_ioctl_bitrate_s req;
            struct flexcan_timeseg arbi_timing;
            struct flexcan_timeseg data_timing;

            objsize = _IOC_SIZE(cmd);
            ret = (objsize == sizeof(req)) ? 0 : (-EINVAL);
            if (ret == 0 && copy_from_user((void *)&req, (void *)arg, objsize) == 0)
            {
                arbi_timing.bitrate = req.arbi_bitrate;
                arbi_timing.samplep = req.arbi_samplep;
                ret = flexcan_bitratetotimeseg(priv, &arbi_timing, false);
                if (ret == 0 && priv->canfd_capable)
                {
                    data_timing.bitrate = req.data_bitrate;
                    data_timing.samplep = req.data_samplep;
                    ret = flexcan_bitratetotimeseg(priv, &data_timing, true);
                }
                if (ret == 0)
                {
                    /* Reset CAN controller and start with new timings */
                    priv->arbi_timing = arbi_timing;
                    if (priv->canfd_capable)
                    {
                        priv->data_timing = data_timing;
                    }
                }
            }
        }
        break;

        case IOCTL_CAN_GET_BITRATE_FINE_TUNING:
        {
            struct can_ioctl_forcetiming_s timing;

            objsize = _IOC_SIZE(cmd);
            ret = (objsize == sizeof(timing)) ? 0 : (-EINVAL);
            if (ret == 0)
            {
                flexcan_get_forcetimeseg(priv, &timing);
                ret = copy_to_user((void *)arg, (void *)&timing, objsize);
            }
        }
        break;

        case IOCTL_CAN_SET_BITRATE_FINE_TUNING:
        {
            struct can_ioctl_forcetiming_s timing;

            objsize = _IOC_SIZE(cmd);
            ret = (objsize == sizeof(timing)) ? 0 : (-EINVAL);
            if (ret == 0)
            {
                if (copy_from_user((void *)&timing, (void *)arg, objsize) == 0)
                {
                    flexcan_forcetimeseg(priv, &timing);
                }
                else {
                    ret = (-EINVAL);
                }
            }
        }
        break;

        default:
            ret = -EPERM;
            break;
    }

    return ret;
}

/****************************************************************************
 * Function: flexcan_init_eccram
 *
 * Description:
 *   Initialize FLEXCAN ECC RAM
 *
 * Input Parameters:
 *   priv - Reference to the private FLEXCAN driver state structure
 *
 * Returned Value:
 *   None
 *
 * Assumptions:
 *
 ****************************************************************************/

static int flexcan_init_eccram(struct flexcan_driver_s *priv)
{
    volatile uint32_t *data = (uint32_t *)(priv->base + FLEX_CAN_MB_OFFSET);
    volatile uint32_t *data_end = (uint32_t *)(priv->base + FLEX_CAN_MB_END);
    int i;

    /* Set WRMFRZ bit in CTRL2 Register to grant write access to memory */

    modifyreg32(priv->base + FLEX_CAN_CTRL2_OFFSET, 0, CAN_CTRL2_WRMFRZ);

    /* Fill in the whole MB area as inactive RX buffers */

    while (data < data_end)
    {
        *data++ = 0;
    }

    /* Clear Mask registers - allow all for RX MBs */

    for (i = 0; i < RXMBCOUNT; i++)
    {
        writel(0, priv->base + FLEX_CAN_RXIMR_OFFSET(i));
    }

    /* Set Mask registers to compare all for TX MBs;
    * sending an RTR will result TX MB to become RX EMPTY.
    * We don't want to match anything in this case
    */

    for (; i < FLEX_CAN_N_RXIMR; i++)
    {
        writel(0xffffffff, priv->base + FLEX_CAN_RXIMR_OFFSET(i));
    }

    /* Clear legacy fifo information registers */

    for (i = 0; i < FLEX_CAN_N_RXFMB; i++)
    {
        writel(0, priv->base + FLEX_CAN_RXFMB_OFFSET(i));
    }

    /* Configure RX*MASKs at 0xaa0-> */

    writel(0x3fffffff, priv->base + FLEX_CAN_RX14MASK_OFFSET);
    writel(0x3fffffff, priv->base + FLEX_CAN_RX15MASK_OFFSET);
    writel(0x3fffffff, priv->base + FLEX_CAN_RXMGMASK_OFFSET);
    writel(0x0, priv->base + FLEX_CAN_RXFGMASK_OFFSET);

    /* Clear Tx_SMB, Rx_SMB0 and Rx_SMB1 */

    if (!priv->canfd_capable)
    {
        data = (uint32_t *)(priv->base + FLEX_CAN_TXSMB_OFFSET);
        data_end = &data[3 * (sizeof(struct mb_s) + 8) / 4];
    }
    else
    {
        data = (uint32_t *)(priv->base + FLEX_CAN_TXSMBFD_OFFSET);
        data_end = &data[3 * (sizeof(struct mb_s) + 64) / 4];
    }

    while (data < data_end)
    {
        *data++ = 0;
    }

    /* Clear WRMFRZ bit in CTRL2 Register */

    modifyreg32(priv->base + FLEX_CAN_CTRL2_OFFSET, CAN_CTRL2_WRMFRZ, 0);

    return 0;
}

/****************************************************************************
 * Function: flexcan_initalize
 *
 * Description:
 *   Initialize FLEXCAN device
 *
 * Input Parameters:
 *   priv - Reference to the private FLEXCAN driver state structure
 *
 * Returned Value:
 *   0 or ERROR
 *
 * Assumptions:
 *
 ****************************************************************************/

static int flexcan_initialize(struct flexcan_driver_s *priv)
{
    uint32_t tdcoff;
    int i;
    volatile struct mb_s *mb;

    /* Enable module */

    if (!flexcan_setenable(priv->base, true))
    {
        KLOG_E("FLEXCAN: enable fail\n");
        return -EBUSY;
    }

    /* Enter freeze mode */

    if (!flexcan_setfreeze(priv->base, true))
    {
        KLOG_E("FLEXCAN: freeze fail\n");
        return -EBUSY;
    }

    /* Initialize memory buffers */
    flexcan_init_eccram(priv);

    /* Configure MCR */
    modifyreg32(priv->base + FLEX_CAN_MCR_OFFSET, CAN_MCR_MAXMB_MASK,
                CAN_MCR_SLFWAK | CAN_MCR_WRNEN | CAN_MCR_WAKSRC |
                CAN_MCR_IRMQ | CAN_MCR_LPRIOEN | CAN_MCR_AEN |
                (((TOTALMBCOUNT - 1) << CAN_MCR_MAXMB_SHIFT) &
                CAN_MCR_MAXMB_MASK));

    if (!priv->canfd_capable)
    {
        modifyreg32(priv->base + FLEX_CAN_CTRL1_OFFSET,
                    CAN_CTRL1_PRESDIV_MASK | CAN_CTRL1_PROPSEG_MASK |
                    CAN_CTRL1_PSEG1_MASK | CAN_CTRL1_PSEG2_MASK |
                    CAN_CTRL1_RJW_MASK,
                    CAN_CTRL1_PRESDIV(priv->arbi_timing.presdiv) | /* Prescaler divisor factor */
                    CAN_CTRL1_PROPSEG(priv->arbi_timing.propseg) | /* Propagation segment */
                    CAN_CTRL1_PSEG1(priv->arbi_timing.pseg1) |     /* Phase buffer segment 1 */
                    CAN_CTRL1_PSEG2(priv->arbi_timing.pseg2) |     /* Phase buffer segment 2 */
                    CAN_CTRL1_RJW(priv->arbi_timing.jumpwidth) |   /* Resynchronization jump width */
                    CAN_CTRL1_BOFFREC                              /* Disable bus Off recovery */
    #ifdef CONFIG_OPEN_ERROR_MESG_FRAME
                    |
                    CAN_CTRL1_RWRNMSK |
                    CAN_CTRL1_TWRNMSK |
                    CAN_CTRL1_BOFFMSK 
    #endif
                );
    }
    else
    {
        modifyreg32(priv->base + FLEX_CAN_CTRL1_OFFSET, 0, 
                    CAN_CTRL1_BOFFREC    /* Disable bus Off recovery */
    #ifdef CONFIG_OPEN_ERROR_MESG_FRAME
                    |
                    CAN_CTRL1_RWRNMSK |
                    CAN_CTRL1_TWRNMSK |
                    CAN_CTRL1_BOFFMSK 
    #endif
                );

        modifyreg32(priv->base + FLEX_CAN_CBT_OFFSET,
                    CAN_CBT_EPRESDIV_MASK | CAN_CBT_EPROPSEG_MASK |
                    CAN_CBT_EPSEG1_MASK | CAN_CBT_EPSEG2_MASK |
                    CAN_CBT_ERJW_MASK,
                    CAN_CBT_BTF |                                 /* Enable extended bit timing
                                                                    * configurations for CAN-FD for setting up
                                                                    * separately nominal and data phase */
                    CAN_CBT_EPRESDIV(priv->arbi_timing.presdiv) | /* Prescaler divisor factor */
                    CAN_CBT_EPROPSEG(priv->arbi_timing.propseg) | /* Propagation segment */
                    CAN_CBT_EPSEG1(priv->arbi_timing.pseg1) |     /* Phase buffer segment 1 */
                    CAN_CBT_EPSEG2(priv->arbi_timing.pseg2) |     /* Phase buffer segment 2 */
                    CAN_CBT_ERJW(priv->arbi_timing.jumpwidth));   /* Resynchronization jump width */

        /* Enable CAN FD feature */
        modifyreg32(priv->base + FLEX_CAN_MCR_OFFSET, 0, CAN_MCR_FDEN);

        modifyreg32(priv->base + FLEX_CAN_FDCBT_OFFSET,
                    CAN_FDCBT_FPRESDIV_MASK | CAN_FDCBT_FPROPSEG_MASK |
                    CAN_FDCBT_FPSEG1_MASK | CAN_FDCBT_FPSEG2_MASK |
                    CAN_FDCBT_FRJW_MASK,
                    CAN_FDCBT_FPRESDIV(priv->data_timing.presdiv) |  /* Prescaler divisor factor of 1 */
                    CAN_FDCBT_FPROPSEG(priv->data_timing.propseg) |  /* Propagation
                                                                    * segment (only register that doesn't add 1) */
                    CAN_FDCBT_FPSEG1(priv->data_timing.pseg1) |      /* Phase buffer segment 1 */
                    CAN_FDCBT_FPSEG2(priv->data_timing.pseg2) |      /* Phase buffer segment 2 */
                    CAN_FDCBT_FRJW(priv->data_timing.jumpwidth));    /* Resynchorinzation jump width same as PSEG2 */

        /* Additional CAN-FD configurations */
        tdcoff = (priv->data_timing.pseg1 + priv->data_timing.pseg2 + 2) *
        (priv->data_timing.presdiv + 1);

        modifyreg32(priv->base + FLEX_CAN_FDCTRL_OFFSET, 0,
                    CAN_FDCTRL_FDRATE |          /* Enable bit rate switch in data phase of frame */
                    CAN_FDCTRL_TDCEN |           /* Enable transceiver delay compensation */
                    CAN_FDCTRL_TDCOFF(tdcoff) |  /* Setup 5 cycles for data phase sampling delay */
                    CAN_FDCTRL_MBDSR0(3) |       /* Setup 64 bytes per MB 0-6 */
                    CAN_FDCTRL_MBDSR1(3) |       /* Setup 64 bytes per MB 7-13 */
                    CAN_FDCTRL_MBDSR2(3));       /* Setup 64 bytes per MB 14-20 */

        modifyreg32(priv->base + FLEX_CAN_CTRL2_OFFSET, 0,
                    CAN_CTRL2_ISOCANFDEN);
    }

    if (!priv->selfrecept)
    {
        modifyreg32(priv->base + FLEX_CAN_MCR_OFFSET, 0, CAN_MCR_SRXDIS);
    }
    else
    {
        /* Clear LoopBack bit */
        modifyreg32(priv->base + FLEX_CAN_CTRL1_OFFSET, CAN_CTRL1_LPB, 0);
        modifyreg32(priv->base + FLEX_CAN_MCR_OFFSET, CAN_MCR_SRXDIS, 0);
    }

    if (priv->loopback)
    {
        /* In Loop-Back mode, CAN_MCR.SRXDIS cannot be asserted because it would 
        impede the self-reception of a transmitted message. */
        modifyreg32(priv->base + FLEX_CAN_MCR_OFFSET, CAN_MCR_SRXDIS, 0);

        /*CAN_FDCTRL.TDCEN must be O (Transceiver Delay Compensation feature disabled)
         when LPB is asserted.*/
        modifyreg32(priv->base + FLEX_CAN_FDCTRL_OFFSET, CAN_FDCTRL_TDCEN, 0);

        /* Enable LoopBack */
        modifyreg32(priv->base + FLEX_CAN_CTRL1_OFFSET, 0, CAN_CTRL1_LPB);
    }
    else
    {
        modifyreg32(priv->base + FLEX_CAN_CTRL1_OFFSET, CAN_CTRL1_LPB, 0);
    }

    /* Exit supervisor mode */
    modifyreg32(priv->base + FLEX_CAN_MCR_OFFSET, CAN_MCR_SUPV, 0);

    /* Always compare also IDE and RTR bits to mask in RX */
    modifyreg32(priv->base + FLEX_CAN_CTRL2_OFFSET, CAN_CTRL2_RETRY_MASK,
                CAN_CTRL2_RRS | CAN_CTRL2_EACEN |
                3 << CAN_CTRL2_RETRY_SHIFT);

    /* Clear MB interrupts */
    writel(IFLAG1_TX | IFLAG1_RX, priv->base + FLEX_CAN_IFLAG1_OFFSET);

    /* Enable MB interrupts */
    writel(IFLAG1_TX | IFLAG1_RX, priv->base + FLEX_CAN_IMASK1_OFFSET);

    /* Set RX buffers to receive */
    for (i = 0; i < RXMBCOUNT; i++)
    {
        mb = flexcan_get_mb(priv, i);
        mb->cs = (CAN_RXMB_EMPTY << CAN_MB_CS_CODE_SHIFT) | CAN_MB_CS_IDE;
    }

    /* Exit freeze mode */
    if (!flexcan_setfreeze(priv->base, false))
    {
        KLOG_E("FLEXCAN: unfreeze fail\n");
        return -EBUSY;
    }

    return 0;
}

/****************************************************************************
 * Function: flexcan_reset
 *
 * Description:
 *   Reset the flexcan and put it into disabled state
 *
 * Input Parameters:
 *   priv - Reference to the private FLEXCAN driver state structure
 *
 * Returned Value:
 *   None
 *
 * Assumptions:
 *
 ****************************************************************************/

static void flexcan_reset(struct flexcan_driver_s *priv)
{
  /* Make sure module is enabled */
  if (!flexcan_setenable(priv->base, true))
    {
      KLOG_E("Enable fail\n");
      return;
    }

  modifyreg32(priv->base + FLEX_CAN_MCR_OFFSET, 0, CAN_MCR_SOFTRST);

  if (!flexcan_waitmcr_change(priv->base, CAN_MCR_SOFTRST, false))
    {
      KLOG_E("Reset failed");
      return;
    }

  /* Disable module */
  if (!flexcan_setenable(priv->base, false))
    {
      KLOG_E("Disable fail\n");
      return;
    }
}

/****************************************************************************
 * Function: flexcan_canpinmux
 *
 * Description:
 *   Mux the pins used for CAN RX&TX
 *
 * Input Parameters:
 *   None
 *
 * Returned Value:
 *   None
 *
 * Assumptions:
 *
 ****************************************************************************/

static void flexcan_canpinmux(void)
{
    // ...
}


/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Function: flexcan_caninitialize
 *
 * Description:
 *   Initialize the CAN controller and driver
 *
 * Input Parameters:
 *   intf - In the case where there are multiple CAN, this value
 *          identifies which CAN is to be initialized.
 *
 * Returned Value:
 *   0 on success; Negated errno on failure.
 *
 * Assumptions:
 *
 ****************************************************************************/

int flexcan_caninitialize(struct flexcan_driver_s *priv)
{
    /* Get and store the clock (should be 80 MHz now) */
    flexcan_get_rootclock(&priv->clk_freq);

    /* Mux the can RX&TX pins */
    flexcan_canpinmux();

    /* Disable */
    flexcan_setenable(priv->base, false);

    /*  Put the interface in the down state. */
    flexcan_ifdown(priv);

    return 0;
}


static void flexcan_hal_init(struct flexcan_driver_s *priv)
{
    (void)priv;
    // ...
}


static int flexcan_mb_to_descp (struct mb_s *rf, struct flexcan_descp *desp)
{
    /* Read the frame contents */
#ifdef CONFIG_FLEXCAN_SUPPORT_CANFD

    uint32_t i;
    uint32_t *frame_data_word;

    if (rf->cs & CAN_MB_CS_EDL)
    {
        /* This is CANFD frame */
        desp->type = CANFD_FRAME_TYPE;

        struct canfd_frame *frame = (struct canfd_frame *)desp->frame;

        if (rf->cs & CAN_MB_CS_IDE)
        {
            frame->can_id = ((rf->id & CAN_MB_ID_ID_MASK) >> CAN_MB_ID_ID_SHIFT);
            frame->can_id |= CAN_EFF_FLAG;
        }
        else
        {
            frame->can_id = ((rf->id & CAN_MB_ID_ID_STD_MASK) >> CAN_MB_ID_ID_STD_SHIFT);
        }

        if (rf->cs & CAN_MB_CS_RTR)
        {
            frame->can_id |= CAN_RTR_FLAG;
        }

        /* Set bitrate switch by default if frame is CANFD */
        frame->flags = CANFD_BRS;
        if (rf->cs & CAN_MB_CS_ESI)
        {
            frame->flags |= CANFD_ESI;
        }

        frame->len = g_can_dlc_to_len[CAN_MB_CS_DLC(rf->cs)];

        frame_data_word = (uint32_t *)&frame->data[0];

        for (i = 0; i < (frame->len + 4 - 1) / 4; i++)
        {
            frame_data_word[i] = __builtin_bswap32(rf->data[i]);
        }
    }
    else
#endif
    {
        /* This is CAN2.0 frame */
        desp->type = CAN_FRAME_TYPE;

        struct can_frame *frame = (struct can_frame *)desp->frame;

        if (rf->cs & CAN_MB_CS_IDE)
        {
            frame->can_id = ((rf->id & CAN_MB_ID_ID_MASK) >> CAN_MB_ID_ID_SHIFT);
            frame->can_id |= CAN_EFF_FLAG;
        }
        else
        {
            frame->can_id = ((rf->id & CAN_MB_ID_ID_STD_MASK) >> CAN_MB_ID_ID_STD_SHIFT);
        }

        if (rf->cs & CAN_MB_CS_RTR)
        {
            frame->can_id |= CAN_RTR_FLAG;
        }

        frame->can_dlc = CAN_MB_CS_DLC(rf->cs);

        *(uint32_t *)&frame->data[0] = __builtin_bswap32(rf->data[0]);
        *(uint32_t *)&frame->data[4] = __builtin_bswap32(rf->data[1]);
    }

    return 0;
}


static int flexcan_desp_init (struct flexcan_rb *rb, size_t num)
{
    int loop;
    struct flexcan_descp *desp;

    desp = (struct flexcan_descp *)malloc(num * sizeof(*desp));
    if (desp == NULL)
    {
        return (-ENOMEM);
    }
    for (loop = 0; loop < num; loop++)
    {
        desp[loop].own = 0;
        desp[loop].type = 0;
    }

    rb->wp = 0;
    rb->rp = 0;
    rb->num = num;
    rb->desp = desp;

    return 0;
}


static int flexcan_desp_clear (struct flexcan_rb *rb)
{
    int loop;
    struct flexcan_descp *desp;

    if (rb == NULL || rb->desp == NULL)
    {
        return (-EINVAL);
    }
    
    desp = rb->desp;

    for (loop = 0; loop < rb->num; loop++)
    {
        desp[loop].own = 0;
        desp[loop].type = 0;
    }

    rb->wp = 0;
    rb->rp = 0;

    return 0;
}


static int flexcan_desp_free (struct flexcan_rb *rb)
{
    if (rb->desp != NULL)
    {
        rb->num = 0;
        rb->rp = 0;
        rb->wp = 0;
        free(rb->desp);
        rb->desp = NULL;
        return 0;
    }
    return (-EINVAL);
}


static int flexcan_read_desp (struct flexcan_rb *rb, void *pbuf, size_t len)
{
    uint32_t rp;
    uint8_t frametype;
    struct flexcan_descp *desp;
    
    desp = rb->desp;
    if (desp == NULL)
    {
        return (-EINVAL);
    }

    rp = rb->rp;

    if (desp[rp].own == 1)
    {
        frametype = desp[rp].type;

        if ((frametype == CANFD_FRAME_TYPE) && (len >= sizeof(struct canfd_frame)) )
        {
            memcpy(pbuf, &desp[rp].frame, sizeof(struct canfd_frame));
        }
        else if ((frametype == CAN_FRAME_TYPE) && (len >= sizeof(struct can_frame)) )
        {
            memcpy(pbuf, &desp[rp].frame, sizeof(struct can_frame));
        }

        smp_mb(); // Must!
        desp[rp].own = 0;
        rb->rp = (rp == (rb->num - 1)) ? 0 : (rp + 1);

        return (frametype == CANFD_FRAME_TYPE) ? sizeof(struct canfd_frame) : sizeof(struct can_frame);
    }

    return (-EAGAIN);
}


static int flexcan_write_desp (struct flexcan_rb *rb, struct mb_s *rf)
{
    uint32_t wp;
    struct flexcan_descp *desp;
    
    desp = rb->desp;
    if (desp == NULL)
    {
        return (-EINVAL);
    }
    
    wp = rb->wp;

    if(desp[wp].own == 0)
    {
        flexcan_mb_to_descp(rf, &desp[wp]);
        smp_mb();  // Must!
        desp[wp].own = 1;
        rb->wp = (wp == (rb->num - 1)) ? 0 : (wp + 1);
        return 0;
    }
    
    return (-ENOMEM);
}


static int flexcan_open (struct file *filep)
{
    int ret;
    struct flexcan_driver_s *priv;
    
    if (filep == NULL) {
        return -EINVAL;
    }
    priv = filep->f_inode->i_private;

    ret = flexcan_desp_init(&priv->rxrb, CONFIG_TRANS_RECV_QUEUE_NUM);
    if (ret != 0) {
        KLOG_E("flexcan alloc rx desp fail.");
        return ret;
    }

#ifdef CONFIG_TX_QUEUE
    ret = flexcan_desp_init(&priv->txrb, CONFIG_TRANS_RECV_QUEUE_NUM);
    if (ret != 0) {
        free(priv->rxrb.desp);
        KLOG_E("flexcan alloc tx desp fail.");
        return ret;
    }
#endif

    flexcan_caninitialize(priv);

# if defined(CONFIG_FLEXCAN_SUPPORT_CANFD)
    priv->canfd_capable = true,
# else
    priv->canfd_capable = false,
# endif

    /* Default bitrate configuration */
# if defined(CONFIG_FLEXCAN_SUPPORT_CANFD)
    priv->arbi_timing.bitrate = CONFIG_FLEXCAN_ARBI_BITRATE;
    priv->arbi_timing.samplep = CONFIG_FLEXCAN_ARBI_SAMPLEP;
    priv->data_timing.bitrate = CONFIG_FLEXCAN_DATA_BITRATE;
    priv->data_timing.samplep = CONFIG_FLEXCAN_DATA_SAMPLEP;
# else
    priv->arbi_timing.bitrate = CONFIG_FLEXCAN_BITRATE;
    priv->arbi_timing.samplep = CONFIG_FLEXCAN_SAMPLEP;
# endif

    if (flexcan_bitratetotimeseg(priv, &priv->arbi_timing, false) != 0)
    {
        KLOG_E("ERROR: Invalid CAN timings please try another sample point "
                "or refer to the reference manual\n");
    }

    if (priv->canfd_capable)
    {
        if (flexcan_bitratetotimeseg(priv, &priv->data_timing, true) != 0)
        {
            KLOG_E("ERROR: Invalid CAN data phase timings please try another "
                    "sample point or refer to the reference manual\n");
        }
    }

    /* Disable Loopback mode */
    priv->loopback = false;
    priv->selfrecept = false,

    priv->packetloss = 0;

    priv->cursta = CFG_STA;

    return  0;
}


static int flexcan_close (struct file *filep)
{
    struct flexcan_driver_s *priv = filep->f_inode->i_private;

#ifdef CONFIG_TX_QUEUE
    flexcan_desp_free(&priv->txrb);
#endif

    flexcan_desp_free(&priv->rxrb);

    /* Disable irq */
    flexcan_ifdown(priv);

    return  0;
}


static ssize_t flexcan_read (struct file *filep, char *buffer, size_t buflen)
{
    ssize_t nbytes;
    struct flexcan_driver_s *priv = filep->f_inode->i_private;
    
    if (priv->cursta != RUN_STA)
    {
        return (-EPERM);
    }

    nbytes = flexcan_read_desp(&priv->rxrb, (void *)buffer, buflen);

    return  nbytes;
}



static ssize_t flexcan_write (struct file *filep, const char *buffer, size_t buflen)
{
    int ret;
    bool isfull;
    ssize_t nbytes;
    struct flexcan_driver_s *priv = filep->f_inode->i_private;

    if (priv->cursta != RUN_STA)
    {
        return (-EPERM);
    }

    ret = TTOS_ObtainSema (priv->txsema, TRANS_MAX_TIMEOUT);
    if (ret != 0)
    {
        return ret;
    }

    nbytes = flexcan_transmit(priv, buffer, buflen);
    
    if (nbytes == 0)
    {   
        nbytes = buflen;
    }

    return nbytes;
}


static int flexcan_ioctl (struct file *filep,unsigned int cmd, unsigned long arg)
{
    int ret;
    struct flexcan_driver_s *priv = filep->f_inode->i_private;
    
    if (priv == NULL)
    {
        return (-EINVAL);
    }

    ret = flexcan_ioctl_handler(priv, cmd, arg);

    return  ret;
}


static int flexcan_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    struct flexcan_driver_s *priv = filep->f_inode->i_private;

    return  0;
}


static const struct file_operations g_flexcan_fops = {
    .open  = flexcan_open,
    .close = flexcan_close,
    .read  = flexcan_read,
    .write = flexcan_write,
    .ioctl = flexcan_ioctl,
    .poll  = flexcan_poll,
    .mmap  = NULL,
};


int flexcan_probe (struct device *dev)
{
    int ret;
    static int index;
    uint32_t txsize;
    uint32_t rxsize;
    size_t regsize;
    phys_addr_t regaddr;
    struct flexcan_driver_s *priv;

    priv = calloc(1, sizeof(struct flexcan_driver_s));
    if (priv == NULL)
    {
        KLOG_E ("No enough memory");
        free(priv);
        return -ENOMEM;
    }

    ret = platform_get_resource_regs(dev, &priv->region[0], 1);
    if (ret < 0)
    {
        KLOG_E ("device get reg error");
        free(priv);
        return ret;
    }

    priv->base = priv->region[0].vaddr;

    /* Alloc irq */
    priv->irq = ttos_pic_irq_alloc (dev, 0);

    /* Pin and clock config */
    flexcan_hal_init(priv);

    /* Install irq handle function */
    ret = ttos_pic_irq_install(priv->irq, flexcan_flexcan_interrupt, 
                              (void*)priv, 0, "flexcan_irq_handle");
    if (ret < 0)
    {
        KLOG_E ("device get reg error");
        free(priv);
        return ret;
    }

    snprintf(priv->name, sizeof(dev->name), "/dev/flexcan%d", ++index);
    ret = register_driver(priv->name, &g_flexcan_fops, 0666, priv);
    if (ret < 0)
    {
        KLOG_E ("register driver error");
        free(priv);
        return ret;
    }

    return 0;
}


static struct of_device_id flexcan_table[] = 
{
    { .compatible = "nxp,flexcan-fd" },
    { /* end of list */ },
};


static struct driver flexcan_driver =
{
    .name = "flexcan driver",
    .probe = flexcan_probe,
    .match_table = &flexcan_table[0]
};


int flexcan_init (void)
{   
    return platform_add_driver (&flexcan_driver);
}


INIT_EXPORT_DRIVER (flexcan_init, "flexcan driver init");
