config FLEXCAN_BUS_CLK
	int "Peripherals Bus clock"
	default 80000000
	---help---
	Bus clock going into peripherals.


config FLEXCAN_TXMB
	int "Number of TX message buffers"
	default 4
	---help---
	This defines number of TX messages buffers. Please note that
	maximum number of all message buffers is 13 (one MB has to
	be reserved for chip errata ERR005829).


config FLEXCAN_RXMB
	int "Number of recv message buffers"
	default 16
	---help---
	This defines number of RX messages buffers. Please note that
	maximum number of all message buffers is 13 (one MB has to
	be reserved for chip errata ERR005829).

config TRANS_RECV_QUEUE_NUM
    int "Num of tx and rx queues"
	default 10
	---help---
	Number of send and receive queues.


config FLEXCAN_SUPPORT_CANFD
	bool "support canfd"
	default true
	help
	    support canfd

config FLEXCAN_BITRATE
	int "can bitrate"
	depends on !FLEXCAN_SUPPORT_CANFD
	default 1000000

config FLEXCAN_SAMPLEP
	int "can sample point"
	depends on !FLEXCAN_SUPPORT_CANFD
	default 75

config FLEXCAN_ARBI_BITRATE
	int "canfd Arbitration phase bitrate"
	depends on FLEXCAN_SUPPORT_CANFD
	default 1000000

config FLEXCAN_ARBI_SAMPLEP
	int "canfd Arbitration phase sample point"
	depends on FLEXCAN_SUPPORT_CANFD
	default 75

config FLEXCAN_DATA_BITRATE
	int "canfd Data phase bitrate"
	depends on FLEXCAN_SUPPORT_CANFD
	default 4000000

config FLEXCAN_DATA_SAMPLEP
	int "canfd Data phase sample point"
	depends on FLEXCAN_SUPPORT_CANFD
	default 75