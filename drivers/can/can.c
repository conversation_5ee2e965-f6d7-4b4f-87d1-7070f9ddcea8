#include <driver/class/chardev.h>
#include <driver/device.h>
#include <stdio.h>
#include <errno.h>


/****************************************************************************
 * Public Data
 ****************************************************************************/

const uint8_t g_can_dlc_to_len[16] =
{
  0, 1, 2, 3, 4, 5, 6, 7, 8, 12, 16, 20, 24, 32, 48, 64,
};


const uint8_t g_len_to_can_dlc[65] =
{
  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9, 9, 9, 10, 10, 10, 10,
  11, 11, 11, 11, 12, 12, 12, 12, 13, 13, 13, 13, 13, 13,
  13, 13, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14,
  14, 14, 14, 14, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
  15, 15, 15, 15, 15, 15,
};


static int g_can_minor = 0;


int* get_g_can_minor()
{
    return &g_can_minor;
}

int can_register (struct device *dev)
{
    char *path;
    const char *eupath;
    int   ret;

    dev->minor = g_can_minor++;
    asprintf (&path, "/dev/can%d", dev->minor);
    if (path == NULL)
    {
        return -ENOMEM;
    }
    ret = char_register (path, dev);
    
    free (path);
    return ret;
}