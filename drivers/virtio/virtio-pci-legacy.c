/****************************************************************************
 * Included Files
 ****************************************************************************/
#include <driver/pci/pci.h>
#include <errno.h>
#include <stdbool.h>
#include <stdint.h>
#include <sys/param.h>

#include "virtio-pci.h"
#include "virtio_helper.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

#define VIRTIO_PCI_LEGACY_IO_BAR 0

/* A 32-bit r/o bitmask of the features supported by the host */

#define VIRTIO_PCI_HOST_FEATURES 0

/* A 32-bit r/w bitmask of features activated by the guest */

#define VIRTIO_PCI_GUEST_FEATURES 4

/* A 32-bit r/w PFN for the currently selected queue */

#define VIRTIO_PCI_QUEUE_PFN 8

/* A 16-bit r/o queue size for the currently selected queue */

#define VIRTIO_PCI_QUEUE_NUM 12

/* A 16-bit r/w queue selector */

#define VIRTIO_PCI_QUEUE_SEL 14

/* A 16-bit r/w queue notifier */

#define VIRTIO_PCI_QUEUE_NOTIFY 16

/* An 8-bit device status register.  */

#define VIRTIO_PCI_STATUS 18

/* An 8-bit r/o interrupt status register.  Reading the value will return the
 * current contents of the ISR and will also clear it.  This is effectively
 * a read-and-acknowledge.
 */

#define VIRTIO_PCI_ISR 19

/* MSI-X registers: only enabled if MSI-X is enabled. */

#define VIRTIO_MSI_CONFIG_VECTOR 20

/* A 16-bit vector for selected queue notifications. */

#define VIRTIO_MSI_QUEUE_VECTOR 22

/* The remaining space is defined by each driver as the per-driver
 * configuration space
 */

#define VIRTIO_PCI_CONFIG_OFF(msix_enabled) ((msix_enabled) ? 24 : 20)

/* Virtio ABI version, this must match exactly */

#define VIRTIO_PCI_ABI_VERSION 0

/* How many bits to shift physical queue address written to QUEUE_PFN.
 * 12 is historical, and due to x86 page size.
 */

#define VIRTIO_PCI_QUEUE_ADDR_SHIFT 12

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/* Helper functions */

static uint16_t virtio_pci_legacy_get_queue_len(struct virtio_pci_device_s *vpdev, int idx);
static int virtio_pci_legacy_create_virtqueue(struct virtio_pci_device_s *vpdev,
                                              struct virtqueue *vq);
static int virtio_pci_legacy_config_vector(struct virtio_pci_device_s *vpdev, bool enable);

static void virtio_pci_legacy_delete_virtqueue(struct virtio_device *vdev, int index);
static void virtio_pci_legacy_set_status(struct virtio_device *vdev, uint8_t status);
static uint8_t virtio_pci_legacy_get_status(struct virtio_device *vdev);
static void virtio_pci_legacy_write_config(struct virtio_device *vdev, uint32_t offset, void *dst,
                                           int length);
static void virtio_pci_legacy_read_config(struct virtio_device *vdev, uint32_t offset, void *dst,
                                          int length);
static uint64_t virtio_pci_legacy_get_features(struct virtio_device *vdev);
static void virtio_pci_legacy_set_features(struct virtio_device *vdev, uint64_t features);
static void virtio_pci_legacy_notify(struct virtqueue *vq);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct virtio_dispatch g_virtio_pci_dispatch = {
    virtio_pci_create_virtqueues,   /* create_virtqueues */
    virtio_pci_delete_virtqueues,   /* delete_virtqueues */
    virtio_pci_legacy_get_status,   /* get_status */
    virtio_pci_legacy_set_status,   /* set_status */
    virtio_pci_legacy_get_features, /* get_features */
    virtio_pci_legacy_set_features, /* set_features */
    virtio_pci_negotiate_features,  /* negotiate_features */
    virtio_pci_legacy_read_config,  /* read_config */
    virtio_pci_legacy_write_config, /* write_config */
    virtio_pci_reset_device,        /* reset_device */
    virtio_pci_legacy_notify,       /* notify */
};

static const struct virtio_pci_ops_s g_virtio_pci_legacy_ops = {
    virtio_pci_legacy_get_queue_len,    /* get_queue_len */
    virtio_pci_legacy_config_vector,    /* config_vector */
    virtio_pci_legacy_create_virtqueue, /* create_virtqueue */
    virtio_pci_legacy_delete_virtqueue, /* delete_virtqueue */
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: virtio_pci_legacy_get_queue_len
 ****************************************************************************/

static uint16_t virtio_pci_legacy_get_queue_len(struct virtio_pci_device_s *vpdev, int idx)
{
    uint16_t num;

    pci_write_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_SEL), idx);
    pci_read_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_NUM), &num);
    if (num == 0)
    {
        printk("Queue is not available num=%d\n", num);
        assert(0);
    }

    return num;
}

/****************************************************************************
 * Name: virtio_pci_legacy_create_virtqueue
 ****************************************************************************/

static int virtio_pci_legacy_create_virtqueue(struct virtio_pci_device_s *vpdev,
                                              struct virtqueue *vq)
{
#if CONFIG_DRIVERS_VIRTIO_PCI_POLLING_PERIOD <= 0
    uint16_t msix_vector;
#endif

    /* Set the pci virtqueue register, active vq, enable vq */

    pci_write_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_SEL),
                      vq->vq_queue_index);

    /* activate the queue */

    pci_write_io_dword(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_PFN),
                       mm_kernel_v2p((virt_addr_t)vq->vq_ring.desc) >> VIRTIO_PCI_QUEUE_ADDR_SHIFT);

#if CONFIG_DRIVERS_VIRTIO_PCI_POLLING_PERIOD <= 0
    pci_write_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_MSI_QUEUE_VECTOR),
                      VIRTIO_PCI_INT_VQ_IRQ);
    pci_read_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_MSI_QUEUE_VECTOR),
                     &msix_vector);
    if (msix_vector == VIRTIO_PCI_MSI_NO_VECTOR)
    {
        pci_write_io_dword(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_PFN), 0);
        printk("Msix vector is 0\n");
        return -EBUSY;
    }
#endif
}

/****************************************************************************
 * Name: virtio_pci_legacy_config_vector
 ****************************************************************************/

static int virtio_pci_legacy_config_vector(struct virtio_pci_device_s *vpdev, bool enable)
{
    uint16_t vector = enable ? 0 : VIRTIO_PCI_MSI_NO_VECTOR;
    uint16_t rvector;

    pci_write_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_MSI_CONFIG_VECTOR), vector);
    pci_read_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_MSI_CONFIG_VECTOR), &rvector);

    if (rvector != vector)
    {
        return -EINVAL;
    }

    return 0;
}

/****************************************************************************
 * Name: virtio_pci_legacy_delete_virtqueue
 ****************************************************************************/

void virtio_pci_legacy_delete_virtqueue(struct virtio_device *vdev, int index)
{
    //   struct virtio_pci_device_s *vpdev =
    //     (struct virtio_pci_device_s *)vdev;
    // #if CONFIG_DRIVERS_VIRTIO_PCI_POLLING_PERIOD <= 0
    //   uint8_t isr;
    // #endif

    //   pci_write_io_word(vpdev->dev,
    //                     (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_SEL),
    //                     index);

    // #if CONFIG_DRIVERS_VIRTIO_PCI_POLLING_PERIOD <= 0
    //   pci_write_io_word(vpdev->dev,
    //                     (uintptr_t)(vpdev->ioaddr + VIRTIO_MSI_QUEUE_VECTOR),
    //                     VIRTIO_PCI_MSI_NO_VECTOR);

    //   /* Flush the write out to device */

    //   pci_read_io_byte(vpdev->dev,
    //                    (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_ISR), &isr);
    // #endif

    //   /* Select and deactivate the queue */

    //   pci_write_io_dword(vpdev->dev,
    //                      (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_PFN), 0);
}

/****************************************************************************
 * Name: virtio_pci_legacy_set_status
 ****************************************************************************/

static void virtio_pci_legacy_set_status(struct virtio_device *vdev, uint8_t status)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)vdev;

    pci_write_io_byte(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_STATUS), status);
}

/****************************************************************************
 * Name: virtio_pci_legacy_get_status
 ****************************************************************************/

static uint8_t virtio_pci_legacy_get_status(struct virtio_device *vdev)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)vdev;
    uint8_t status;

    pci_read_io_byte(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_STATUS), &status);
    return status;

    return 0;
}

/****************************************************************************
 * Name: virtio_pci_legacy_write_config
 ****************************************************************************/

static void virtio_pci_legacy_write_config(struct virtio_device *vdev, uint32_t offset, void *src,
                                           int length)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)vdev;
#if CONFIG_DRIVERS_VIRTIO_PCI_POLLING_PERIOD <= 0
    char *config = vpdev->ioaddr + VIRTIO_PCI_CONFIG_OFF(true) + offset;
#else
    char *config = vpdev->ioaddr + VIRTIO_PCI_CONFIG_OFF(false) + offset;
#endif
    uint8_t *s = src;
    int i;

    for (i = 0; i < length; i++)
    {
        pci_write_io_byte(vpdev->dev, (uintptr_t)(config + i), s[i]);
    }
}

/****************************************************************************
 * Name: virtio_pci_legacy_read_config
 ****************************************************************************/

static void virtio_pci_legacy_read_config(struct virtio_device *vdev, uint32_t offset, void *dst,
                                          int length)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)vdev;
#if CONFIG_DRIVERS_VIRTIO_PCI_POLLING_PERIOD <= 0
    char *config = vpdev->ioaddr + VIRTIO_PCI_CONFIG_OFF(true) + offset;
#else
    char *config = vpdev->ioaddr + VIRTIO_PCI_CONFIG_OFF(false) + offset;
#endif

    uint8_t *d = dst;
    int i;

    for (i = 0; i < length; i++)
    {
        pci_read_io_byte(vpdev->dev, (uintptr_t)(config + i), &d[i]);
    }
}

/****************************************************************************
 * Name: virtio_pci_legacy_get_features
 ****************************************************************************/

static uint64_t virtio_pci_legacy_get_features(struct virtio_device *vdev)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)vdev;
    uint32_t feature;

    pci_read_io_dword(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_HOST_FEATURES), &feature);
    return feature;
}

/****************************************************************************
 * Name: virtio_pci_legacy_set_features
 ****************************************************************************/

static void virtio_pci_legacy_set_features(struct virtio_device *vdev, uint64_t features)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)vdev;

    if ((features >> 32) != 0)
    {
        features = (uint64_t)((uint32_t)features);
        printk("Virtio pci legacy not support feature bits larger than 32\n");
    }

    pci_write_io_dword(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_GUEST_FEATURES),
                       features);
    vdev->features = features;
}

/****************************************************************************
 * Name: virtio_pci_legacy_notify
 ****************************************************************************/

static void virtio_pci_legacy_notify(struct virtqueue *vq)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)vq->vq_dev;

    pci_write_io_word(vpdev->dev, (uintptr_t)(vpdev->ioaddr + VIRTIO_PCI_QUEUE_NOTIFY),
                      vq->vq_queue_index);
}

/****************************************************************************
 * Name: virtio_pci_legacy_init_device
 ****************************************************************************/

static int virtio_pci_legacy_init_device(struct virtio_pci_device_s *vpdev)
{
    struct virtio_device *vdev = &vpdev->vdev;
    struct pci_dev *dev = vpdev->dev;

    // if (dev->revision != VIRTIO_PCI_ABI_VERSION)
    //   {
    //     printk("Virtio_pci: expected ABI version %d, got %u\n",
    //             VIRTIO_PCI_ABI_VERSION, dev->revision);
    //     return -ENODEV;
    //   }

    vpdev->ioaddr = (void *)vpdev->dev->bar[0].addr;
    if (vpdev->ioaddr == NULL)
    {
        printk("Unable to map virtio on bar\n");
        return -EINVAL;
    }

    vdev->id.vendor = dev->subsystem_vendor;
    vdev->id.device = dev->subsystem_device;

    return 0;
}

/****************************************************************************
 * Name: virtio_pci_legacy_probe
 ****************************************************************************/

int virtio_pci_legacy_probe(struct pci_dev *dev)
{
    struct virtio_pci_device_s *vpdev = (struct virtio_pci_device_s *)dev->priv;
    struct virtio_device *vdev = &vpdev->vdev;
    int ret;

    /* We only own devices >= 0x1000 and <= 0x107f: leave the rest. */

    if (dev->device < 0x1000 || dev->device > 0x103f)
    {
        return -ENODEV;
    }

    vpdev->ops = &g_virtio_pci_legacy_ops;
    vdev->func = &g_virtio_pci_dispatch;
    vdev->role = VIRTIO_DEV_DRIVER;

    ret = virtio_pci_legacy_init_device(vpdev);
    if (ret < 0)
    {
        printk("Virtio pci legacy device init failed, ret=%d\n", ret);
    }

    return ret;
}