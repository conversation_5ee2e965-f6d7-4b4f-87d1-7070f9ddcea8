int atomic32_compare_exchange(volatile int *ptr, int *old, int new)
{
    long ltmp, loldval;
    long lnew = new;

    __asm__ volatile("   prfm    pstl1strm, %2\n"
                     "1: ldr     %0, %2\n"
                     "   eor     %1, %0, %3\n"
                     "   cbnz    %1, 2f\n"
                     "   stlxr   %w1, %4, %2\n"
                     "   cbnz    %w1, 1b\n"
                     "   dmb     ish\n"
                     "2:"
                     : "=&r"(loldval), "=&r"(ltmp), "+Q"(*ptr)
                     : "Kr"(*old), "r"(lnew)
                     : "memory");

    return loldval == (long)*old;
}
