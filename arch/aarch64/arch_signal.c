/**
 * @file    arch/arm/arch_signal.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * ac006b61 2024-07-02 移除一级ttos目录
 * 4dc228c0 2024-06-07 处理进程退出
 * 1f357380 2024-06-06 signal bug fix
 * 4bb423b7 2024-05-30 1.添加进程组 2.支持信号发送到进程组
 * 3823374e 2024-05-23 添加signal功能模块
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <assert.h>
#include <cache.h>
#include <context.h>
#include <errno.h>
#include <process_signal.h>
#include <ptrace.h>
#include <sigcontext.h>
#include <signal.h>
#include <syscall.h>
#include <ttos.h>
#include <ttosProcess.h>
#include <uaccess.h>
#include <util.h>

#include <arch_sigcontext.h>

#undef KLOG_TAG
#define KLOG_TAG "arch_signal"
#include <klog.h>

//#define SIGNAL_DEBUG_INFO

#define SZ_256K 0x00040000
#define SIGFRAME_MAXSZ SZ_256K

void sys_exit_group(int flag);
T_TTOS_TaskControlBlock *current_task(void);
void thread_signal_mask_get(pcb_t pcb, process_sigset_t *oset);
int arch_valid_user_regs(struct arch_context *regs);
void sigreturn_code(void);
void restore_raw_context(arch_exception_context_t *context);
void signal_del_running(int signo);

struct rt_sigframe
{
    struct siginfo info;
    ucontext_t uc;

    /* 存放信号的返回指令码(两条指令，共8个字节)*/
    unsigned int sig_return_code[2];
};

struct frame_record
{
    u64 fp;
    u64 lr;
};

struct rt_sigframe_user_layout
{
    struct rt_sigframe __user *sigframe;
    struct frame_record __user *next_frame;

    unsigned long size;  /* size of allocated sigframe data */
    unsigned long limit; /* largest allowed size */

    unsigned long fpsimd_offset;
    unsigned long esr_offset;
    unsigned long sve_offset;
    unsigned long tpidr2_offset;
    unsigned long za_offset;
    unsigned long zt_offset;
    unsigned long fpmr_offset;
    unsigned long extra_offset;
    unsigned long end_offset;
};

struct user_ctxs
{
    struct fpsimd_context __user *fpsimd;
    u32 fpsimd_size;
    struct sve_context __user *sve;
    u32 sve_size;
    struct tpidr2_context __user *tpidr2;
    u32 tpidr2_size;
    struct za_context __user *za;
    u32 za_size;
    struct zt_context __user *zt;
    u32 zt_size;
    struct fpmr_context __user *fpmr;
    u32 fpmr_size;
};

#define IS_ALIGNED(x, a) (((x) & ((typeof(x))(a)-1)) == 0)

#ifdef CONFIG_COMPAT
#define compat_thumb_mode(regs) (((regs)->pstate & PSR_AA32_T_BIT))
#else
#define compat_thumb_mode(regs) (0)
#endif

#define BASE_SIGFRAME_SIZE round_up(sizeof(struct rt_sigframe), 16)
#define TERMINATOR_SIZE round_up(sizeof(struct _aarch64_ctx), 16)
#define EXTRA_CONTEXT_SIZE round_up(sizeof(struct extra_context), 16)

#define SI_EXPANSION_SIZE (sizeof(struct siginfo) - sizeof(struct kernel_siginfo))

static bool system_supports_fpsimd(void)
{
    return true;
}

static bool system_supports_sve(void)
{
    return false;
}

static bool system_supports_sme(void)
{
    return false;
}
static bool system_supports_tpidr2(void)
{
    return false;
}
static bool system_supports_sme2(void)
{
    return false;
}

static bool system_supports_fpmr(void)
{
    return false;
}

static int restore_sve_fpsimd_context(struct user_ctxs *user)
{
    KLOG_E("%s not implement!!!", __func__);
    return 0;
}

static int restore_zt_context(struct user_ctxs *user)
{
    KLOG_E("%s not implement!!!", __func__);
    return 0;
}

static int restore_za_context(struct user_ctxs *user)
{
    KLOG_E("%s not implement!!!", __func__);
    return 0;
}

static int restore_tpidr2_context(struct user_ctxs *user)
{
    KLOG_E("%s not implement!!!", __func__);
    return 0;
}
static int restore_fpmr_context(struct user_ctxs *user)
{
    KLOG_E("%s not implement!!!", __func__);
    return 0;
}

static inline bool system_supports_bti(void)
{
    return false;
}

static void init_user_layout(struct rt_sigframe_user_layout *user)
{
    const size_t reserved_size = sizeof(user->sigframe->uc.uc_mcontext.__reserved);

    memset(user, 0, sizeof(*user));
    user->size = offsetof(struct rt_sigframe, uc.uc_mcontext.__reserved);

    user->limit = user->size + reserved_size;

    user->limit -= TERMINATOR_SIZE;
    user->limit -= EXTRA_CONTEXT_SIZE;
    /* Reserve space for extension and terminator ^ */
}

static size_t sigframe_size(struct rt_sigframe_user_layout const *user)
{
    return round_up(max(user->size, sizeof(struct rt_sigframe)), 16);
}

/*
 * Sanity limit on the approximate maximum size of signal frame we'll
 * try to generate.  Stack alignment padding and the frame record are
 * not taken into account.  This limit is not a guarantee and is
 * NOT ABI.
 */

static int __sigframe_alloc(struct rt_sigframe_user_layout *user, unsigned long *offset,
                            size_t size, bool extend)
{
    size_t padded_size = round_up(size, 16);

    if (padded_size > user->limit - user->size && !user->extra_offset && extend)
    {
        int ret;

        user->limit += EXTRA_CONTEXT_SIZE;
        ret = __sigframe_alloc(user, &user->extra_offset, sizeof(struct extra_context), false);
        if (ret)
        {
            user->limit -= EXTRA_CONTEXT_SIZE;
            return ret;
        }

        /* Reserve space for the __reserved[] terminator */
        user->size += TERMINATOR_SIZE;

        /*
         * Allow expansion up to SIGFRAME_MAXSZ, ensuring space for
         * the terminator:
         */
        user->limit = SIGFRAME_MAXSZ - TERMINATOR_SIZE;
    }

    /* Still not enough space?  Bad luck! */
    if (padded_size > user->limit - user->size)
    {
        return -ENOMEM;
    }

    *offset = user->size;
    user->size += padded_size;

    return 0;
}

/*
 * Allocate space for an optional record of <size> bytes in the user
 * signal frame.  The offset from the signal frame base address to the
 * allocated block is assigned to *offset.
 */
static int sigframe_alloc(struct rt_sigframe_user_layout *user, unsigned long *offset, size_t size)
{
    return __sigframe_alloc(user, offset, size, true);
}

/* Allocate the null terminator record and prevent further allocations */
static int sigframe_alloc_end(struct rt_sigframe_user_layout *user)
{
    int ret;

    /* Un-reserve the space reserved for the terminator: */
    user->limit += TERMINATOR_SIZE;

    ret = sigframe_alloc(user, &user->end_offset, sizeof(struct _aarch64_ctx));
    if (ret)
    {
        return ret;
    }

    /* Prevent further allocation: */
    user->limit = user->size;
    return 0;
}

/*
 * Determine the layout of optional records in the signal frame
 *
 * add_all: if true, lays out the biggest possible signal frame for
 *	this task; otherwise, generates a layout for the current state
 *	of the task.
 */
static int setup_sigframe_layout(struct rt_sigframe_user_layout *user, bool add_all)
{
    int ret;

    /* 目前仅支持fpsimd_context,其它上下文不支持 */

    if (system_supports_fpsimd())
    {
        ret = sigframe_alloc(user, &user->fpsimd_offset, sizeof(struct fpsimd_context));
        if (ret)
        {
            return ret;
        }
    }

    return sigframe_alloc_end(user);
}

static void __user *apply_user_offset(struct rt_sigframe_user_layout const *user,
                                      unsigned long offset)
{
    char __user *base = (char __user *)user->sigframe;

    return base + offset;
}

static int get_sigframe(struct rt_sigframe_user_layout *user, struct ksignal *ksig,
                        struct arch_context *regs)
{
    unsigned long sp, sp_top;
    int ret;

    init_user_layout(user);
    ret = setup_sigframe_layout(user, false);
    if (ret)
    {
        return ret;
    }

    sp = sp_top = sigsp(regs->sp, ksig);

    sp = round_down(sp - sizeof(struct frame_record), 16);
    user->next_frame = (struct frame_record __user *)sp;

    sp = round_down(sp, 16) - sigframe_size(user);
    user->sigframe = (struct rt_sigframe __user *)sp;

    /*
     * Check that we can actually write to the signal frame.
     */
    if (!user_access_check(user->sigframe, sp_top - sp, UACCESS_W))
    {
        return -EFAULT;
    }

    memset(user->sigframe, 0, sizeof(*user->sigframe));

    return 0;
}

static int preserve_fpsimd_context(struct fpsimd_context __user *ctx, struct arch_context *context)
{
    /* copy the FP and status/control registers */
    copy_to_user(ctx->vregs, &context->fpContext.vRegs, sizeof(ctx->vregs));

    ctx->fpsr = context->fpContext.fpsr;
    ctx->fpcr = context->fpContext.fpcr;

    /* copy the magic/size information */
    ctx->head.magic = FPSIMD_MAGIC;
    ctx->head.size = sizeof(struct fpsimd_context);

    return 0;
}

static int setup_sigframe(struct rt_sigframe_user_layout *user, struct arch_context *regs,
                          process_sigset_t *set)
{
    int ret = 0;
    struct rt_sigframe __user *sf = user->sigframe;

    /* set up the stack frame for unwinding */
    user->next_frame->fp = regs->regs[29];
    user->next_frame->lr = regs->regs[30];

    memcpy(&sf->uc.uc_mcontext.regs, &regs->regs, sizeof(sf->uc.uc_mcontext.regs));

    sf->uc.uc_mcontext.sp = regs->sp;
    sf->uc.uc_mcontext.pc = regs->elr;
    sf->uc.uc_mcontext.pstate = regs->cpsr;
    sf->uc.uc_mcontext.fault_address = 0;

    memcpy(&sf->uc.uc_sigmask, set, sizeof(*set));

    if (system_supports_fpsimd())
    {
        struct fpsimd_context __user *fpsimd_ctx = apply_user_offset(user, user->fpsimd_offset);
        ret |= preserve_fpsimd_context(fpsimd_ctx, regs);
    }

    return ret;
}

static void setup_return(struct arch_context *regs, struct ksignal *ksig,
                         struct rt_sigframe_user_layout *user, int usig)
{
    unsigned int code_size = 0;
    unsigned long sigtramp;

    regs->regs[0] = usig;
    regs->sp = (unsigned long)user->sigframe;
    regs->regs[29] = (unsigned long)&user->next_frame->fp;
    regs->elr = (unsigned long)ksig->ka.__sa_handler.sa_handler;

    /*
     * Signal delivery is a (wacky) indirect function call in
     * userspace, so simulate the same setting of BTYPE as a BLR
     * <register containing the signal handler entry point>.
     * Signal delivery to a location in a PROT_BTI guarded page
     * that is not a function entry point will now trigger a
     * SIGILL in userspace.
     *
     * If the signal handler entry point is not in a PROT_BTI
     * guarded page, this is harmless.
     */
    if (system_supports_bti())
    {
        regs->cpsr &= ~PSR_BTYPE_MASK;
        regs->cpsr |= PSR_BTYPE_C;
    }

    /* TCO (Tag Check Override) always cleared for signal handlers */
    regs->cpsr &= ~PSR_TCO_BIT;

    /* 使用用户设置的信号返回函数返回 */
    if (ksig->ka.sa_flags & SA_RESTORER)
    {
        sigtramp = (unsigned long)ksig->ka.sa_restorer;
    }
    else
    {
        /* 使用默认的信号返回函数返回(共两条指令，总共占用8个字节空间) */
        code_size = 8;

        sigtramp = (unsigned long)&user->sigframe->sig_return_code;
        memcpy(&user->sigframe->sig_return_code, sigreturn_code, code_size);

        cache_text_update((size_t)&user->sigframe->sig_return_code, code_size);
    }

    regs->regs[30] = sigtramp;
}

static int setup_rt_frame(struct ksignal *ksig, process_sigset_t *set, struct arch_context *regs)
{
    struct rt_sigframe_user_layout user = {0};
    struct rt_sigframe __user *frame;
    int err = 0;

    if (get_sigframe(&user, ksig, regs))
        return 1;

    frame = user.sigframe;

    frame->uc.uc_flags = 0;
    frame->uc.uc_link = NULL;

    __save_altstack(&frame->uc.uc_stack, regs->sp);

    err |= setup_sigframe(&user, regs, set);

    if (err == 0)
    {
        setup_return(regs, ksig, &user, ksig->sig);

        if (ksig->ka.sa_flags & SA_SIGINFO)
        {
            err |= copy_siginfo_to_user(&frame->info, &ksig->info);
            regs->regs[1] = (unsigned long)&frame->info;
            regs->regs[2] = (unsigned long)&frame->uc;
        }
    }

    return err;
}

static void handle_signal(struct ksignal *ksig, struct arch_context *regs)
{
    process_sigset_t *oldset = sigmask_to_save();

    setup_rt_frame(ksig, oldset, regs);

    arch_valid_user_regs(regs);

    signal_delivered(ksig, 0);

    TTOS_TaskEnterUserHook(ttosProcessSelf()->taskControlId);
    restore_raw_context(regs);
}

void do_syscall_restart_check(struct arch_context *context, struct ksignal *ksignal)
{
    unsigned long continue_addr = 0, restart_addr = 0;
    int retval = 0;

    /* 若是系统调用，需要考虑信号重启的问题 */
    if (SYSCALL_CONTEXT == context->type)
    {
        continue_addr = context->elr;
        restart_addr = continue_addr - (compat_thumb_mode(context) ? 2 : 4);
        retval = context->regs[0];

        if (-ERESTARTNOHAND == retval)
        {
            context->regs[0] = -EINTR;
            context->elr = continue_addr;
        }
        else if (ksignal && -EINTR == retval && (ksignal->ka.sa_flags & SA_RESTART))
        {
            context->regs[0] = context->ori_x0;
            context->elr = restart_addr;
        }
    }
}

static int parse_user_sigframe(struct user_ctxs *user, struct rt_sigframe __user *sf)
{
    struct sigcontext __user *const sc = &sf->uc.uc_mcontext;
    struct _aarch64_ctx __user *head;
    char __user *base = (char __user *)&sc->__reserved;
    size_t offset = 0;
    size_t limit = sizeof(sc->__reserved);
    bool have_extra_context = false;
    char const __user *const sfp = (char const __user *)sf;

    user->fpsimd = NULL;
    user->sve = NULL;
    user->tpidr2 = NULL;
    user->za = NULL;
    user->zt = NULL;
    user->fpmr = NULL;

    if (!IS_ALIGNED((unsigned long)base, 16))
        goto invalid;

    while (1)
    {
        int err = 0;
        u32 magic, size;
        char const __user *userp;
        struct extra_context const __user *extra;
        u64 extra_datap;
        u32 extra_size;
        struct _aarch64_ctx const __user *end;
        u32 end_magic, end_size;

        if (limit - offset < sizeof(*head))
            goto invalid;

        if (!IS_ALIGNED(offset, 16))
            goto invalid;

        head = (struct _aarch64_ctx __user *)(base + offset);
        magic = head->magic;
        size = head->size;
        if (err)
            return err;

        if (limit - offset < size)
            goto invalid;

        switch (magic)
        {
        case 0:
            if (size)
                goto invalid;

            goto done;

        case FPSIMD_MAGIC:
            if (!system_supports_fpsimd())
                goto invalid;
            if (user->fpsimd)
                goto invalid;

            user->fpsimd = (struct fpsimd_context __user *)head;
            user->fpsimd_size = size;
            break;

        case ESR_MAGIC:
            /* ignore */
            break;

        case SVE_MAGIC:
            if (!system_supports_sve() && !system_supports_sme())
                goto invalid;

            if (user->sve)
                goto invalid;

            user->sve = (struct sve_context __user *)head;
            user->sve_size = size;
            break;

        case TPIDR2_MAGIC:
            if (!system_supports_tpidr2())
                goto invalid;

            if (user->tpidr2)
                goto invalid;

            user->tpidr2 = (struct tpidr2_context __user *)head;
            user->tpidr2_size = size;
            break;

        case ZA_MAGIC:
            if (!system_supports_sme())
                goto invalid;

            if (user->za)
                goto invalid;

            user->za = (struct za_context __user *)head;
            user->za_size = size;
            break;

        case ZT_MAGIC:
            if (!system_supports_sme2())
                goto invalid;

            if (user->zt)
                goto invalid;

            user->zt = (struct zt_context __user *)head;
            user->zt_size = size;
            break;

        case FPMR_MAGIC:
            if (!system_supports_fpmr())
                goto invalid;

            if (user->fpmr)
                goto invalid;

            user->fpmr = (struct fpmr_context __user *)head;
            user->fpmr_size = size;
            break;

        case EXTRA_MAGIC:
            if (have_extra_context)
                goto invalid;

            if (size < sizeof(*extra))
                goto invalid;

            userp = (char const __user *)head;

            extra = (struct extra_context const __user *)userp;
            userp += size;

            extra_datap = extra->datap;
            extra_size = extra->size;
            if (err)
                return err;

            /* Check for the dummy terminator in __reserved[]: */

            if (limit - offset - size < TERMINATOR_SIZE)
                goto invalid;

            end = (struct _aarch64_ctx const __user *)userp;
            userp += TERMINATOR_SIZE;

            end_magic = end->magic;
            end_size = end->size;
            if (err)
                return err;

            if (end_magic || end_size)
                goto invalid;

            /* Prevent looping/repeated parsing of extra_context */
            have_extra_context = true;

            base = (__force void __user *)extra_datap;
            if (!IS_ALIGNED((unsigned long)base, 16))
                goto invalid;

            if (!IS_ALIGNED(extra_size, 16))
                goto invalid;

            if (base != userp)
                goto invalid;

            /* Reject "unreasonably large" frames: */
            if (extra_size > sfp + SIGFRAME_MAXSZ - userp)
                goto invalid;

            /*
             * Ignore trailing terminator in __reserved[]
             * and start parsing extra data:
             */
            offset = 0;
            limit = extra_size;

            if (!user_access_check(base, limit, UACCESS_R))
                goto invalid;

            continue;

        default:
            goto invalid;
        }

        if (size < sizeof(*head))
            goto invalid;

        if (limit - offset < size)
            goto invalid;

        offset += size;
    }

done:
    return 0;

invalid:
    return -EINVAL;
}

static int restore_fpsimd_context(struct user_ctxs *user, struct arch_context *context)
{
    /* check the size information */
    if (user->fpsimd_size != sizeof(struct fpsimd_context))
    {
        return -EINVAL;
    }

    /* copy the FP and status/control registers */
    copy_from_user(&context->fpContext.vRegs, &(user->fpsimd->vregs),
                   sizeof(context->fpContext.vRegs));

    context->fpContext.fpsr = user->fpsimd->fpsr;
    context->fpContext.fpcr = user->fpsimd->fpcr;

    return 0;
}

static int restore_sigframe(struct arch_context *regs, struct rt_sigframe __user *sf)
{
    process_sigset_t set;
    int ret = 0;
    struct user_ctxs user;

    ret = copy_from_user(&set, &sf->uc.uc_sigmask, sizeof(set));
    if (ret == 0)
    {
        set_current_blocked(&set);
    }
    else
    {
        return -EINVAL;
    }

    memcpy(&regs->regs, &sf->uc.uc_mcontext.regs, sizeof(regs->regs));

    regs->sp = sf->uc.uc_mcontext.sp;
    regs->elr = sf->uc.uc_mcontext.pc;
    regs->cpsr = sf->uc.uc_mcontext.pstate;

    signal_forget_syscall(regs);

    ret |= !arch_valid_user_regs(regs);
    if (ret == 0)
    {
        ret = parse_user_sigframe(&user, sf);
    }

    if (ret == 0 && system_supports_fpsimd())
    {
        if (!user.fpsimd)
        {
            return -EINVAL;
        }

        if (user.sve)
        {
            ret = restore_sve_fpsimd_context(&user);
        }
        else
        {
            ret = restore_fpsimd_context(&user, regs);
        }
    }

    if (ret == 0 && system_supports_tpidr2() && user.tpidr2)
    {
        ret = restore_tpidr2_context(&user);
    }

    if (ret == 0 && system_supports_fpmr() && user.fpmr)
    {
        ret = restore_fpmr_context(&user);
    }

    if (ret == 0 && system_supports_sme() && user.za)
    {
        ret = restore_za_context(&user);
    }

    if (ret == 0 && system_supports_sme2() && user.zt)
    {
        ret = restore_zt_context(&user);
    }

    return ret;
}

int rt_sigreturn(struct arch_context *context)
{
    struct arch_context *regs = context;
    struct rt_sigframe __user *frame;

    /*
     * Since we stacked the signal on a 128-bit boundary, then 'sp' should
     * be word aligned here.
     */
    if (regs->sp & 15)
    {
        goto badframe;
    }

    frame = (struct rt_sigframe __user *)regs->sp;
    signal_del_running(frame->info.si_signo);

    if (!user_access_check(frame, sizeof(*frame), UACCESS_R))
    {
        goto badframe;
    }

    if (restore_sigframe(regs, frame))
    {
        goto badframe;
    }

    if (restore_altstack(&frame->uc.uc_stack, regs->sp))
    {
        goto badframe;
    }

    return 0;

badframe:
    KLOG_E("sigframe err, so stop the self");
    kernel_tkill(ttosProcessSelf()->taskControlId->tid, SIGSEGV);
    return 0;
}

void setup_restart_syscall(struct arch_context *regs)
{
    regs->regs[8] = __NR_restart_syscall;
}

static void syscall_set_return_value(pcb_t pcb, struct arch_context *regs, int error, long val)
{
    if (error)
    {
        val = error;
    }

    regs->regs[0] = val;
}

int arch_do_signal(struct arch_context *regs)
{
    bool exist_signal = false;
    unsigned long continue_addr = 0, restart_addr = 0;
    int retval = 0;
    struct ksignal ksig;
    bool is_in_syscall = in_syscall(regs);

    /*
     * If we were from a system call, check for system call restarting...
     */
    if (is_in_syscall)
    {
        continue_addr = regs->elr;
        restart_addr = continue_addr - (compat_thumb_mode(regs) ? 2 : 4);
        retval = regs->regs[0];

        /*
         * Avoid additional syscall restarting via ret_to_user.
         */
        signal_forget_syscall(regs);

        /*
         * Prepare for system call restart. We do this here so that a
         * debugger will see the already changed PC.
         */
        switch (retval)
        {
        case -ERESTARTNOHAND:
        case -ERESTARTSYS:
        case -ERESTARTNOINTR:
        case -ERESTART_RESTARTBLOCK:
            regs->regs[0] = regs->ori_x0;
            regs->elr = restart_addr;
            break;
        }
    }

    /*
     * Get the signal to deliver. When running under ptrace, at this point
     * the debugger may change all of our registers.
     */
    exist_signal = get_signal(&ksig);

    if (regs->elr == restart_addr &&
        (retval == -ERESTARTNOHAND || retval == -ERESTART_RESTARTBLOCK ||
         (retval == -ERESTARTSYS && !(ksig.ka.sa_flags & SA_RESTART))))
    {
        syscall_set_return_value(ttosProcessSelf(), regs, -EINTR, 0);
        regs->elr = continue_addr;
    }

    if (exist_signal)
    {
        handle_signal(&ksig, regs);
        assert(0);
    }

    /*
     * Handle restarting a different system call. As above, if a debugger
     * has chosen to restart at a different PC, ignore the restart.
     */
    if (is_in_syscall && regs->elr == restart_addr)
    {
        if (retval == -ERESTART_RESTARTBLOCK)
            setup_restart_syscall(regs);
        // user_rewind_single_step(current);
    }

    restore_saved_sigmask();

    return 0;
}