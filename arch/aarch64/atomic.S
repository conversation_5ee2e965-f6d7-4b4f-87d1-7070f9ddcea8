/************************************************************************
 *              科东(广州)软件科技有限公司 版权所有
 *   Copyright (C) 2022 Intewell Inc. All Rights Reserved.
 ***********************************************************************/

/*
 * 修改历史：
 * 2024-03-11    毛玉泽，科东(广州)软件科技有限公司
 *               创建该文件。
 */

/**
 * @file： atomic.S
 * @brief：
 *	    <li>原子操作代码。</li>
 */

/************************头 文 件******************************/
#define ASM_USE
#include <asm.h>
#include <cp15.h>

/************************宏 定 义******************************/

/************************类型定义******************************/

/************************外部声明******************************/

/************************前向声明******************************/

/************************模块变量******************************/

/************************函数实现******************************/

.text

/*
* @brief 
*    将target指向的值增加value，并返回target原有指向的值。
* @param[out]||[in]: target: 需要增加值的地址。
* @param[in]: value: 需要增加的值
* @retval target原有指向的值
*/
/* T_UWORD atomic32_add(T_UWORD * target, T_UWORD value) */
ENTRY(atomic32_add)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	add	w3, w2, w1	/* add xord */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32_add)	

/*
* @brief 
*	 将target指向的值增加value，并返回target增加后的值。
* @param[out]||[in]: target: 需要增加值的地址。
* @param[in]: value: 需要增加的值
* @retval target增加后的值
*/
/* T_UWORD atomic32_add_return(T_UWORD * target, T_UWORD value) */
ENTRY(atomic32_add_return)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	add	w3, w2, w1	/* add xord */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w3		/* return old value */
	ret
ENDPROC(atomic32_add_return)

/*
* @brief 
*       将target指向的值与value位或，并返回target原有的值。
* @param  target: 需要位或值的地址。
* @param[in]  value: 需要位或的值。
* @return target原有指向的值。
*/
/* T_UWORD atomic32Or(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32Or)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	orr	w3, w2, w1	/* OR xord */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32Or)
     
/*
* @brief 
*       将target指向的值与value位与，并返回target原有的值。
* @param  target: 需要位与值的地址。
* @param[in]  value: 需要位与的值。
* @return target原有指向的值。
*/
/* T_UWORD atomic32And(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32And)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	and	w3, w2, w1	/* OR xord */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32And)

/*
* @brief 
*       设置target指向的值为value，并返回target原有的值。
* @param  target: 需要设置值的地址。
* @param[in]  value: 需要设置的值。
* @return target原有指向的值。
*/
/* T_UWORD atomic32_set(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_set)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	stlxr	w4, w1, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32_set)

/*
* @brief 
*       获取target指向的值。
* @param  target: 需要获取值的地址。
* @return target指向的值。
*/
/*
* @brief 
*       获取target指向的值。
* @param  target: 需要获取值的地址。
* @return 获取target指向的值
*/
/* T_UWORD atomic32Get(T_UWORD * target) */
ENTRY(atomic32Get)
	ldr	   w0, [x0]        
	ret
ENDPROC(atomic32Get)

/*
* @brief
*       将target指向的值增加1，并返回target原有的值。
* @param  target: 需要增加值的地址。
* @return target原有指向的值
*/
/* T_UWORD atomic32_inc(T_UWORD * target) */
ENTRY(atomic32_inc)
1:
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	add	w3, w2, #1	/* add 1 */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32_inc)

/*
* @brief
*		将target指向的值增加1，并返回target增加后的值。
* @param  target: 需要增加值的地址。
* @return target增加后的值
*/
/* T_UWORD atomic32_inc_return(T_UWORD * target) */
ENTRY(atomic32_inc_return)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	add	w3, w2, #1	/* add 1 */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w3		/* return old value */
	ret
ENDPROC(atomic32_inc_return)

/*
* @brief
*       将target指向的值减去1，并返回target原有的值。
* @param  target: 需要减去值的地址。
* @return target原有指向的值
*/
/* T_UWORD atomic32_dec(T_UWORD * target) */
ENTRY(atomic32_dec)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	sub	w3, w2, #1	/* add 1 */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32_dec)

/*
* @brief
*		将target指向的值减去1，并返回target减去后的值。
* @param  target: 需要减去值的地址。
* @return target减去后的值
*/
/* T_UWORD atomic32_dec_return(T_UWORD * target) */
ENTRY(atomic32_dec_return)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	sub	w3, w2, #1	/* add 1 */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w3		/* return old value */
	ret
ENDPROC(atomic32_dec_return)

/*
* @brief 
*       将target指向的值与oldValue对比，如果相等，设置target指向的值为newValue，
*       并返回TRUE；如果不相等，则直接返回FALSE。
* @@param  target: 需要对比值的地址。
* @param[in]  oldValue: 需要对比的值。
* @param[in]  newValue: 需要设置的值。
* @return TURE:target指向的值与oldValue相等
* @return FALSE:target指向的值与oldValue不相等
*/
/* BOOL atomic32_cas(T_UWORD * target,T_UWORD oldValue,T_UWORD newValue) */
ENTRY(atomic32_cas)
1:
	ldaxr	w3, [x0]	/* load target value and mark exclusive access */
	cmp w3, w1
	bne 2f		/* store successful? if not, retry */
	stlxr	w4, w2, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, #1		/* return TRUE */
	ret
2:
	clrex  /* clears the local monitor of the executing PE */
	mov	w0, #0		/* return FALSE */
	ret
ENDPROC(atomic32_cas)

/*
* @brief 
*       将target指向的值减去value，并返回target原有的值。
* @param  target: 需要减去值的地址。
* @param[in]  value: 需要减去的值。
* @return target原有指向的值
*/
/* T_UWORD atomic32_sub(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_sub)
1:
	ldaxr	w2, [x0]	/* load target value and mark exclusive access */
	sub	w3, w2, w1	/* sub value */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return target value */
	ret
ENDPROC(atomic32_sub)

/*
* @brief 
*		将target指向的值减去value，并返回target减去后的值。
* @param  target: 需要减去值的地址。
* @param[in]  value: 需要减去的值。
* @return target减去后的值
*/
/* T_UWORD atomic32_sub_return(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32_sub_return)
1:
	ldaxr	w2, [x0]	/* load target value and mark exclusive access */
	sub	w3, w2, w1	/* sub value */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w3		/* return target value */
	ret
ENDPROC(atomic32_sub_return)

/*
* @brief 
*       清除target指向的值，并返回target原有的值。
* @param  target: 需要清除值的地址。
* @return target原有指向的值
*/
/* T_UWORD atomic32Clear(T_UWORD * target) */
ENTRY(atomic32Clear)
	mov x1, #0
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	stlxr	w4, w1, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32Clear)

/*
* @brief 
*       将target指向的值与value位异或，并返回target原有的值。
* @param  target: 需要位异或值的地址。
* @param[in]  value: 需要位异或的值。
* @return target原有指向的值
*/
/* T_UWORD atomic32Xor(T_UWORD * target,T_UWORD value) */
ENTRY(atomic32Xor)
1:
	ldaxr	w2, [x0]	/* load old value and mark exclusive access */
	eor	w3, w2, w1	/* XOR xord */
	stlxr	w4, w3, [x0]	/* try to store nex value */
	tbnz	w4, #0, 1b		/* store successful? if not, retry */

	mov	w0, w2		/* return old value */
	ret
ENDPROC(atomic32Xor)
