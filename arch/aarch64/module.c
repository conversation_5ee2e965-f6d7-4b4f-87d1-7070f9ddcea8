﻿
#ifdef CONFIG_MODULES
#include <elf.h>
#include <asm_module.h>

#include <system/types.h>
#include <errno.h>
#include <module.h>
#include <inttypes.h>
/*
#include <linux/gfp.h>
#include <linux/kasan.h>
#include <linux/kernel.h>
#include <linux/mm.h>
#include <linux/moduleloader.h>
#include <linux/vmalloc.h>
#include <asm/alternative.h>
#include <asm/insn.h>
#include <asm/sections.h>
*/
#undef KLOG_TAG
#define KLOG_TAG "Module"
#include <klog.h>

#define cpu_to_le64(x) ((uint64_t)(x))
#define le64_to_cpu(x) ((uint64_t)(x))
#define cpu_to_le32(x) ((uint32_t)(x))
#define le32_to_cpu(x) ((uint32_t)(x))
#define cpu_to_le16(x) ((uint16_t)(x))
#define le16_to_cpu(x) ((uint16_t)(x))


# define fallthrough                    do {} while (0)  /* fallthrough */

#ifndef BIT /* in case third-party project also defined marco BIT */
#define BIT(nr) (1ULL << (nr))
#endif

/* For triggering a fault on purpose (reserved) */

#define FAULT_BRK_IMM           0x100

/* BRK instruction encoding
 * The #imm16 value should be placed at bits[20:5] within BRK ins
 */

#define AARCH64_BREAK_MON       0xd4200000

/* BRK instruction for provoking a fault on purpose
 * Unlike kgdb, #imm16 value with unallocated handler is used for faulting.
 */

#define AARCH64_BREAK_FAULT     (AARCH64_BREAK_MON | (FAULT_BRK_IMM << 5))

#define ADR_IMM_HILOSPLIT       2
#define ADR_IMM_SIZE            (2 * 1024 * 1024)
#define ADR_IMM_LOMASK          ((1 << ADR_IMM_HILOSPLIT) - 1)
#define ADR_IMM_HIMASK          ((ADR_IMM_SIZE >> ADR_IMM_HILOSPLIT) - 1)
#define ADR_IMM_LOSHIFT         29
#define ADR_IMM_HISHIFT         5


enum aarch64_reloc_op {
    RELOC_OP_NONE,
    RELOC_OP_ABS,
    RELOC_OP_PREL,
    RELOC_OP_PAGE,
};

enum aarch64_insn_movw_imm_type
{
    AARCH64_INSN_IMM_MOVNZ,
    AARCH64_INSN_IMM_MOVKZ,
};
enum aarch64_insn_imm_type 
{
    AARCH64_INSN_IMM_ADR,
    AARCH64_INSN_IMM_26,
    AARCH64_INSN_IMM_19,
    AARCH64_INSN_IMM_16,
    AARCH64_INSN_IMM_14,
    AARCH64_INSN_IMM_12,
    AARCH64_INSN_IMM_9,
    AARCH64_INSN_IMM_7,
    AARCH64_INSN_IMM_6,
    AARCH64_INSN_IMM_S,
    AARCH64_INSN_IMM_R,
    AARCH64_INSN_IMM_N,
    AARCH64_INSN_IMM_MAX
};

static u64 do_reloc(enum aarch64_reloc_op reloc_op, __le32 *place, u64 val)
{
    switch (reloc_op) 
    {
    case RELOC_OP_ABS:
      return val;
    case RELOC_OP_PREL:
      return val - (u64)place;
    case RELOC_OP_PAGE:
      return (val & ~0xfff) - ((u64)place & ~0xfff);
    case RELOC_OP_NONE:
      return 0;
    }

    KLOG_E("do_reloc: unknown relocation operation %d\n", reloc_op);
    return 0;
}

static int reloc_data(enum aarch64_reloc_op op, void *place, u64 val, int len)
{
    s64 sval = do_reloc(op, place, val);
    
    /*
    * The ELF psABI for AArch64 documents the 16-bit and 32-bit place
    * relative and absolute relocations as having a range of [-2^15, 2^16)
    * or [-2^31, 2^32), respectively. However, in order to be able to
    * detect overflows reliably, we have to choose whether we interpret
    * such quantities as signed or as unsigned, and stick with it.
    * The way we organize our address space requires a signed
    * interpretation of 32-bit relative references, so let's use that
    * for all R_AARCH64_PRELxx relocations. This means our upper
    * bound for overflow detection should be Sxx_MAX rather than Uxx_MAX.
    */
    
    switch (len) 
    {
    case 16:
        *(s16 *)place = sval;
        switch (op) 
        {
        case RELOC_OP_ABS:
          if (sval < 0 || sval > UINT16_MAX)
            return -ERANGE;
          break;
        case RELOC_OP_PREL:
          if (sval < INT16_MIN || sval > INT16_MAX)
            return -ERANGE;
          break;
        default:
          KLOG_E("Invalid 16-bit data relocation (%d)\n", op);
          return 0;
        }
        break;

    case 32:
        *(s32 *)place = sval;
        switch (op) 
        {
        case RELOC_OP_ABS:
          if (sval < 0 || sval > UINT32_MAX)
            return -ERANGE;
          break;
        case RELOC_OP_PREL:
          if (sval < INT32_MIN || sval > INT32_MAX)
            return -ERANGE;
          break;
        default:
          KLOG_E("Invalid 32-bit data relocation (%d)\n", op);
          return 0;
        }
        break;

    case 64:
        *(s64 *)place = sval;
        break;

    default:
        KLOG_E("Invalid length (%d) for data relocation\n", len);
        return 0;

    }
    return 0;
}


u32 aarch64_insn_encode_immediate(enum aarch64_insn_imm_type type,
				  u32 insn, u64 imm)
{
    u32 immlo, immhi, mask;
    int shift;
    
    if (insn == AARCH64_BREAK_FAULT)
      return AARCH64_BREAK_FAULT;
    
    switch (type) 
    {
    case AARCH64_INSN_IMM_ADR:
      shift = 0;
      immlo = (imm & ADR_IMM_LOMASK) << ADR_IMM_LOSHIFT;
      imm >>= ADR_IMM_HILOSPLIT;
      immhi = (imm & ADR_IMM_HIMASK) << ADR_IMM_HISHIFT;
      imm = immlo | immhi;
      mask = ((ADR_IMM_LOMASK << ADR_IMM_LOSHIFT) |
              (ADR_IMM_HIMASK << ADR_IMM_HISHIFT));
      break;

    case AARCH64_INSN_IMM_26:
      mask = BIT(26) - 1;
      shift = 0;
      break;

    case AARCH64_INSN_IMM_19:
      mask = BIT(19) - 1;
      shift = 5;
      break;

    case AARCH64_INSN_IMM_16:
      mask = BIT(16) - 1;
      shift = 5;
      break;

    case AARCH64_INSN_IMM_14:
      mask = BIT(14) - 1;
      shift = 5;
      break;

    case AARCH64_INSN_IMM_12:
      mask = BIT(12) - 1;
      shift = 10;
      break;

    case AARCH64_INSN_IMM_9:
      mask = BIT(9) - 1;
      shift = 12;
      break;

    case AARCH64_INSN_IMM_7:
      mask = BIT(7) - 1;
      shift = 15;
      break;

    case AARCH64_INSN_IMM_6:
    case AARCH64_INSN_IMM_S:
      mask = BIT(6) - 1;
      shift = 10;
      break;

    case AARCH64_INSN_IMM_R:
      mask = BIT(6) - 1;
      shift = 16;
      break;

    case AARCH64_INSN_IMM_N:
      mask = 1;
      shift = 22;
      break;

    default:
      KLOG_E("aarch64_insn_encode_immediate: unknown immediate encoding %d\n", type);
      return AARCH64_BREAK_FAULT;
    }
    
    /* Update the immediate field. */
    insn &= ~(mask << shift);
    insn |= (imm & mask) << shift;
    
    return insn;
}


static int reloc_insn_movw(enum aarch64_reloc_op op, __le32 *place, u64 val,
			   int lsb, enum aarch64_insn_movw_imm_type imm_type)
{
    u64 imm;
    s64 sval;
    u32 insn = le32_to_cpu(*place);
    
    sval = do_reloc(op, place, val);
    imm = sval >> lsb;
    
    if (imm_type == AARCH64_INSN_IMM_MOVNZ) 
    {
      /*
      * For signed MOVW relocations, we have to manipulate the
      * instruction encoding depending on whether or not the
      * immediate is less than zero.
      */
      insn &= ~(3 << 29);
      if (sval >= 0) 
      {
        /* >=0: Set the instruction to MOVZ (opcode 10b). */
        insn |= 2 << 29;
      }
      else 
      {
        /*
        * <0: Set the instruction to MOVN (opcode 00b).
        *     Since we've masked the opcode already, we
        *     don't need to do anything other than
        *     inverting the new immediate field.
        */
        imm = ~imm;
      }
    }
    
    /* Update the instruction with the new encoding. */
    insn = aarch64_insn_encode_immediate(AARCH64_INSN_IMM_16, insn, imm);
    *place = cpu_to_le32(insn);
    
    if (imm > UINT16_MAX)
      return -ERANGE;
    
    return 0;
}
    
static int reloc_insn_imm(enum aarch64_reloc_op op, __le32 *place, u64 val,
                             int lsb, int len, enum aarch64_insn_imm_type imm_type)
{
    u64 imm, imm_mask;
    s64 sval;
    u32 insn = le32_to_cpu(*place);
    
    /* Calculate the relocation value. */
    sval = do_reloc(op, place, val);
    sval >>= lsb;
    
    /* Extract the value bits and shift them to bit 0. */
    imm_mask = (BIT(lsb + len) - 1) >> lsb;
    imm = sval & imm_mask;
    
    /* Update the instruction's immediate field. */
    insn = aarch64_insn_encode_immediate(imm_type, insn, imm);
    *place = cpu_to_le32(insn);
    
    /*
    * Extract the upper value bits (including the sign bit) and
    * shift them to bit 0.
    */
    sval = (s64)(sval & ~(imm_mask >> 1)) >> (len - 1);
    
    /*
    * Overflow has occurred if the upper bits are not all equal to
    * the sign bit of the value.
    */
    if ((u64)(sval + 1) >= 2)
      return -ERANGE;
    
    return 0;
}

static int reloc_insn_adrp(struct module *mod, Elf64_Shdr *sechdrs,
			   __le32 *place, u64 val)
{
    u32 insn;
    
    if (!is_forbidden_offset_for_adrp(place))
      return reloc_insn_imm(RELOC_OP_PAGE, place, val, 12, 21, AARCH64_INSN_IMM_ADR);
    
    /* patch ADRP to ADR if it is in range */
    if (!reloc_insn_imm(RELOC_OP_PREL, place, val & ~0xfff, 0, 21, AARCH64_INSN_IMM_ADR)) 
    {
      insn = le32_to_cpu(*place);
      insn &= (~ BIT(31) );
    }
    else
    {
      /* out of range for ADR -> emit a veneer */
      /*  nuttx重定位代码中,out of range直接报错
          而linux重定位代码中,会进行emit aveneer,成功的话,就会重新生成指令
          采用nuttx的实现 
      val = module_emit_veneer_for_adrp(mod, sechdrs, place, val & ~0xfff);
      if (!val)
        return -ENOEXEC;
      insn = aarch64_insn_gen_branch_imm((u64)place, val, AARCH64_INSN_BRANCH_NOLINK);
      */
      return -ENOEXEC;
    }
    
    *place = cpu_to_le32(insn);
    return 0;
}

int apply_relocate_add(Elf64_Shdr *sechdrs,
		       const char *strtab,
		       unsigned int symindex,
		       unsigned int relsec,
		       struct module *me)
{
    unsigned int i;
    int ovf;
    bool overflow_check;
    Elf64_Sym *sym;
    void *loc;
    u64 val;
    Elf64_Rela *rel = (void *)sechdrs[relsec].sh_addr;

    for (i = 0; i < sechdrs[relsec].sh_size / sizeof(*rel); i++) 
    {
      /* loc corresponds to P in the AArch64 ELF document. */
      loc = (void *)sechdrs[sechdrs[relsec].sh_info].sh_addr + rel[i].r_offset;
  
      /* sym is the ELF symbol we're referring to. */
      sym = (Elf64_Sym *)sechdrs[symindex].sh_addr + ELF64_R_SYM(rel[i].r_info);
  
      /* val corresponds to (S + A) in the AArch64 ELF document. */
      val = sym->st_value + rel[i].r_addend;
  
      /* Check for overflow by default. */
      overflow_check = true;

      /* Perform the static relocation. */
      switch (ELF64_R_TYPE(rel[i].r_info)) 
      {
      /* 
      **NULL relocations. 
      **NULL relocations.
      */
      case R_AARCH64_NONE:
        ovf = 0;
        break;
  
      /* 
      **Data relocations. 
      **Data relocations.
      */
      case R_AARCH64_ABS64:
        overflow_check = false;
        ovf = reloc_data(RELOC_OP_ABS, loc, val, 64);
        break;
  
      case R_AARCH64_ABS32:
        ovf = reloc_data(RELOC_OP_ABS, loc, val, 32);
        break;
  
      case R_AARCH64_ABS16:
        ovf = reloc_data(RELOC_OP_ABS, loc, val, 16);
        break;
  
      case R_AARCH64_PREL64:
        overflow_check = false;
        ovf = reloc_data(RELOC_OP_PREL, loc, val, 64);
        break;
  
      case R_AARCH64_PREL32:
        ovf = reloc_data(RELOC_OP_PREL, loc, val, 32);
        break;
  
      case R_AARCH64_PREL16:
        ovf = reloc_data(RELOC_OP_PREL, loc, val, 16);
        break;

      /* 
      ** MOVW instruction relocations.
      ** MOVW instruction relocations.
      */
      case R_AARCH64_MOVW_UABS_G0_NC:
        overflow_check = false;
        fallthrough;
      case R_AARCH64_MOVW_UABS_G0:
        ovf = reloc_insn_movw(RELOC_OP_ABS, loc, val, 0, AARCH64_INSN_IMM_MOVKZ);
        break;

      case R_AARCH64_MOVW_UABS_G1_NC:
        overflow_check = false;
        fallthrough;
      case R_AARCH64_MOVW_UABS_G1:
        ovf = reloc_insn_movw(RELOC_OP_ABS, loc, val, 16, AARCH64_INSN_IMM_MOVKZ);
        break;

      case R_AARCH64_MOVW_UABS_G2_NC:
        overflow_check = false;
        fallthrough;
      case R_AARCH64_MOVW_UABS_G2:
        ovf = reloc_insn_movw(RELOC_OP_ABS, loc, val, 32, AARCH64_INSN_IMM_MOVKZ);
        break;
  
      case R_AARCH64_MOVW_UABS_G3:
        /* We're using the top bits so we can't overflow. */
        overflow_check = false;
        ovf = reloc_insn_movw(RELOC_OP_ABS, loc, val, 48, AARCH64_INSN_IMM_MOVKZ);
        break;
  
      case R_AARCH64_MOVW_SABS_G0:
        ovf = reloc_insn_movw(RELOC_OP_ABS, loc, val, 0, AARCH64_INSN_IMM_MOVNZ);
        break;
  
      case R_AARCH64_MOVW_SABS_G1:
        ovf = reloc_insn_movw(RELOC_OP_ABS, loc, val, 16, AARCH64_INSN_IMM_MOVNZ);
        break;
  
      case R_AARCH64_MOVW_SABS_G2:
        ovf = reloc_insn_movw(RELOC_OP_ABS, loc, val, 32, AARCH64_INSN_IMM_MOVNZ);
        break;
  
      case R_AARCH64_MOVW_PREL_G0_NC:
        overflow_check = false;
        ovf = reloc_insn_movw(RELOC_OP_PREL, loc, val, 0, AARCH64_INSN_IMM_MOVKZ);
        break;
  
      case R_AARCH64_MOVW_PREL_G0:
        ovf = reloc_insn_movw(RELOC_OP_PREL, loc, val, 0, AARCH64_INSN_IMM_MOVNZ);
        break;
  
      case R_AARCH64_MOVW_PREL_G1_NC:
        overflow_check = false;
        ovf = reloc_insn_movw(RELOC_OP_PREL, loc, val, 16, AARCH64_INSN_IMM_MOVKZ);
        break;
  
      case R_AARCH64_MOVW_PREL_G1:
        ovf = reloc_insn_movw(RELOC_OP_PREL, loc, val, 16, AARCH64_INSN_IMM_MOVNZ);
        break;
  
      case R_AARCH64_MOVW_PREL_G2_NC:
        overflow_check = false;
        ovf = reloc_insn_movw(RELOC_OP_PREL, loc, val, 32, AARCH64_INSN_IMM_MOVKZ);
        break;
  
      case R_AARCH64_MOVW_PREL_G2:
        ovf = reloc_insn_movw(RELOC_OP_PREL, loc, val, 32, AARCH64_INSN_IMM_MOVNZ);
        break;
  
      case R_AARCH64_MOVW_PREL_G3:
        /* We're using the top bits so we can't overflow. */
        overflow_check = false;
        ovf = reloc_insn_movw(RELOC_OP_PREL, loc, val, 48, AARCH64_INSN_IMM_MOVNZ);
        break;

      /* 
      ** Immediate instruction relocations.
      ** Immediate instruction relocations.
      */
      case R_AARCH64_LD_PREL_LO19:
        ovf = reloc_insn_imm(RELOC_OP_PREL, loc, val, 2, 19, AARCH64_INSN_IMM_19);
        break;
  
      case R_AARCH64_ADR_PREL_LO21:
      ovf = reloc_insn_imm(RELOC_OP_PREL, loc, val, 0, 21, AARCH64_INSN_IMM_ADR);
      break;
  
      case R_AARCH64_ADR_PREL_PG_HI21_NC:
        overflow_check = false;
        fallthrough;
      case R_AARCH64_ADR_PREL_PG_HI21:
        ovf = reloc_insn_adrp(me, sechdrs, loc, val);
        if (ovf && ovf != -ERANGE)
          return ovf;
        break;
  
      case R_AARCH64_ADD_ABS_LO12_NC:
      case R_AARCH64_LDST8_ABS_LO12_NC:
        overflow_check = false;
        ovf = reloc_insn_imm(RELOC_OP_ABS, loc, val, 0, 12, AARCH64_INSN_IMM_12);
        break;
  
      case R_AARCH64_LDST16_ABS_LO12_NC:
        overflow_check = false;
        ovf = reloc_insn_imm(RELOC_OP_ABS, loc, val, 1, 11, AARCH64_INSN_IMM_12);
        break;
  
      case R_AARCH64_LDST32_ABS_LO12_NC:
        overflow_check = false;
        ovf = reloc_insn_imm(RELOC_OP_ABS, loc, val, 2, 10, AARCH64_INSN_IMM_12);
        break;
  
      case R_AARCH64_LDST64_ABS_LO12_NC:
        overflow_check = false;
        ovf = reloc_insn_imm(RELOC_OP_ABS, loc, val, 3, 9, AARCH64_INSN_IMM_12);
        break;
  
      case R_AARCH64_LDST128_ABS_LO12_NC:
        overflow_check = false;
        ovf = reloc_insn_imm(RELOC_OP_ABS, loc, val, 4, 8, AARCH64_INSN_IMM_12);
        break;
  
      case R_AARCH64_TSTBR14:
        ovf = reloc_insn_imm(RELOC_OP_PREL, loc, val, 2, 14, AARCH64_INSN_IMM_14);
        break;
  
      case R_AARCH64_CONDBR19:
        ovf = reloc_insn_imm(RELOC_OP_PREL, loc, val, 2, 19, AARCH64_INSN_IMM_19);
        break;
  	
      case R_AARCH64_JUMP26:
      case R_AARCH64_CALL26:
        ovf = reloc_insn_imm(RELOC_OP_PREL, loc, val, 2, 26, AARCH64_INSN_IMM_26);
      /*  nuttx重定位代码中,返回out of range下,会直接报错
          而linux重定位代码中,会进行emit plt entry,成功的话,再重新执行一遍重定位,如下所示
          采用nuttx的实现 

        if ( IS_ENABLED(CONFIG_ARM64_MODULE_PLTS) && ovf == -ERANGE ) 
        {
          val = module_emit_plt_entry(me, sechdrs, loc, &rel[i], sym);
          if (!val)
            return -ENOEXEC;
          ovf = reloc_insn_imm(RELOC_OP_PREL, loc, val, 2, 26, AARCH64_INSN_IMM_26);
        }
      */
        break;
        
      default:
        KLOG_E("module %s: unsupported RELA relocation: %" PRIu64, me->name,
                ELF64_R_TYPE(rel[i].r_info));
        return -ENOEXEC;

      }
      
      if (overflow_check && ovf == -ERANGE)
        goto overflow;
    
    }

    return 0;

overflow:
    KLOG_E("module %s: overflow in relocation type %d val 0x%" PRIx64,
            me->name, (int)ELF64_R_TYPE(rel[i].r_info), val);
    return -ENOEXEC;
}

int apply_relocate(Elf_Shdr *sechdrs,
				const char *strtab,
				unsigned int symindex,
				unsigned int relsec,
				struct module *me)
{
    KLOG_E("REL relocation unsupported, REL\n");
    return -ENOEXEC;
}

int klp_apply_section_relocs(struct module *pmod, Elf_Shdr *sechdrs,
				 const char *shstrtab, const char *strtab,
				 unsigned int symindex, unsigned int secindex,
				 const char *objname)
{
    KLOG_E("module : REL relocation unsupported, LIVEPATCH\n");
    return -ENOEXEC;
}
#if 0

static const Elf_Shdr *find_section_header(const Elf_Ehdr *hdr,
				    const Elf_Shdr *sechdrs,
				    const char *name)
{
    const Elf_Shdr *s, *se;
    const char *secstrs = (void *)hdr + sechdrs[hdr->e_shstrndx].sh_offset;
    
    for (s = sechdrs, se = sechdrs + hdr->e_shnum; s < se; s++) 
    {
      if (strcmp(name, secstrs + s->sh_name) == 0)
        return s;
    }

    return NULL;
}
#endif

int elfmodule_finalize(const Elf_Ehdr *hdr,
		    const Elf_Shdr *sechdrs,
		    struct module *me)
{
    //待实现
    return 0;
}
#endif
