/**
 * @file    arch/arm_common/generic_timer.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 2572306a 2024-07-09 修复可能造成释放内存后还在使用的bug
 * c295493f 2024-07-02 移除include 路径中clock
 * 8334d47a 2024-07-02 移除头文件路径中的driver
 * ac006b61 2024-07-02 移除一级ttos目录
 * 6fd7a00d 2024-05-22 整理libk并格式化代码
 * be1a0b92 2024-05-21 irq命名修改为ttos_pic_xxx, 头文件引用删<irq.h>
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * f9f81129 2024-05-07 调整部分timer代码。
 * 8ae4d067 2024-05-07 1.添加链接libgcc.a库 2.添加pipe支持 3.添加time event支持
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <barrier.h>
#include <clock/clockchip.h>
#include <clock/clocksource.h>
#include <cpu.h>
#include <cpu_generic_timer.h>
#include <cpuid.h>
#include <driver/generic_timer.h>
#include <driver/of.h>
#include <kmalloc.h>
#include <stdio.h>
#include <string.h>
#include <system/kconfig.h>
#include <time/ktime.h>
#include <ttos_pic.h>
#include <ttos_time.h>

#undef DEBUG

#ifdef DEBUG
#define DPRINTF(msg...) KLOG_E(msg)
#else
#define DPRINTF(msg...)
#endif

#define KLOG_TAG "generic_timer.c"
#include <klog.h>

#define ARM_TIMER_PHYS_NONSECURE (30)
#define ARM_TIMER_PHYS_SECURE (29)
#define ARM_TIMER_VIRT (27)
#define ARM_TIMER_HYP (26)

enum gen_timer_type
{
    GENERIC_SPHYSICAL_TIMER,
    GENERIC_PHYSICAL_TIMER,
    GENERIC_VIRTUAL_TIMER,
    GENERIC_HYPERVISOR_TIMER,
};

static u32 generic_timer_hz = 0;
static u32 generic_timer_mult = 0;
static u32 generic_timer_shift = 0;
static u32 timer_irq[4];

static void generic_timer_get_freq(void)
{
    if (generic_timer_hz == 0)
    {
        generic_timer_hz = ttos_time_freq_get();
    }
}

static u64 generic_counter_read(struct timer_clocksource *cs)
{
    return ttos_time_count_get();
}

int arch_timer_clocksource_init(void)
{
    struct timer_clocksource *cs;

    generic_timer_get_freq();
    if (generic_timer_hz == 0)
    {
        return -1;
    }

    cs = zalloc(sizeof(struct timer_clocksource));
    if (!cs)
    {
        return -1;
    }

    cs->name = "gen-timer";
    cs->rating = 400;
    cs->read = &generic_counter_read;
    cs->mask = CLOCKSOURCE_MASK(56);
    cs->freq = generic_timer_hz;
    clocks_calc_mult_shift(&cs->mult, &cs->shift, generic_timer_hz, NSEC_PER_SEC, 10);
    generic_timer_mult = cs->mult;
    generic_timer_shift = cs->shift;
    cs->priv = NULL;

    return clocksource_register(cs);
}

static void generic_timer_stop(void)
{
    ttos_time_disable();
}

static void generic_timer_set_mode(enum clockchip_mode mode, struct clockchip *cc)
{
    switch (mode)
    {
    case CLOCKCHIP_MODE_UNUSED:
    case CLOCKCHIP_MODE_SHUTDOWN:
        generic_timer_stop();
        break;
    default:
        break;
    }
}

static int generic_timer_set_next_event(unsigned long long evt, struct clockchip *unused)
{
    ttos_time_count_set(evt);

    return 0;
}

static inline void generic_timer_reg_write(int reg, u32 val)
{
    switch (reg)
    {
    case GENERIC_TIMER_REG_FREQ:
        write_cntfrq(val);
        break;
    case GENERIC_TIMER_REG_HCTL:
        write_cnthctl(val);
        break;
    case GENERIC_TIMER_REG_KCTL:
        write_cntkctl(val);
        break;
    case GENERIC_TIMER_REG_HYP_CTRL:
        write_cnthp_ctl(val);
        break;
    case GENERIC_TIMER_REG_HYP_TVAL:
        write_cnthp_tval(val);
        break;
    case GENERIC_TIMER_REG_PHYS_CTRL:
        write_cntp_ctl(val);
        break;
    case GENERIC_TIMER_REG_PHYS_TVAL:
        write_cntp_tval(val);
        break;
    case GENERIC_TIMER_REG_VIRT_CTRL:
        write_cntv_ctl(val);
        break;
    case GENERIC_TIMER_REG_VIRT_TVAL:
        write_cntv_tval(val);
        break;
    default:
        KLOG_E("Trying to write invalid generic-timer register");
    }

    isb();
}

static inline u32 generic_timer_reg_read(int reg)
{
    u32 val;

    switch (reg)
    {
    case GENERIC_TIMER_REG_FREQ:
        val = read_cntfrq();
        break;
    case GENERIC_TIMER_REG_HCTL:
        val = read_cnthctl();
        break;
    case GENERIC_TIMER_REG_KCTL:
        val = read_cntkctl();
        break;
    case GENERIC_TIMER_REG_HYP_CTRL:
        val = read_cnthp_ctl();
        break;
    case GENERIC_TIMER_REG_HYP_TVAL:
        val = read_cnthp_tval();
        break;
    case GENERIC_TIMER_REG_PHYS_CTRL:
        val = read_cntp_ctl();
        break;
    case GENERIC_TIMER_REG_PHYS_TVAL:
        val = read_cntp_tval();
        break;
    case GENERIC_TIMER_REG_VIRT_CTRL:
        val = read_cntv_ctl();
        break;
    case GENERIC_TIMER_REG_VIRT_TVAL:
        val = read_cntv_tval();
        break;
    default:
        KLOG_E("Trying to read invalid generic-timer register\n");
    }

    return val;
}

static void generic_phys_timer_handler(uint32_t irq, void *dev)
{
    u32 ctl;
    struct clockchip *cc = dev;

    if (cc->bound_on != cpuid_get())
    {
        return;
    }

    ctl = generic_timer_reg_read(GENERIC_TIMER_REG_PHYS_CTRL);
    if (!(ctl & GENERIC_TIMER_CTRL_IT_STAT))
    {
        /* We got interrupt without status bit set.
         * Looks like we are running on buggy hardware.
         */
        DPRINTF("%s: suprious interrupt\n", __func__);
        return;
    }

    ctl |= GENERIC_TIMER_CTRL_IT_MASK;
    generic_timer_reg_write(GENERIC_TIMER_REG_PHYS_CTRL, ctl);

    cc->event_handler(cc);

    return;
}

static int generic_timer_startup(void)
{
    int rc;
    u32 irq;
    struct clockchip *cc;
    struct device_node *np;
    int ret = 0;

    /* Ensure ttos timer is stopped */
    generic_timer_stop();

    /* Create generic ttos timer clockchip */
    cc = zalloc(sizeof(struct clockchip));
    if (!cc)
    {
        return -1;
    }

    np = of_find_compatible_node(NULL, NULL, "arm,armv7-timer");
    if (!np)
    {
        // KLOG_E("no armv7-timer node found");
        np = of_find_compatible_node(NULL, NULL, "arm,armv8-timer");
    }

    if (IS_ENABLED(CONFIG_ARM_32) &&
        of_property_read_bool(np, "arm,cpu-registers-not-fw-configured"))
    {
        cc->name = "GENERIC_SPHYSICAL_TIMER";
        cc->hirq = timer_irq[GENERIC_SPHYSICAL_TIMER];
    }
    else
    {
        cc->name = "GENERIC_PHYSICAL_TIMER";
        cc->hirq = timer_irq[GENERIC_PHYSICAL_TIMER];
    }

    cc->rating = 400;
    // cc->cpumask  = vmm_cpumask_of (cpuid_get ());
    cc->features = CLOCKCHIP_FEAT_ONESHOT;
    cc->freq = generic_timer_hz;
    clocks_calc_mult_shift(&cc->mult, &cc->shift, NSEC_PER_SEC, generic_timer_hz, 600);
    cc->min_delta_ns = clockchip_delta2ns(0xF, cc);
    cc->max_delta_ns = clockchip_delta2ns(CLOCKSOURCE_MASK(56), cc);
    cc->set_mode = &generic_timer_set_mode;
    cc->set_next_event = &generic_timer_set_next_event;
    cc->priv = NULL;

    /* Register ttos timer clockchip */
    rc = clockchip_register(cc);
    if (rc)
    {
        free(cc);
        KLOG_EMERG("fail at %s:%d", __FILE__, __LINE__);
        return -1;
    }

    irq = ttos_pic_irq_alloc(NULL, cc->hirq);
    /* Register irq handler for timer */
    rc = ttos_pic_irq_install(irq, generic_phys_timer_handler, cc, IRQ_SHARED, "PHYSICAL_TIMER");
    if (rc)
    {
        clockchip_unregister(cc);
        ttos_pic_irq_uninstall(irq, "PHYSICAL_TIMER");
        free(cc);
    }

    ttos_pic_irq_priority_set(irq, 0);

    ttos_pic_irq_unmask(irq);

    return 0;
}

int arch_timer_clockchip_init(void)
{
    /* Get and Check generic timer frequency */
    generic_timer_get_freq();
    if (generic_timer_hz == 0)
    {
        return -1;
    }

    /* Get physical timer irq number */
    timer_irq[GENERIC_PHYSICAL_TIMER] = ARM_TIMER_PHYS_NONSECURE;
    timer_irq[GENERIC_SPHYSICAL_TIMER] = ARM_TIMER_PHYS_SECURE;

    generic_timer_startup();
    return 0;
}
