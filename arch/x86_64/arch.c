#include <context.h>
#include <cpu.h>
extern void printk(const char *fmt, ...);
long arch_context_get_args(arch_exception_context_t *context, int index)
{
    printk("%s unimplement!!!\n", __func__);
}

void arch_context_set_return(arch_exception_context_t *context, long value)
{
    context->rax = value;
}

long arch_context_thread_init(arch_exception_context_t *context)
{
    return 0;
}

void arch_switch_context_set_stack(T_TBSP_TaskContext *ctx, long sp)
{
    ctx->sp = sp;
}

void arch_context_set_stack(arch_exception_context_t *context, long value)
{
    context->rsp = value;
}