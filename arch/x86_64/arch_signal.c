/**
 * @file    arch/x86_64/arch_signal.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2025-03-27
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <assert.h>
#include <cache.h>
#include <context.h>
#include <errno.h>
#include <process_signal.h>
#include <sigcontext.h>
#include <signal.h>
#include <syscall.h>
#include <ttos.h>
#include <ttosProcess.h>
#include <uaccess.h>
#include <util.h>

#undef KLOG_TAG
#define KLOG_TAG "arch_signal"
#include <klog.h>

/* x86 ABI requires 16-byte alignment */
#define FRAME_ALIGNMENT 16UL

#define MAX_FRAME_PADDING (FRAME_ALIGNMENT - 1)

struct rt_sigframe
{
    char __user *pretcode;
    struct __ucontext uc;
    struct siginfo info;
};

/*
 * UC_SIGCONTEXT_SS will be set when delivering 64-bit or x32 signals on
 * kernels that save SS in the sigcontext.  All kernels that set
 * UC_SIGCONTEXT_SS will correctly restore at least the low 32 bits of esp
 * regardless of SS (i.e. they implement espfix).
 *
 * Kernels that set UC_SIGCONTEXT_SS will also set UC_STRICT_RESTORE_SS
 * when delivering a signal that came from 64-bit code.
 *
 * Sigreturn restores SS as follows:
 *
 * if (saved SS is valid || UC_STRICT_RESTORE_SS is set ||
 *     saved CS is not 64-bit)
 *         new SS = saved SS  (will fail IRET and signal if invalid)
 * else
 *         new SS = a flat 32-bit data segment
 */
#define UC_SIGCONTEXT_SS 0x2
#define UC_STRICT_RESTORE_SS 0x4
#define UC_FP_XSTATE 0x1

/*
 * Save the fpu, extended register state to the user signal frame.
 *
 * 'buf_fx' is the 64-byte aligned pointer at which the [f|fx|x]save
 *  state is copied.
 *  'buf' points to the 'buf_fx' or to the fsave header followed by 'buf_fx'.
 *
 *	buf == buf_fx for 64-bit frames and 32-bit fsave frame.
 *	buf != buf_fx for 32-bit frames with fxstate.
 *
 * Save it directly to the user frame with disabled page fault handler. If
 * that faults, try to clear the frame which handles the page fault.
 *
 * If this is a 32-bit frame with fxstate, put a fsave header before
 * the aligned state at 'buf_fx'.
 *
 * For [f]xsave state, update the SW reserved fields in the [f]xsave frame
 * indicating the absence/presence of the extended state to the user.
 */
bool copy_fpstate_to_sigframe(void __user *buf, void __user *buf_fx, int size,
                              struct arch_context *regs)
{
    bool ia32_fxstate = (buf != buf_fx);
    int ret;
    void *src = (void *)round_up(((unsigned long)regs->fp_save_area), 16);

    memcpy(buf_fx, src, FP_CONTEXT_SIZE);

    return true;
}

unsigned long fpu__alloc_mathframe(unsigned long sp, unsigned long *buf_fx, unsigned long *size)
{
    unsigned long frame_size = FP_CONTEXT_SIZE;

    *buf_fx = sp = round_down(sp - frame_size, 64);
    *size = frame_size;

    return sp;
}

void __user *get_sigframe(struct ksignal *ksig, struct arch_context *regs, size_t frame_size,
                          void __user **fpstate)
{
    pcb_t pcb = ttosProcessSelf();
    struct sigaction *ka = &ksig->ka;
    /* Default to using normal stack */
    bool nested_altstack = on_sig_stack(regs->rsp);
    bool entering_altstack = false;
    unsigned long math_size = 0;
    unsigned long sp = regs->rsp;
    unsigned long buf_fx = 0;

    /* redzone */
    sp -= 128;

    /* This is the X/Open sanctioned signal stack switching.  */
    if (ka->sa_flags & SA_ONSTACK)
    {
        /*
         * This checks nested_altstack via sas_ss_flags(). Sensible
         * programs use SS_AUTODISARM, which disables that check, and
         * programs that don't use SS_AUTODISARM get compatible.
         */
        if (sas_ss_flags(sp) == 0)
        {
            sp = pcb->sas_ss_sp + pcb->sas_ss_size;
            entering_altstack = true;
        }
    }

    sp = fpu__alloc_mathframe(sp, &buf_fx, &math_size);
    *fpstate = (void __user *)sp;

    sp -= frame_size;
    sp = round_down(sp, FRAME_ALIGNMENT) - 8;
    /*
     * If we are on the alternate signal stack and would overflow it, don't.
     * Return an always-bogus address instead so we will die with SIGSEGV.
     */
    if (unlikely((nested_altstack || entering_altstack) && !__on_sig_stack(sp)))
    {
        printk("fail at %s:%d\n", __FILE__, __LINE__);
        return (void __user *)-1L;
    }

    /* save i387 and extended state */
    if (!copy_fpstate_to_sigframe(*fpstate, (void __user *)buf_fx, math_size, regs))
        return (void __user *)-1L;

    return (void __user *)sp;
}

static __always_inline int setup_sigcontext(struct sigcontext __user *dest_context,
                                            void __user *fpstate, struct arch_context *src_context,
                                            unsigned long mask)
{
    dest_context->rdi = src_context->rdi;
    dest_context->rsi = src_context->rsi;
    dest_context->rbp = src_context->rbp;
    dest_context->rsp = src_context->rsp;
    dest_context->rbx = src_context->rbx;
    dest_context->rdx = src_context->rdx;
    dest_context->rcx = src_context->rcx;
    dest_context->rax = src_context->rax;
    dest_context->r8 = src_context->r8;
    dest_context->r9 = src_context->r9;
    dest_context->r10 = src_context->r10;
    dest_context->r11 = src_context->r11;
    dest_context->r12 = src_context->r12;
    dest_context->r13 = src_context->r13;
    dest_context->r14 = src_context->r14;
    dest_context->r15 = src_context->r15;

    dest_context->trapno = 0;
    dest_context->err = 0;
    dest_context->rip = src_context->rip;
    dest_context->eflags = src_context->rflags;
    dest_context->cs = src_context->cs;
    dest_context->gs = 0;
    dest_context->fs = 0;
    // dest_context->ss = src_context->ss;
    dest_context->fpstate = fpstate;
    dest_context->oldmask = mask;
    dest_context->cr2 = cr2_get();

    return 0;
}

static unsigned long frame_uc_flags(void)
{
    unsigned long flags;

    flags = UC_FP_XSTATE | UC_SIGCONTEXT_SS;
    flags |= UC_STRICT_RESTORE_SS;

    return flags;
}

int x64_setup_rt_frame(struct ksignal *ksig, struct arch_context *src_context)
{
    pcb_t pcb = ttosProcessSelf();
    process_sigset_t *set = sigmask_to_save();
    struct rt_sigframe __user *frame;
    void __user *fp = NULL;
    unsigned long uc_flags;

    // printk("11save src_context->rsp:%p\n", src_context->rsp);

    /* x86-64 should always use SA_RESTORER. */
    if (!(ksig->ka.sa_flags & SA_RESTORER))
        return -EFAULT;

    frame = get_sigframe(ksig, src_context, sizeof(struct rt_sigframe), &fp);
    uc_flags = frame_uc_flags();

    /* Create the ucontext.  */
    frame->uc.uc_flags = uc_flags;
    frame->uc.uc_link = 0;

    frame->uc.uc_stack.ss_sp = (void *)pcb->sas_ss_sp;
    frame->uc.uc_stack.ss_flags = pcb->sas_ss_flags;
    frame->uc.uc_stack.ss_size = pcb->sas_ss_size;

    /* 设置用户态信号函数执行完后，需要执行的指令地址 */
    frame->pretcode = (char __user *)ksig->ka.sa_restorer;

    if (sizeof(struct sigcontext) != sizeof(frame->uc.uc_mcontext))
    {
        assert(0);
    }

    setup_sigcontext((struct sigcontext *)&frame->uc.uc_mcontext, fp, src_context, set->sig[0]);

    memcpy(&frame->uc.uc_sigmask, set, sizeof(sigset_t));

    if (ksig->ka.sa_flags & SA_SIGINFO)
    {
        if (copy_siginfo_to_user(&frame->info, &ksig->info))
            return -EFAULT;
    }

    // if (setup_signal_shadow_stack(ksig))
    // 	return -EFAULT;

    /* 设置信号函数的第一个参数(信号num) */
    src_context->rdi = ksig->sig;

    /* 设置信号函数的第二个参数(siginfo_t结构指针) */
    src_context->rsi = (unsigned long)&frame->info;

    /* 设置信号函数的第三个参数(struct __ucontext结构指针) */
    src_context->rdx = (unsigned long)&frame->uc;

    /* In case the signal handler was declared without prototypes */
    src_context->rax = 0;

    /* 设置返回到用户态后要执行的信号函数 */
    src_context->rip = (unsigned long)ksig->ka.__sa_handler.sa_handler;

    /* 设置返回到用户态后的sp，由于在执行完信号handler后，会执行一条ret指令(函数末尾),会导致sp增加8,所以，需要在此先减
     */
    src_context->rsp = (unsigned long)frame;

    /*
     * Set up the CS and SS registers to run signal handlers in
     * 64-bit mode, even if the handler happens to be interrupting
     * 32-bit or 16-bit code.
     *
     * SS is subtle.  In 64-bit mode, we don't need any particular
     * SS descriptor, but we do need SS to be valid.  It's possible
     * that the old SS is entirely bogus -- this can happen if the
     * signal we're trying to deliver is #GP or #SS caused by a bad
     * SS value.  We also have a compatibility issue here: DOSEMU
     * relies on the contents of the SS register indicating the
     * SS value at the time of the signal, even though that code in
     * DOSEMU predates sigreturn's ability to restore SS.  (DOSEMU
     * avoids relying on sigreturn to restore SS; instead it uses
     * a trampoline.)  So we do our best: if the old SS was valid,
     * we keep it.  Otherwise we replace it.
     */
    src_context->cs = USER_CODE64;

    return 0;
}

static int setup_rt_frame(struct ksignal *ksig, struct arch_context *regs)
{
    return x64_setup_rt_frame(ksig, regs);
}

void signal_setup_done(struct ksignal *ksig, int stepping)
{
    signal_delivered(ksig, stepping);
}

static void handle_signal(struct ksignal *ksig, struct arch_context *context, bool in_syscall)
{
    int ret = 0;
    /* Are we from a system call? */
    if (in_syscall)
    {
        /* If so, check system call restarting.. */
        switch (context->rax)
        {
        case -ERESTART_RESTARTBLOCK:
        case -ERESTARTNOHAND:
            context->rax = -EINTR;
            break;

        case -ERESTARTSYS:
            if (!(ksig->ka.sa_flags & SA_RESTART))
            {
                context->rax = -EINTR;
                break;
            }

        case -ERESTARTNOINTR:
            context->rax = context->ori_rax;
            context->rip -= 2;
            break;
        }
    }

    /*
     * If TF is set due to a debugger (TIF_FORCED_TF), clear TF now
     * so that register information in the sigcontext is correct and
     * then notify the tracer before entering the signal handler.
     */
    // stepping = test_thread_flag(TIF_SINGLESTEP);
    // if (stepping)
    // 	user_disable_single_step(current);

    ret = setup_rt_frame(ksig, context);
    if (!ret)
    {
        /*
         * Clear the direction flag as per the ABI for function entry.
         *
         * Clear RF when entering the signal handler, because
         * it might disable possible debug exception from the
         * signal handler.
         *
         * Clear TF for the case when it wasn't set by debugger to
         * avoid the recursive send_sigtrap() in SIGTRAP handler.
         */
        context->rflags &= ~(X86_EFLAGS_DF | X86_EFLAGS_RF | X86_EFLAGS_TF);
        /*
         * Ensure the signal handler starts with the new fpu state.
         */
        // fpu__clear_user_states(fpu);
    }

    signal_setup_done(ksig, 0);

    void restore_context(void *context);
    asm volatile("swapgs");

    TTOS_TaskEnterUserHook(ttosProcessSelf()->taskControlId);
    restore_context(context);
}

void do_syscall_restart_check(struct arch_context *context, struct ksignal *ksignal) {}

int rt_sigreturn(struct arch_context *context)
{
    unsigned long sp = context->rsp - 8;
    struct rt_sigframe __user *frame = (struct rt_sigframe __user *)sp;
    struct sigcontext __user *uc = (struct sigcontext __user *)&frame->uc.uc_mcontext;
    process_sigset_t set;

    // printk("11 restore rsp:%p\n", frame);

    copy_from_user(&set, &frame->uc.uc_sigmask, sizeof(set));
    set_current_blocked(&set);

    context->rdi = uc->rdi;
    context->rsi = uc->rsi;
    context->rbp = uc->rbp;
    context->rsp = uc->rsp;
    context->rbx = uc->rbx;
    context->rdx = uc->rdx;
    context->rcx = uc->rcx;
    context->rax = uc->rax;
    context->r8 = uc->r8;
    context->r9 = uc->r9;
    context->r10 = uc->r10;
    context->r11 = uc->r11;
    context->r12 = uc->r12;
    context->r13 = uc->r13;
    context->r14 = uc->r14;
    context->r15 = uc->r15;

    context->errorCode = 0;
    context->rip = uc->rip;
    context->rflags = uc->eflags;
    context->cs = USER_CODE64;
    context->ss = USER_DATA64;

    // dest_context->ss = src_context->ss;
    void *fp_save_area = (void *)round_up(((unsigned long)(context->fp_save_area)), 16);

    memcpy(fp_save_area, (void *)uc->fpstate, FP_CONTEXT_SIZE);

    /* Avoid ERESTART handling */
    signal_forget_syscall(context);

    // printk("22 restore rsp:%p\n", context->rsp);

    return 0;
}

void setup_restart_syscall(struct arch_context *regs)
{
    regs->rax = __NR_restart_syscall;
}

static void syscall_set_return_value(pcb_t pcb, struct arch_context *regs, int error, long val)
{
    if (error)
    {
        val = error;
    }

    regs->rax = val;
}

int arch_do_signal(struct arch_context *regs)
{
    bool exist_signal = false;
    unsigned long continue_addr = 0, restart_addr = 0;
    int retval = 0;
    struct ksignal ksig;
    bool is_in_syscall = in_syscall(regs);

    /*
     * If we were from a system call, check for system call restarting...
     */
    if (is_in_syscall)
    {
        continue_addr = regs->rip;
        /*
         * Avoid additional syscall restarting via ret_to_user.
         */
        signal_forget_syscall(regs);
    }

    /*
     * Get the signal to deliver. When running under ptrace, at this point
     * the debugger may change all of our registers.
     */
    exist_signal = get_signal(&ksig);

    if (exist_signal)
    {
        handle_signal(&ksig, regs, is_in_syscall);
        assert(0);
    }

    /* Did we come from a system call? */
    if (is_in_syscall)
    {
        /* Restart the system call - no handlers present */
        switch (regs->rax)
        {
        case -ERESTARTNOHAND:
        case -ERESTARTSYS:
        case -ERESTARTNOINTR:
            regs->rax = regs->ori_rax;
            regs->rip -= 2;
            break;

        case -ERESTART_RESTARTBLOCK:
            regs->rax = __NR_restart_syscall;
            regs->rip -= 2;
            break;
        }
    }

    restore_saved_sigmask();

    return 0;
}