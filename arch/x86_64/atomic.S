#ifdef __x86_64__

#define ASM_USE

.text

/* BOOL atomic32_cas(T_UWORD * target,T_UWORD oldValue,T_UWORD newValue) */
.align 8
.global atomic32_cas
atomic32_cas:

    /* edi-target esi-oldValue edx-newValue */
    movl    %esi,%eax /* get old_value to cmp */
    movl    %edx,%ecx /* get new_value to set */
    lock                       /* lock the Bus during the next inst */
    cmpxchg %ecx,(%rdi)        /* if (eax == (edx) */
                               /*   {ZF = 1; (edx) = ecx;} */
                               /* else              */
                               /*   {ZF = 0; eax = (edx);} */
    jne     cas1
    movl    $1, %eax           /* set status to TRUE */
    retq

cas1:
    movl    $0, %eax           /* set status to FALSE */
    retq
	
/* T_UWORD atomic32_add(T_UWORD * target,T_UWORD value) */
.align 8
.global atomic32_add
atomic32_add:

	mov  %rdi,%rdx
	movl  %esi,%eax

	lock			                       /* lock the Bus during the next inst */
	xadd  %eax,(%rdx)                /* TMP = eax + (edx) */
				                       /* eax = (edx) */
				                       /* (edx) = TMP */
	retq

/* T_UWORD atomic32_sub(T_UWORD * target,T_UWORD value) */
.align 8
.global atomic32_sub
atomic32_sub:
	mov  %rdi,%rdx
	movl  %esi,%eax

	negl  %eax		  /* Negate eax... */

	lock		          /* lock the Bus during the next inst */
	xadd  %eax,(%rdx)         /* TMP = eax + (edx) */
				  /* eax = (edx) */
				  /* (edx) = TMP */
	retq

/* T_UWORD atomic32_inc(T_UWORD * target) */
.align 8
.global atomic32_inc
atomic32_inc:

	mov  %rdi,%rdx
	movl  $0x1,%eax           /* inc by 1 */

	lock                      /* lock the Bus during the next inst */
	xadd  %eax,(%rdx)         /* TMP = eax + (edx) */
				  /* eax = (edx) */
				  /* (edx) = TMP */
	retq
	

/* T_UWORD atomic32_inc_return(T_UWORD * target) */
.align 8
.global atomic32_inc_return
atomic32_inc_return:

	movl $1, %eax
	lock xaddl %eax, (%rdi)  #lock锁总线,xaddl指令的行为：先交换两个加数的值，再相加
	addl $1, %eax            #由于上一条指令,原始的值已经存在eax中,此处加1，即可获取相加后的值(注意:不能直接使用(%rdi)来读相加后的值，多核情况下,读到的值可能并不是期望的值+1后的值)
	retq

/* T_UWORD atomic32_dec(T_UWORD * target) */
.align 8
.global atomic32_dec
.global atomic32_dec_return
atomic32_dec:
atomic32_dec_return:
	mov  %rdi,%rdx
	movl  $0x1,%eax           /* dec by 1 */
	negl  %eax		  /* Negate eax... */

	lock                      /* lock the Bus during the next inst */
	xadd  %eax,(%rdx)         /* TMP = eax + (edx) */
				  /* eax = (edx) */
				  /* (edx) = TMP */
	retq

/* T_UWORD atomic32Get(T_UWORD * target) */
.align 8
.global atomic32Get
atomic32Get:
	mov (%rdi), %eax
	retq

/* T_UWORD atomic32_set(T_UWORD * target,T_UWORD value) */
.align 8
.global atomic32_set
atomic32_set:
	mov  %rdi,%rdx
	movl  %esi,%eax

	/* lock */		  /* xchg always locks, lock not needed */
	xchg  %eax,(%rdx)         /* set value with bus-lock */

	retq

/* T_UWORD atomic32Clear(T_UWORD * target) */
.align 8
.global atomic32Clear
atomic32Clear:
	mov  %rdi,%rdx
	movl  $0x0,%eax           /* 'clear' value to set */
	/* lock */                /* xchg always locks, lock not needed */
	xchg  %eax,(%rdx)         /* swap 'clear' value with bus-lock */

	retq

/* T_UWORD atomic32Or(T_UWORD * target,T_UWORD value) */
.align 8
.global atomic32Or
atomic32Or:

	mov  %rdi,%rdx
	movl  (%rdx),%eax         /* get old value */

atomicOr_retry:

	movl  %esi,%ecx  /* get value to set */
	orl   %eax, %ecx
	lock			  /* lock the Bus during the next inst */
	cmpxchg  %ecx,(%rdx)      /* if (eax == (edx)) */
				  /*   {ZF = 1 ;  (edx) = ecx;} */
				  /* else                */
				  /*   {ZF = 0 ;  eax = (edx);} */
	jnz atomicOr_retry
	retq

/* T_UWORD atomic32Xor(T_UWORD * target,T_UWORD value) */
.align 8
.global atomic32Xor
atomic32Xor:
	mov  %rdi,%rdx
	movl  (%rdx),%eax         /* get old value */

atomicXor_retry:

	movl  %esi,%ecx  /* get value to set */
	xorl  %eax, %ecx
	lock			  /* lock the Bus during the next inst */
	cmpxchg  %ecx,(%rdx)      /* if (eax == (edx)) */
				  /*   {ZF = 1 ;  (edx) = ecx;} */
				  /* else                */
				  /*   {ZF = 0 ;  eax = (edx);} */
	jnz atomicXor_retry
	retq

/* T_UWORD atomic32And(T_UWORD * target,T_UWORD value) */
.align 8
.global atomic32And
atomic32And:

	mov  %rdi,%rdx
	movl  (%rdx),%eax         /* get old value */

atomicAnd_retry:

	movl  %esi,%ecx  /* get value to set */
	andl  %eax, %ecx
	lock			  /* lock the Bus during the next inst */
	cmpxchg  %ecx,(%rdx)      /* if (eax == (edx)) */
				  /*   {ZF = 1 ;  (edx) = ecx;} */
				  /* else                */
				  /*   {ZF = 0 ;  eax = (edx);} */
	jnz atomicAnd_retry
	retq

/* T_UWORD atomic32Nand(T_UWORD * target,T_UWORD value) */
.align 8
.global atomic32Nand
atomic32Nand:
	mov  %rdi,%rdx
	movl  (%rdx),%eax         /* get old value */

atomicNand_retry:

	movl  %esi,%ecx  /* get value to nand with old value */

	andl  %eax, %ecx
    not   %ecx

	lock                      /* lock the Bus during the next inst */
	cmpxchg  %ecx,(%rdx)      /* if (eax == (edx)) */
				  /*   {ZF = 1 ;  (edx) = ecx;} */
				  /* else                */
				  /*   {ZF = 0 ;  eax = (edx);} */
	jnz atomicNand_retry
	retq

#if 0	
/* T_VOID atomic64Write(T_UDWORD * target,T_UDWORD value) */
.balign 16,0x90
.global atomic64Write
atomic64Write:
/* edi--target esi--value */
   pushl %ebx
   pushl	%edi
   movl  SP_ARG4(%esp),%edi
   movl  0(%edi), %ebx
   movl  4(%edi), %ecx
   movl  SP_ARG3(%esp),%edi

	/* current 64 bit value in location */
   movl  0(%edi), %eax
   movl  4(%edi), %edx

atomicQuadWrite_retry:

   lock
   cmpxchg8b   (%edi) /* If (edi) == edx:eax write ecx:ebx value to (edi) */
	jnz         atomicQuadWrite_retry /* keep trying until success */
   popl        %edi
   popl        %ebx
   retq	
 
/* T_VOID atomic64Read(T_UDWORD * target,T_UDWORD * value) */
.balign 16,0x90
.global atomic64Read
atomic64Read:
   pushl %ebx
   pushl %edi
   movl  SP_ARG3(%esp), %edi

   movl  %eax, %ebx 
   movl  %edx, %ecx
	/*
    * If the value in edx:eax happens to equal what is in the location
    * then the same value is written back (ie: ecx:ebx).
    *
    * If the value in edx:eax is different then we read the location and
    * store that value in edx:eax
    *
    * Either way edx:eax gets returned.
    *
    * WARNING: There is a side affect of WRITING during a READ operation. WARNING
    */
   lock
   cmpxchg8b   (%edi)
	
   movl  SP_ARG4(%esp), %edi
   movl  eax, 0(%edi)
   movl  edx, 4(%edi)

   popl  %edi
   popl  %ebx

   retq
   #endif
#endif /*__x86_64__*/
