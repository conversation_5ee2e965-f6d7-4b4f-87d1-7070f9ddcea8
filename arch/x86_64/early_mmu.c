#include <cpu.h>
#include <mmu.h>
#include <msr.h>
#include <ttosMM.h>

#define MMU_SPACE_PAGE_NUM 0x800
#define MMU_SPACE_SIZE (MMU_SPACE_PAGE_NUM * 4096)

__attribute__((section(".data"), aligned(PAGE_SIZE))) char mmu_space[MMU_SPACE_SIZE] = {0};

extern long long _start;
extern int __image_start__;
extern int __end__;
extern long long kernel_pgtable;
extern void kernel_mmu_set_pvoffset(uint32_t pa_l, uint32_t pa_h, virt_addr_t va);
extern uint64_t __attribute__((aligned(PAGE_SIZE))) g_fix_map_level1[512];
extern uint64_t __attribute__((aligned(PAGE_SIZE))) g_fix_map_level2[512];
extern uint64_t __attribute__((aligned(PAGE_SIZE))) g_fix_map_level3[512];

static void page_clean(uintptr_t *page)
{
    int i = 0;
    for (i = 0; i < PAGE_SIZE / sizeof(uintptr_t); i++)
    {
        page[i] = 0;
    }
}

static uintptr_t page_index = 0;

void reset_free_page(void)
{
    page_index = 0;
}

uintptr_t get_free_page(uintptr_t pv_offset)
{
    uintptr_t page;
    unsigned long long mmu_space_pa = 0ULL;

    mmu_space_pa = ((unsigned long long)mmu_space);
    // va_offset = va_offset - KERNEL_SPACE_START;

    page = mmu_space_pa + page_index * PAGE_SIZE;

    page_index++;

    /* 最多MMU_SPACE_PAGE_NUM个页 */
    if (page_index > MMU_SPACE_PAGE_NUM)
    {
        while (1)
            ;
    }

    return page;
}

static int early_map_single_entry_r(uint64_t *mmu_base, int level, uintptr_t va, uint64_t pa,
                                    uint64_t attr, uintptr_t pv_offset)
{
    int level_final = level;
    uint64_t *cur_lv_tbl = mmu_base;
    uintptr_t page;
    uint64_t off;
    level = 0;

    if (!IS_LEVEL_ALIGN(level_final, va))
    {
        return -1;
    }

    if (!IS_LEVEL_ALIGN(level_final, pa))
    {
        return -1;
    }

    for (; level < level_final; level++)
    {
        off = GET_TABLE_OFF((unsigned long long)level, va);
        if (!(cur_lv_tbl[off] & PAGE_P))
        {
            page = get_free_page(pv_offset);
            if (!page)
            {
                return -1;
            }

            cur_lv_tbl[off] = (MEM_NORMAL_ATTRS | page);
        }

        page = cur_lv_tbl[off];

        cur_lv_tbl = (uint64_t *)(uintptr_t)(page & ENTRY_ADDRESS_MASK);
    }

    off = GET_TABLE_OFF(level, va);

    cur_lv_tbl[off] = (attr | pa);

    return 0;
}

int early_mmu_map_r(uint64_t *mmu_base, uintptr_t va, uint64_t pa, size_t size, uint64_t attr,
                    uintptr_t pv_offset)
{
    size_t i;
    size_t count;
    int level;
    int ret;

    level = GET_MAP_LEVEL(va, pa, size);

    count = size >> LEVEL_SIZE_SHIFT(level);

    if (!IS_LEVEL_ALIGN(level, pa))
    {
        return -1;
    }

    if (!IS_LEVEL_ALIGN(level, va))
    {
        return -1;
    }

    for (i = 0; i < count; i++)
    {
        ret = early_map_single_entry_r(mmu_base, level, va, pa, attr, pv_offset);
        va += LEVEL_SIZE(level);
        pa += LEVEL_SIZE(level);
        if (ret != 0)
        {
            return ret;
        }
    }
    return 0;
}

void early_mmu_init(unsigned int kernel_pa)
{
    int ret;
    uint64_t mmu_base;
    uintptr_t kernel_space_va = (uintptr_t)KERNEL_SPACE_START;
    uint64_t kernel_space_pa = (uint64_t)kernel_pa;
    uint64_t mapsize = (uint64_t)&__end__ - (uint64_t)&__image_start__;
    int64_t pv_offset = (long long)kernel_pa - (long long)&_start;

    pat_init();

    kernel_mmu_set_pvoffset(kernel_pa, 0, (virt_addr_t)(&_start));

    mmu_base = cr3_get() & CR3_ADDR_MASK;

    ret = early_mmu_map_r((uint64_t *)mmu_base, kernel_space_va, kernel_space_pa, mapsize,
                          MEM_NORMAL_KRWX_ATTRS, pv_offset);

    if (ret != 0)
    {
        while (1)
            ;
    }

    ((uint64_t *)mmu_base)[GET_TABLE_OFF(0, FIX_MAP_START)] =
        TABLE_DESC((g_fix_map_level1 + pv_offset));
    g_fix_map_level1[GET_TABLE_OFF(1, FIX_MAP_START)] = TABLE_DESC(g_fix_map_level2 + pv_offset);
    g_fix_map_level2[GET_TABLE_OFF(2, FIX_MAP_START)] = TABLE_DESC(g_fix_map_level3 + pv_offset);
    g_fix_map_level3[FIX_MAP_SELF] = PAGE_DESC(MEM_NORMAL_KRW_ATTRS, g_fix_map_level3 + pv_offset);

    reset_free_page();
}

void ap_mmu_setup_early(uintptr_t pv_off) {}
