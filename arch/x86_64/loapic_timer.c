/**
 * @file    loapic_timer.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2025-03-17
 *
 * aa65c061 2024-03-14 添加loapic timer驱动
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

/************************头 文 件******************************/
#include "loapic_timer.h"
#include <arch_timer.h>
#include <barrier.h>
#include <clock/clockchip.h>
#include <clock/clocksource.h>
#include <commonUtils.h>
#include <cpuid.h>
#include <ttos_pic.h>
#include <driver/apic.h>
#include <inttypes.h>
#include <kmalloc.h>
#include <msr.h>
#include <time/ktime.h>
#include <ttos_time.h>
#include <vector.h>
#include <driver/clocksource/hpet.h>
#include <driver/clocksource/tsc.h>

#define KLOG_TAG "loapic Timer"
#include <klog.h>

/************************宏 定 义******************************/
#define APIC_LVT_MASKED (1 << 16)
#define APIC_LVT_TIMER_TSCDEADLINE (2 << 17)
#define APIC_LVT_TIMER_PERIODIC (1 << 17)
#define MSR_IA32_TSC_DEADLINE 0x000006E0

#define CONFIG_CPU_FREQ 2000000000ULL
#define CONFIG_US_PER_TICK 1000 /* 每个tick1000us */
/************************类型定义******************************/
/************************外部声明******************************/
/************************前向声明******************************/
static s32 loapic_timer_init(void);
static s32 loapic_timer_enable(void);
static s32 loapic_timer_disable(void);
static u64 loapic_timer_count_get(void);
static s32 loapic_timer_count_set(u64 count);
static u64 loapic_timer_freq_get(void);
static u64 loapic_timer_walltime_get(void);
static s32 loapic_timer_timeout_set(u64 timeout);

/************************模块变量******************************/
static ttos_time_ops_t loapic_timer_ops = {.time_name = "loapic timer",
                                           .time_init = loapic_timer_init,
                                           .time_enable = loapic_timer_enable,
                                           .time_disable = loapic_timer_disable,
                                           .time_count_get = loapic_timer_count_get,
                                           .time_count_set = loapic_timer_count_set,
                                           .time_freq_get = loapic_timer_freq_get,
                                           .time_walltime_get = loapic_timer_walltime_get,
                                           .time_timeout_set = loapic_timer_timeout_set};

static bool tsc_deadline_mode;
struct loapic_timer_reg_ops *loapic_timer_reg_ops;

static u32 loapic_timer_hz = 0;
static u32 loapic_timer_mult = 0;
static u32 loapic_timer_shift = 0;

/************************全局变量******************************/
/************************实   现*******************************/

/************************外部实现******************************/
uint64_t rdtsc(void);
/************************实现******************************/

static void timer_check(void)
{
    /*
     *  If CPUID.06H:EAX.ARAT[bit 2] = 1, the processor’s APIC timer runs at a constant rate
     * regardless of P-state transi- tions and it continues to run at the same rate in deep C-states
     */

    bool result;
    uint32_t eax = 6;
    uint32_t ebx = 0;
    uint32_t ecx = 0;
    uint32_t edx = 0;

    cpuid(&eax, &ebx, &ecx, &edx);

    /* 检测 APIC-Timer-always-running feature是否支持 */
    if (!(eax & (1 << 2)))
    {
        printk("APIC timer may not running at a constant rate!!!");
    }
}

static bool tsc_deadline_timer_support(void)
{
    bool result;
    uint32_t eax = 1;
    uint32_t ebx = 0;
    uint32_t ecx = 0;
    uint32_t edx = 0;

    cpuid(&eax, &ebx, &ecx, &edx);

    result = (ecx & (1 << 24)) ? true : false;

    return result;
}

/**
 * @brief
 *    time 中断初始化
 * @param[in] 无
 * @retval 无
 */
static void loapic_timer_irq_init(void)
{
    /* 使能中断 */
    ttos_pic_irq_unmask(LOCAL_APIC_TIMER_VECTOR);
}

static uint64_t lapic_freq_from_hpet()
{
    int seconds = 3;
    uint64_t freq = 0;
    uint64_t start_tick = 0xffffffff, end_tick, diff_tick;
    size_t flags;

    hpet_init();

    ttos_int_lock(flags);

    for (size_t i = 0; i < seconds; i++)
    {
        loapic_timer_reg_ops->init_count_write(start_tick);
        hpet_time_udelay(USEC_PER_SEC);
        end_tick = loapic_timer_reg_ops->cur_count_read();
        freq += (start_tick - end_tick);
    }

    ttos_int_unlock(flags);

    freq /= seconds;

    return (modifyFreq(freq));
}

static void loapic_timer_freq_init(void)
{
    if (tsc_deadline_mode)
    {
        loapic_timer_hz = tsc_freq_get();
    }
    else
    {
        loapic_timer_hz = lapic_freq_from_hpet();
    }
}

/**
 * @brief
 *    time 初始化
 * @param[in] 无
 * @retval EIO 失败
 * @retval 0 成功
 */
static s32 loapic_timer_init(void)
{
    u32 value = 0;

    /* 检测 APIC-Timer-always-running feature是否支持(是否会受到C-STATE P-STATE影响) */
    timer_check();

    if (loapic_x2apic_enable())
    {
        loapic_timer_reg_ops = loapic_x2apic_timer_get();
    }
    else
    {
        loapic_timer_reg_ops = loapic_xapic_timer_get();
    }

    /* 设置devide config register,设置分频系数为1 */
    loapic_timer_reg_ops->dcr_write(0xb);

    if (tsc_deadline_timer_support())
    {
        printk("LOCAL-TIMER TSC-DEADLINE MODE\n");
        /* 在初始化时使能localApic Timer中断，并且设置为TSCDEADLINE模式。*/
        value = APIC_LVT_TIMER_TSCDEADLINE | LOCAL_APIC_TIMER_VECTOR;
        tsc_deadline_mode = true;
    }
    else
    {
        printk("LOCAL-TIMER ONE-SHOT MODE\n");
        /* 在初始化时禁止localApic Timer中断，并且设置为one-shot模式。*/
        value = LOCAL_APIC_TIMER_VECTOR;
    }

    /* 计算apic timer频率*/
    loapic_timer_freq_init();

    loapic_timer_reg_ops->timer_cfg_write(value);

    /*time 中断初始化*/
    loapic_timer_irq_init();

    if (is_bootcpu())
    {
        KLOG_I("loapic timer freq:%" PRIu64, loapic_timer_freq_get());
    }

    return (0);
}

/**
 * @brief
 *    使能timer
 * @param[in] 无
 * @retval 0 设置成功
 */
static s32 loapic_timer_enable(void)
{
#if 0
    /*在tsc-deadline模式下，设置了MSR_IA32_TSC_DEADLINE寄存器为非0值就表示使能
    在非tsc-deadline模式下，设置了INIT_COUNT寄存器就表示使能
    以上两种情况，在初始化过程中就已经设置，所以，无需单独设置*/

    /*使能timer*/
    if (tsc_deadline_mode)
    {
        msr_write(MSR_IA32_TSC_DEADLINE, 1);
    }
    else
    {
        printk("");
        //loapic_timer_reg_ops->init_count_write(1);
    }
#endif

    // u32 value = loapic_timer_reg_ops->timer_cfg_read();
    // value &= ~APIC_LVT_MASKED;
    // loapic_timer_reg_ops->timer_cfg_write(value);

    return (0);
}

/**
 * @brief
 *    关闭timer
 * @param[in] 无
 * @retval 0 设置成功
 */
static s32 loapic_timer_disable(void)
{
    if (tsc_deadline_timer_support())
    {
        msr_write(MSR_IA32_TSC_DEADLINE, 0);
    }
    else
    {
        if (!loapic_timer_reg_ops)
        {
            loapic_timer_reg_ops = loapic_xapic_timer_get();
        }

        loapic_timer_reg_ops->init_count_write(0);
    }

    return (0);
}

/**
 * @brief
 *    获取timer count
 * @param[in] 无
 * @retval  time count
 */
static u64 loapic_timer_count_get(void)
{
#if 0
    if (tsc_deadline_mode)
    {
        return rdtsc ();
    }
    else
    {
        GENERIC_SPHYSICAL_TIMER
        printk("while(1) at %s:%d\n", __FILE__, __LINE__);
        while(1);
    }
#endif

    return rdtsc();
}

/**
 * @brief
 *    设置timer count
 * @param[in] count time count
 * @retval 0 设置成功
 */
static s32 loapic_timer_count_set(u64 count)
{
    u64 now = 0;

    if (tsc_deadline_timer_support())
    {
        msr_write(MSR_IA32_TSC_DEADLINE, count + rdtsc());
    }
    else
    {
        if (count > UINT32_MAX)
        {
            printk("error:count > UINT32_MAX!!!\n");
        }
        loapic_timer_reg_ops->init_count_write(count);
    }

    /* 使能timer */
    loapic_timer_enable();

    return (0);
}

/**
 * @brief
 *    获取timer freq
 * @param[in] 无
 * @retval  time freq
 */
static u64 loapic_timer_freq_get(void)
{
    return loapic_timer_hz;
}

/**
 * @brief
 *    获取timer墙上时间
 * @param[in] 无
 * @retval 墙上时间，nanoseconds
 */
static u64 loapic_timer_walltime_get(void)
{
    u64 freq, count;
    u64 seconds, nanoseconds, walltime;

    /*获取频率*/
    freq = loapic_timer_freq_get();

    /*获取timer count*/
    count = loapic_timer_count_get();

    /*计算walltime*/
    seconds = count / freq;
    nanoseconds = ((count % freq) * NSEC_PER_SEC) / freq;
    walltime = seconds * NSEC_PER_SEC + nanoseconds;

    return walltime;
}

/**
 * @brief
 *    设置timer超时时刻
 * @param[in] timeout 超时时刻，nanoseconds
 * @retval EIO 失败
 * @retval 0 成功
 */
static s32 loapic_timer_timeout_set(u64 timeout)
{
    u64 freq, count;
    u64 seconds;

    /*获取频率*/
    freq = loapic_timer_freq_get();

    /*计算count*/
    seconds = timeout / NSEC_PER_SEC;
    count = ((timeout % NSEC_PER_SEC) * freq) / NSEC_PER_SEC;
    count = seconds * freq + count;

    return loapic_timer_count_set(count);
}

static void loapic_timer_handler(uint32_t irq, void *dev)
{
    u32 ctl;
    struct clockchip *cc = dev;

    if (cc->bound_on != cpuid_get())
    {
        return;
    }

    cc->event_handler(cc);

    return;
}

static int loapic_timer_set_next_event(unsigned long long evt, struct clockchip *unused)
{
    loapic_timer_count_set(evt);

    return 0;
}

static void loapic_timer_stop(void)
{
    loapic_timer_disable();
}

static void loapic_timer_set_mode(enum clockchip_mode mode, struct clockchip *cc)
{
    switch (mode)
    {
    case CLOCKCHIP_MODE_UNUSED:
    case CLOCKCHIP_MODE_SHUTDOWN:
        loapic_timer_stop();
        break;
    default:
        break;
    }
}

static int loapic_timer_startup(void)
{
    int rc;
    u32 irq;
    struct clockchip *cc;

    /* Ensure ttos timer is stopped */
    loapic_timer_stop();

    /* Create generic ttos timer clockchip */
    cc = zalloc(sizeof(struct clockchip));
    if (!cc)
    {
        return -1;
    }
    cc->name = "LOCAO_APIC_TIMER";
    cc->hirq = LOCAL_APIC_TIMER_VECTOR;
    cc->rating = 400;
    cc->features = CLOCKCHIP_FEAT_ONESHOT;
    cc->freq = loapic_timer_hz;
    if (tsc_deadline_mode)
    {
        clocks_calc_mult_shift(&cc->mult, &cc->shift, NSEC_PER_SEC, loapic_timer_hz, 600);
        cc->min_delta_ns = clockchip_delta2ns(0xF, cc);
        cc->max_delta_ns = clockchip_delta2ns(~0UL, cc);
    }
    else
    {
        clocks_calc_mult_shift(&cc->mult, &cc->shift, NSEC_PER_SEC, loapic_timer_hz, 5);
        cc->min_delta_ns = clockchip_delta2ns(0xF, cc);
        cc->max_delta_ns = clockchip_delta2ns(0x7FFFFFFF, cc);
    }

    cc->set_mode = &loapic_timer_set_mode;
    cc->set_next_event = &loapic_timer_set_next_event;
    cc->priv = NULL;

    /* Register ttos timer clockchip */
    rc = clockchip_register(cc);
    if (rc)
    {
        free(cc);
        KLOG_EMERG("fail at %s:%d", __FILE__, __LINE__);
        return -1;
    }

    irq = ttos_pic_irq_alloc(NULL, LOCAL_APIC_TIMER_VECTOR);
    /* Register irq handler for timer */
    rc = ttos_pic_irq_install(irq, loapic_timer_handler, cc, IRQ_SHARED, "LOAPIC_TIMER");
    if (rc)
    {
        clockchip_unregister(cc);
        ttos_pic_irq_uninstall(irq, "LOAPIC_TIMER");
        free(cc);
    }

    ttos_pic_irq_priority_set(irq, 0);

    ttos_pic_irq_unmask(irq);

    return 0;
}

int arch_timer_clockchip_init(void)
{
    if (loapic_timer_hz == 0)
    {
        return -1;
    }

    loapic_timer_startup();
    return 0;
}

/**
 * @brief
 *    loapic timer初始化，注册ttos_timer操作函数
 * @param[in] 无
 * @retval 0 初始化成功
 */
s32 arch_timer_pre_init(void)
{
    if (is_bootcpu())
    {
        /*注册loapic timer操作函数*/
        ttos_time_register(&loapic_timer_ops);
    }

    return (0);
}

void timer_debug(void)
{
    // static int exe_once = 0;
    // uint64_t rdtsc (void);
    // volatile unsigned long long delay = 0xffffffff;
    // unsigned long long init_count = loapic_timer_reg_ops->init_count_read();
    // unsigned long long cur_count = loapic_timer_reg_ops->cur_count_read();

    // unsigned long long tsc_deadline = msr_read(MSR_IA32_TSC_DEADLINE);
    // unsigned long long tsc = rdtsc();

    // // if (!exe_once)
    // // {
    // //     exe_once = 1;
    // //     msr_write(MSR_IA32_TSC_DEADLINE, tsc+0x10000000);
    // // }

    // asm ("sti");

    // #define IA32_X2APIC_IRR0 0x820
    // #define IA32_X2APIC_IRR1 0x821
    // #define IA32_X2APIC_IRR2 0x822
    // #define IA32_X2APIC_IRR3 0x823
    // #define IA32_X2APIC_IRR4 0x824
    // #define IA32_X2APIC_IRR5 0x825
    // #define IA32_X2APIC_IRR6 0x826
    // #define IA32_X2APIC_IRR7 0x827
    // #define IA32_X2APIC_LVT_TIMER   0x832

    // // unsigned long  irr0 =  msr_read(IA32_X2APIC_IRR0);
    // // unsigned long irr1 =  msr_read(IA32_X2APIC_IRR1);
    // // unsigned long irr2 =  msr_read(IA32_X2APIC_IRR2);
    // // unsigned long irr3 =  msr_read(IA32_X2APIC_IRR3);
    // // unsigned long irr4 =  msr_read(IA32_X2APIC_IRR4);
    // // unsigned long irr5 =  msr_read(IA32_X2APIC_IRR5);
    // // unsigned long irr6 =  msr_read(IA32_X2APIC_IRR6);
    // // unsigned long irr7 =  msr_read(IA32_X2APIC_IRR7);
    // // printk("irr0:0x%llx\n", irr0);
    // // printk("irr0:0x%llx\n", irr1);
    // // printk("irr0:0x%llx\n", irr2);
    // // printk("irr0:0x%llx\n", irr3);
    // // printk("irr0:0x%llx\n", irr4);
    // // printk("irr0:0x%llx\n", irr5);
    // // printk("irr0:0x%llx\n", irr6);
    // // printk("irr0:0x%llx\n", irr7);

    // unsigned long val = msr_read(IA32_X2APIC_LVT_TIMER);

    // printk("IA32_X2APIC_LVT_TIMER:0x%llx\n", val);
    // printk("tsc_deadline = %llu, tsc = %llu\n", tsc_deadline, tsc);

    // while(delay--);
}