#define ASM_USE

#include <multiboot.h>
#include <mmu.h>
#include <asm.h>
#include <cpu.h>

.macro gdt_desc dlim_0_15, dbas_0_15, dbas_16_23, daccess, dgran, dbas_24_31
 .word \dlim_0_15
 .word \dbas_0_15
 .byte \dbas_16_23
 .byte \daccess
 .byte \dgran
 .byte \dbas_24_31
.endm

.macro idt_desc dIDT_off_0_15, dIDT_selector, dIDT_access, dIDT_off_16_31
.word \dIDT_off_0_15
.word \dIDT_selector
.word \dIDT_access
.word \dIDT_off_16_31
.endm

.globl	GTEXT(sys_gdtr)
.globl	GTEXT(sys_gdt)
.set COM_BASE,0x3f8
.text  
.code32
.balign 16

.section ".__start", "ax"
.global _start
FUNC_LABEL(_start)

	cli	/* 禁用中断 */

	call    1f
1:	popl    %ebp
	subl    $(rva(1b)), %ebp                              /* 获取到_start的物理地址，存到ebp */

        leal    rva(init_stack+TMP_STACK_SIZE)(%ebp), %esp   /* 获取栈物理地址,设置栈 */
        and     $~0xf,%esp

.code32
.balign 16
         
#ifdef GRUB_MULTIBOOT
        jmp     multiboot_entry

        /* Multiboot Header ---allows GRUB to load kernel */
.align 8

        /* MULTIBOOT ---ELF images */
multiboot_header:
        .long   MULTIBOOT_HEADER_MAGIC
        .long   MULTIBOOT_HEADER_FLAGS
        .long   -(MULTIBOOT_HEADER_MAGIC + MULTIBOOT_HEADER_FLAGS)
        .long   multiboot_header + SYS_BASE_ADRS - _start
        .long   SYS_BASE_ADRS
        .long   _edata + SYS_BASE_ADRS - _start
        .long   __bss_end__ + SYS_BASE_ADRS - _start
        .long   SYS_BASE_ADRS

multiboot_entry:
        cmp     $MULTIBOOT_BOOTLOADER_MAGIC, %eax
        jne     multiPrologEnd

        #movl    %eax,(MULTIBOOT_SCRATCH)     /* save magic */
        leal     rva(muti_boot_scratch)(%ebp), %edi
        mov     %eax, (%edi)

        #movl    %ebx,(MULTIBOOT_SCRATCH_PTR)           /* save multi-boot pointer */
        leal     rva(muti_boot_ptr)(%ebp), %edi
        mov     %ebx, (%edi)

mbh_mmap_not_valid:

        #popl    %eax                        /* restore MULTIBOOT_BOOTLOADER_MAGIC */

        movl    $BOOT_CLEAR,%ebx

multiPrologEnd:
        movl    $BOOT_CLEAR,%ebx
#endif /* GRUB_MULTIBOOT */

        /*禁止cache前需要刷新无效cache里的数据。*/
        wbinvd

clear_bss:
        leal    rva(__bss_start__)(%ebp), %edi
        leal    rva(__bss_end__)(%ebp), %ecx
        subl    %edi, %ecx
        shrl    $2, %ecx             # 除以4       
        xorl    %eax, %eax           # 清零eax
        cld                       # 清除方向标志,确保向前移动
        rep     stosl                 # 重复存储4字节
	
        xorl    %eax, %eax              /* zero EAX */
        movl    %cr0, %edx              /* get CR0 */
        andl    $0x7ffafff1, %edx       /* clear PG, AM, WP, TS, EM, MP */
        movl    %edx, %cr0              /* set CR0 */

        pushl   %eax                    /* initialize EFLAGS */
        popfl

        xorl    %eax, %eax              /* zero EAX */
        movl    %eax, %cr4              /* initialize CR4 */
                
        /* Initialize the %ds segment register */

        /* load new GDT with the 64bit segments using 32bit descriptor */
        leal    rva(sys_gdt)(%ebp), %eax         /* 获取sysGdt相对_start 的偏移，保存至eax */
        leal    (rva(sys_gdtr)+2)(%ebp), %ebx    /* 获取sysGdtr 所在的物理地址 */
        movl    %eax, (%ebx)                    /* 将sysGdt的物理地址存入(sys_gdtr + 2) */
        leal    rva(sys_gdtr)(%ebp), %edi
        lgdt    (%edi)

        movw    $KERNEL_DATA32, %ax	/* set data segment 0x10 is 3rd one */
        movw    %ax, %ds
	movw	%ax, %ss
	movw	%ax, %es
	movw	%ax, %fs
	movw	%ax, %gs
       
        /*
        * Prepare for entering 64bits mode
        */	 
        xorl    %eax, %eax
        orl     $CR4_PAE, %eax          /* PAE */
        movl    %eax, %cr4

        /* Initialize static MMU table */
        mov $0, %ebx

        /* Initialize Page tables to 0 */
        leal	rva(kernel_pgtable)(%ebp), %edi
        xorl	%eax, %eax
        movl	$(INIT_PAGE_SPACE_SIZE/4), %ecx
        rep	stosl /*将eax中的值存储到edi指向的内存中，并将edi根据数据大小(此处是4字节)递增，rep前缀表示重复执行stosl ecx次数*/

        movl    $0, %edx

        /* Build Level 4 */
        leal	rva(kernel_pgtable + 0)(%ebp), %edi
        leal	0x1007 (%edi), %eax
        movl	%eax, 0(%edi)
        addl	%edx, 4(%edi)

        /* Build Level 3 */
        leal	rva(kernel_pgtable + 0x1000)(%ebp), %edi
        leal	0x1007(%edi), %eax
        movl	$4, %ecx
    1:	movl	%eax, 0x00(%edi)
        addl	%edx, 0x04(%edi)
        addl	$0x00001000, %eax
        addl	$8, %edi
        decl	%ecx
        jnz	1b

        /* Build Level 2 */
        leal	rva(kernel_pgtable + 0x2000)(%ebp), %edi
        movl	$0x00000183, %eax
        movl	$2048, %ecx
    1:	movl	%eax, 0(%edi)
        addl	%edx, 4(%edi)
        addl	$0x00200000, %eax
        addl	$8, %edi
        decl	%ecx
        jnz	1b

        leal	rva(kernel_pgtable)(%ebp), %eax
        movl	%eax, %cr3

        movl    $IA32_EFER, %ecx        /* Extended Feature Enable Register */
        rdmsr

        orl     $IA32_EFER_LME, %eax    /* Enable IA-32e mode */
        wrmsr

        xorl    %eax, %eax
        orl     $CR0_PG, %eax           /* Enable paging */
        orl     $CR0_PE, %eax           /* Enable protected mode */
        movl    %eax, %cr0

        leal    rva(bp_enter_64)(%ebp), %eax
        pushl   $KERNEL_CODE64
        push    %eax
        lret		

.code64
.balign 16,0x90

FUNC_LABEL(bp_enter_64)
	
        movl    %ebx, %esi              /* Save startType */

        movl    $0x80000001, %eax       /* Execute disable check */
        cpuid
        movl    %edx, %edi
		
        movl    %esi, %ebx              /* Restore startType */

        movl    $IA32_EFER, %ecx        /* Extended Feature Enable Register */
        rdmsr

        orl     $IA32_EFER_SCE, %eax    /* System call enable */

        btl     $20,%edi
        jnc     1f
        orl     $IA32_EFER_NX, %eax     /* Enable execute disable */
1:
        wrmsr

        mov     $CR0_INIT_VALUE, %rax
        movq    %rax, %cr0

        push    $0                        /* initialize EFLAGS */
        popf
	
        movw    $KERNEL_DATA64,%ax		/* set data segment 0x10 is 3rd one */
        movw    %ax, %ds
        movw    %ax, %ss
        movw    %ax, %es

        movw    %ax, %fs
        movw    %ax, %gs

        movq %cr4, %rax
        or    $( CR4_OSFXSR | CR4_OSXSAVE), %rax  /* 打开SSE和AVX功能 */
        or    $CR4_PCIDE, %rax                    /* 使能pcid */
        movq  %rax, %cr4
		
        /* ebx has the startType, pass it to C routine usrInit() via %edi */

        movl    %ebx, %edi

        xor     %rdi, %rdi                                  /* 清0 rdi */
        mov     %ebp, %edi
        leal    rva(early_mmu_init)(%ebp), %ebx            /* 获取early_mmu_init函数的物理地址 */
        call    *%rbx                                      /* 跳转到early_mmu_init */

        #leal    rva(sys_gdtr)(%ebp), %eax
        movabs   $sys_gdtr, %rax
        addq     $2, %rax                                  /* 计算base的地址 */
        movabs   $sys_gdt, %rcx                            /* 获取sys_gdt的地址 */
        movq     %rcx, (%rax)                              /* 设置sys_gdt的逻辑地址到sys_gdt地址空间 */

        leal    rva(sys_gdtr)(%ebp), %eax
        lgdt    (%eax)
               
        movabs   $init_stack, %rsp
        addq     $TMP_STACK_SIZE, %rsp                      /* 重新设置栈 */
        andq     $~0xf,%rsp

        movq     %cr3, %rbx
        movq     %rbx, %cr3                                  /* 刷新tlb */

        movabs   $start_kernel, %rax
        call     *%rax

        .align  8
        .globl  initialCcode
initialCcode:
        .quad   early_mmu_init

.section .data
.global kernel_pgtable
.align(0x1000)
kernel_pgtable:
	.fill INIT_PAGE_SPACE_SIZE, 1, 0

.global init_stack
.balign 16
init_stack:
	.fill INIT_STACK_SIZE, 1, 0


.section ".__start", "ax"
.balign 16,0x90
FUNC_LABEL(sys_gdtr)
        .word   GDT_LIMIT                  /* size   : 39(8 * 5 - 1) bytes */
        .quad   0                          /* 用于保存sysGdt 的物理地址，暂时填充为0，后续访问时填充 */

.balign 16,0x90
FUNC_LABEL(sys_gdt)

.word 0,      0, 0,      0	/* 0x00: null descriptor */
.word 0xFFFF, 0, 0x9A00, 0x00CF	/* 0x08: 32-bit kernel code */
.word 0xFFFF, 0, 0x9200, 0x00CF	/* 0x10: 32-bit kernel data */
.word 0,      0, 0x9800, 0x0020	/* 0x18: 64-bit kernel code */
.word 0,      0, 0x9200, 0x0020	/* 0x20: 64-bit kernel data */
/*.word 0xFFFF, 0, 0xFA00, 0x00CF  0x28: 32-bit user code (unused) */
.word 0,      0, 0xF200, 0x0000	/* 0x30: 64-bit user data */
.word 0,      0, 0xF200, 0x0000	/* 0x30: 64-bit user data */
.word 0,      0, 0xF800, 0x0020	/* 0x38: 64-bit user code */

.rept GDT_ENTRYS - 8
gdt_desc 0xffff,0,0x0,0xf2,0xcf,0x00

.endr

.code16
.align	  0x1000
.global _start_ap
_start_ap:
        cli
	cld

	mov    $0, %ax
	mov    %ax, %ds
        mov    %ax, %ss

        mov    $AP_INIT_SP, %sp
	
	lgdtl  AP_GDTR_ADDR

	smsw   %ax              /* 读cr0低16位 */
	orb    $0x01, %al       /* 使能保护模式 */
	andw   $0xff01, %ax     
	lmsw   %ax              /* 写cr0低16位 */

        movw   $(PROTECT_MODE-_start_ap), %ax
        add    $AP_START_ADDR, %ax

        push   $KERNEL_CODE32
        push   %ax
        lret

.code32

PROTECT_MODE:
        movw    $KERNEL_DATA32, %ax
	movw    %ax, %ss
	movw    %ax, %ds
	movw    %ax, %es
	movw    %ax, %fs
	movw    %ax, %gs

        xorl    %eax, %eax              /* zero EAX */
        movl    %cr0, %edx              /* get CR0 */
        andl    $0x7ffafff1, %edx       /* clear PG, AM, WP, TS, EM, MP */
        movl    %edx, %cr0              /* set CR0 */

        pushl   %eax                    /* initialize EFLAGS */
        popfl

        xorl    %eax, %eax              /* zero EAX */
        movl    %eax, %cr4              /* initialize CR4 */
                     
        lgdt    AP_GDTR_ADDR

        movw    $KERNEL_DATA32, %ax	
        movw    %ax, %ds
	movw	%ax, %ss
	movw	%ax, %es
	movw	%ax, %fs
	movw	%ax, %gs

        mov    $(ap_enter_64-_start_ap), %ebx
        add    $AP_START_ADDR, %ebx
       
        /*
        * Prepare for entering 64bits mode
        */	 
        xorl    %eax, %eax
        orl     $CR4_PAE, %eax          /* PAE */
        movl    %eax, %cr4

        /* Enable the boot page tables */
        mov     (AP_CR3), %eax
        movl	%eax, %cr3

        movl    $IA32_EFER, %ecx        /* Extended Feature Enable Register */
        rdmsr

        orl     $IA32_EFER_LME, %eax    /* Enable IA-32e mode */
        wrmsr

        xorl    %eax, %eax
        orl     $CR0_PG, %eax           /* Enable paging */
        orl     $CR0_PE, %eax           /* Enable protected mode */
        movl    %eax, %cr0

        #mov    $(ap_enter_64-_start_ap), %eax
        #add    $AP_START_ADDR, %eax


        pushl    $KERNEL_CODE64
        push     %ebx
        lret
        
.code64
.balign 16,0x90

FUNC_LABEL(ap_enter_64)
        movl    %ebx, %esi              /* Save startType */

        movl    $0x80000001, %eax       /* Execute disable check */
        cpuid
        movl    %edx, %edi
		
        movl    %esi, %ebx              /* Restore startType */

        movl    $IA32_EFER, %ecx        /* Extended Feature Enable Register */
        rdmsr

        orl     $IA32_EFER_SCE, %eax    /* System call enable */

        btl     $20,%edi
        jnc     1f
        orl     $IA32_EFER_NX, %eax     /* Enable execute disable */
1:
        wrmsr

        mov     $CR0_INIT_VALUE, %rax
        movq    %rax, %cr0

        /* %rsp was already set to $STACK_ADRS, and grows down from there */

        push    $0                        /* initialize EFLAGS */
        popf

        #mov     $AP_GDTR_ADDR, %eax
        #lgdt    (%eax)

        movw    $KERNEL_DATA64,%ax		/* set data segment 0x10 is 3rd one */
        movw    %ax, %ds
        movw    %ax, %ss
        movw    %ax, %es
        movw    %ax, %fs
        movw    %ax, %gs

        movq  %cr4, %rax
        or    $( CR4_OSFXSR | CR4_OSXSAVE), %rax  /* 打开SSE和AVX功能 */
        or    $CR4_PCIDE, %rax                    /* 使能pcid */
        movq  %rax, %cr4

        movabs   $sys_gdtr, %rax
        lgdt    (%rax)
	
        mov    $1, %eax
        mov    $0, %rbx
        mov    $0, %rcx
        mov    $0, %rdx
        cpuid
        shrl   $24, %ebx
        andl   $0xff ,%ebx                         /* 获取cpuid存放在ebx中 */

        mov    %ebx, %eax                          /* 存放cpuid到eax中 */
        add    $1, %eax                            /* eax = eax + 1 */
        imul  $TMP_STACK_SIZE, %ebx                /* ebx = eax * TMP_STACK_SIZE */

        movabs  $tmp_stack, %rax
        addq    %rbx, %rax                         /* 计算当前cpu的临时栈 */

        movq    %rax, %rsp                         /* 重新设置栈 */
        andq    $~0xful,%rsp

        movq     %cr3, %rbx
        movq     %rbx, %cr3                        /* 刷新tlb */

        movabs   $start_ap, %rax

        call     *%rax
_start_ap_end:
