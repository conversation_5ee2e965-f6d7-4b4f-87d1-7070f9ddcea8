/**
 * @file    arch/x86_64/arch_ptrace.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2025-03-25
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */
#include "arch/x86_64/ptrace.h"
#include "ptrace/ptrace.h"
#include <ttosProcess.h>

void reset_debug_state(void)
{
    // printk("%s unimplement!!!\n",__func__);
}

void restore_hw_debug(pcb_t pcb)
{
    printk("%s unimplement!!!\n", __func__);
}

int ptrace_setregset(pcb_t pcb, void *uregs, int nt_type)
{
    printk("%s unimplement!!!\n", __func__);
    return 0;
}

int ptrace_getregset(pcb_t pcb, void *uregs, int nt_type)
{
    printk("%s unimplement!!!\n", __func__);
    return 0;
}

void ptrace_cancel_bpt(pcb_t pcb)
{
    printk("%s unimplement!!!\n", __func__);
}

void ptrace_set_bpt(pcb_t child)
{
    printk("%s unimplement!!!\n", __func__);
}

struct user *get_user_regs(pcb_t pcb)
{
    struct user *regs = malloc(sizeof(struct user));

    return regs;
}

int valid_user_regs(struct user *regs)
{
    printk("%s unimplement!!!\n", __func__);
}

void set_user_regs(pcb_t pcb, struct user *regs)
{
    printk("%s unimplement!!!\n", __func__);
}