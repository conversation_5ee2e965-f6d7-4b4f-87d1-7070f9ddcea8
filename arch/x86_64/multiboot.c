#include <commonTypes.h>
#include <memblock.h>
#include <mmu.h>
#include <multiboot.h>
#include <string.h>

#define MULTIBOOT_BOOTLOADER_MAGIC 0x2BADB002

/* Flags field */

#define MULTIBOOT_FLAGS_MEM 0x00000001
#define MULTIBOOT_FLAGS_BOOTDEV 0x00000002
#define MULTIBOOT_FLAGS_CMDLINE 0x00000004
#define MULTIBOOT_FLAGS_MODS 0x00000008
#define MULTIBOOT_FLAGS_AOUTSYMS 0x00000010
#define MULTIBOOT_FLAGS_ELFSHHDR 0x00000020
#define MULTIBOOT_FLAGS_MMAP 0x00000040
#define MULTIBOOT_FLAGS_DRVINFO 0x00000080
#define MULTIBOOT_FLAGS_CFGTBL 0x00000100
#define MULTIBOOT_FLAGS_LDRNAME 0x00000200
#define MULTIBOOT_FLAGS_APMTBL 0x00000400
#define MULTIBOOT_FLAGS_VIDINFO 0x00000800
#define MULTIBOOT_FLAGS_HDRVALID 0x00010000

__attribute__((section(".data"))) unsigned int muti_boot_scratch; /* save magic */
__attribute__((section(".data"))) unsigned int muti_boot_ptr;     /* save multi-boot pointer */

multiboot_info_t multiboot_info;
const char *memmory_type_str[] = {"",    "Available", "Reserved", "ACPI Reclaimable",
                                  "NVS", "Bad RAM"};

extern void printk(const char *fmt, ...);

void multiboot_init(void)
{
    static bool initialized = FALSE;

    if (initialized)
        return;

    initialized = TRUE;

    if (!(*((unsigned int *)(long)muti_boot_ptr)) & MULTIBOOT_INFO_MEM_MAP)
    {
        return;
    }

    uint32_t *p = (uint32_t *)(long)muti_boot_scratch;

    if (muti_boot_scratch != MULTIBOOT_BOOTLOADER_MAGIC)
        return;

    p = (UINT32 *)(long)muti_boot_ptr;

    printk("MULTIBOOT_SCRATCH_PTR = 0x%x\n", muti_boot_ptr);

    memcpy(&multiboot_info, (void *)(long)muti_boot_ptr, sizeof(multiboot_info_t));
}

bool g_multiboot_initrd = false;
phys_addr_t g_multiboot_initrd_start = 0;
phys_addr_t g_multiboot_initrd_end = 0;

void multiboot_mem_scan(void (*mem_add_func)(phys_addr_t addr, phys_addr_t size, void *ctx),
                        void (*mem_reserve_func)(phys_addr_t addr, phys_addr_t size, void *ctx))
{
    uint64_t addr;
    uint64_t length;
    uint32_t flags;
    phys_addr_t start, size;
    uint64_t address;
    uint64_t end_addr;
    int32_t i;
    int32_t region_num = 0;

    flags = (unsigned int)multiboot_info.flags;
#if 0
    printk("flags: 0x%x\n", flags);

    if (flags & MULTIBOOT_FLAGS_MEM)
        printk("mem_lower: 0x%x mem_upper: 0x%x\n", (unsigned int)multiboot_info.mem_lower,
               (unsigned int)multiboot_info.mem_upper);

    if (flags & MULTIBOOT_FLAGS_BOOTDEV)
        printk("boot_device: 0x%x\n", (unsigned int)multiboot_info.boot_device);

    if (flags & MULTIBOOT_FLAGS_CMDLINE)
        printk("cmdline: 0x%x %s\n", (unsigned int)multiboot_info.cmdline, (const char *)(uintptr_t)multiboot_info.cmdline);

    if (flags & MULTIBOOT_FLAGS_MODS)
    {
        printk("mods_length: 0x%x\n", multiboot_info.mods_count);
        printk("mods_addr: 0x%x\n", multiboot_info.mods_addr);

        multiboot_module_t* modules = (multiboot_module_t*) (uintptr_t)multiboot_info.mods_addr;
        for (i = 0; i < multiboot_info.mods_count; i++)
        {
            if(strcmp((const char *)(uintptr_t)modules[i].string, "rootfs") == 0)
            {
                g_multiboot_initrd = true;
                g_multiboot_initrd_start = modules[i].mod_start;
                g_multiboot_initrd_end = modules[i].mod_end;
                mem_reserve_func(g_multiboot_initrd_start, g_multiboot_initrd_end - g_multiboot_initrd_start, NULL);
            }
            printk(">> module[%d]: 0x%x - 0x%x %x(%s)\n", i, modules[i].mod_start, modules[i].mod_end, modules[i].string, (const char *)(uintptr_t)modules[i].string);
        }
    }

    printk("mmap_length: 0x%x\n", multiboot_info.mmap_length);
    printk("mmap_addr: 0x%x\n", multiboot_info.mmap_addr);
#endif
    // 打印表头
    printk("%-10s  %-9s  %-12s  %s\n", "region", "type", "start", "end");
    end_addr = (unsigned long)multiboot_info.mmap_addr + multiboot_info.mmap_length;
    address = (unsigned long)multiboot_info.mmap_addr;

    while (address < end_addr)
    {
        struct multiboot_mmap_entry *mmap = (struct multiboot_mmap_entry *)address;

        printk("region[%-2d]  %-9s  0x%-8llx -- 0x%-8llx\n", region_num,
               memmory_type_str[mmap->type], mmap->addr, mmap->addr + mmap->len);

        region_num++;
        if (mmap->len >= 0x100000)
        {
            mem_add_func(mmap->addr, mmap->len, NULL);
            if (MULTIBOOT_MEMORY_AVAILABLE != mmap->type)
            {
                mem_reserve_func(mmap->addr, mmap->len, NULL);
            }
        }

        address += mmap->size + sizeof(mmap->size);
    }
}