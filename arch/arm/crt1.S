/************************************************************************
 *              科东(广州)软件科技有限公司 版权所有
 *   Copyright (C) 2022 Intewell Inc. All Rights Reserved.
 ***********************************************************************/

/*
 * 修改历史：
 * 2024-02-29    毛玉泽，科东(广州)软件科技有限公司
 *               创建该文件。
 */

/**
 * @file： crt1.S
 * @brief：
 *	    <li>启动代码。</li>
 */
#define STACK_SIZE  0x1000

/************************头 文 件******************************/
#define ASM_USE
#include <asm.h>
#include <cpu.h>

/************************宏 定 义******************************/

/************************类型定义******************************/

/************************外部声明******************************/

/************************前向声明******************************/

/************************模块变量******************************/

/************************函数实现******************************/
.section ".image_header", "ax"
.global _arm_header
_arm_header:
#if  CONFIG_BOOTING_FROM_AARCH64
    .word 0x14020000 // b pc + 0x80000
#else
    b _start
#endif
	.word 0
	/* Image load offset from start of RAM, little-endian */
	.quad   0
	/* Effective size of kernel image, little-endian */
	.quad   0 /* 0x60000 - ignored */
	/* Informative flags, little-endian */
	.quad   0xa
	.quad   0                               /* reserved */
	.quad   0                               /* reserved */
	.quad   0                               /* reserved */
	.byte   0x41                            /* Magic number, "ARM\x64" */
	.byte   0x52
	.byte   0x4d
	.byte   0x64
	.word   0    

/* void _start(void *boot_param, uint32_t boot_param_size) */
.section ".__start", "ax"
.globl _start
_start:
#if CONFIG_ARCH_ARMv7
    mov    r9, r2
#elif CONFIG_ARCH_AARCH32
#if  CONFIG_BOOTING_FROM_AARCH64
    #include "aarch64_to32.S.in"
#endif
    mov    r9, r0
#endif

	/* 屏蔽所有中断 */
	CPSID   iaf

    /* 浮点初始化 */
	bl	   fp_init

    /* 记录物理地址与虚拟地址的偏移存于r7 */
    adr    r0, _start
    ldr    r1, =_start
    sub    r7, r0, r1

    /* 计算出整个内核大小存于r8 */
    ldr    r2, =__end__
    sub    r8, r2, r1

    /* 设置调用cache_dcache_flush的size参数 */
    mov    r1, r8

    /* Clean and invalidate VMK空间cache到 PoC */
    bl     cache_dcache_flush

    /* 关MMU |关数据CACHE |关指令CACHE |异常向量表存放在VBAR寄存器 | 使能对齐检查 */
    ldcopr r0, SCTLR
    bic    r0, r0, #(SCTLR_M | SCTLR_C)
    bic    r0, r0, #(SCTLR_I | SCTLR_V)
    bic    r0, r0, #(SCTLR_A)
    stcopr r0, SCTLR
	isb

    /* 清0 bss段 */
    mov r0,#0                   /* get a zero                       */
    ldr r1,=__bss_start__         /* bss start                        */
    ldr r2,=__bss_end__           /* bss end                          */

    /* 获取物理地址 */
    add     r1, r1, r7
    add     r2, r2, r7

bss_loop:
    cmp r1,r2                   /* check if data to clear           */
    strlo r0,[r1],#4            /* clear 4 bytes                    */
    blo bss_loop                /* loop until done                  */

    /* 设置栈 */

    /* 虚拟地址 */
    ldr     sp, =init_stack
    add     sp, sp, #STACK_SIZE

    /* 获取物理地址 */
    add     sp, sp, r7

    mov r0, r9
    mov r1, r7
    bl dtb_copy

    /* 构建 mmu的 ttbcr寄存器 */
    bl mmu_mair_ttbcr_init

    /* pvoffset */
    mov r0, r7

    bl mmu_setup_early

    ldr lr, =after_mmu_enable  /* set LR to after_mmu_enable function, it's a v_addr */

    /* 获取物理地址 */
    adr    r7, _start

    b start_enbale_mmu

after_mmu_enable:

    mov r0, #0
    mov r1, #0
    stcopr64 r1, r0, TTBR0_64
    dsb sy

    /* 设置异常向量表 */
    ldr    r0, =vector_table
    stcopr r0, VBAR

    /* 重新设置栈 */
    ldr    sp, =init_stack
    add    sp, sp, #STACK_SIZE

    mov r0, r7
    mov r1, #0
    ldr r2, =_start
    bl kernel_mmu_set_pvoffset

    /* 使能所有中断 */
	#CPSIE  iaf
    b      start_kernel

/* 从核启动入口 */
ENTRY(_start_ap)

	/* 屏蔽所有中断 */
	CPSID	iaf

#if CONFIG_ARCH_ARMv7
    ldcopr r0, MPIDR
    and    r0, r0, #0xff
#endif /* CONFIG_ARCH_ARMv7 */

	/* 保存cpu index + 1*/
    add    r9, r0, #1

    /* 浮点初始化 */
	bl	   fp_init

	/* 关MMU |关数据CACHE |关指令CACHE |异常向量表存放在VBAR寄存器 */
	ldcopr r0, SCTLR
	bic    r0, r0, #(SCTLR_M | SCTLR_C)
	bic    r0, r0, #(SCTLR_I | SCTLR_V)
	stcopr r0, SCTLR
	isb

	/* 使能对齐检查 */
	ldcopr r0, SCTLR
	bic    r0, r0, #(SCTLR_A)
	stcopr r0, SCTLR
	isb

    /* 记录物理地址与虚拟地址的偏移存于r7 */
    adr    r0, _start_ap
    ldr    r1, =_start_ap
    sub    r7, r0, r1

    /* 设置栈 */
    mov    r0, #STACK_SIZE
    mul    r9, r9, r0
    ldr    r0, =init_stack
    add    r9, r9, r0

    /* 获取物理地址 */
    add    sp, r9, r7

    /* 构建 mmu的 ttbcr寄存器 */
    bl      mmu_mair_ttbcr_init

    /* pvoffset */
    mov r0, r7

    bl ap_mmu_setup_early

    ldr    r0, =_kernel_mmu_table0
    add    r0, r0, r7

    mov r1, #0
    stcopr64 r0, r1, TTBR1_64
    dsb sy

    ldr lr, =__ap_after_mmu_enable  /* set LR to after_mmu_enable function, it's a v_addr */

    b start_enbale_mmu

__ap_after_mmu_enable:

    mov r0, #0
    mov r1, #0
    stcopr64 r1, r0, TTBR0_64
    dsb sy

	/* 设置异常向量表 */
	ldr    r0, =vector_table
	stcopr r0, VBAR

	/* 设置栈 */
	mov    sp, r9

	/* 使能所有中断 */
	#CPSIE  iaf
	b	   start_ap

ENDPROC(_start_ap)

ENTRY(start_enbale_mmu)
    /*ARMv7 smp位需要置1，若不置位cache不正常原子操作不能使用 */
	ldcopr r0, ACTLR
	orr	r0, r0, #(0x01 << 6)
	stcopr r0, ACTLR

    ldcopr r0, SCTLR
    orr r0, r0, #(1 << 12)   /* I */
    orr r0, r0, #(1 << 2)    /* C */
    orr r0, r0, #(1 << 0)    /* M */
    stcopr r0, SCTLR        /* enable MMU */

    /* 无效TLB */
    mov	   r0, #0
    stcopr r0, ICIALLU      /* Invalidate all instruction caches in Inner Shareable domain to Point of Unification */
    dsb sy
    isb sy
    stcopr r0, TLBIALL    /* Invalidate all stage 1 translations used at EL1 with the current VMID */
    dsb sy
    isb sy

    bx lr

ENDPROC(start_enbale_mmu)

ENTRY(fp_init)

    /* 设置CPACR.cp10为0x3,使能 FP/SIMD，以便CPU能正常执行后续浮点指令。*/
    mov    r1, #0x3
    mov    r1, r1,lsl#20
    stcopr r1, CPACR
    dsb    sy
    isb

    /* 使能FPEXC.EN位，开启FP/SIMD浮点功能*/
    mov    r1, #1
    mov    r1,r1, lsl#30
    vmsr   fpexc, r1

    /* 清零fpscr,将浮点异常产生标识位清除 */
    vmrs   r3, fpscr
    bic    r3, r3, #0x9f
    vmsr   fpscr, r3

    /* 使能浮点异常 */
    vmrs   r3, fpscr
    orr    r3, r3, #0
    vmsr   fpscr, r3

    /* 寄存器清零 */
    vmov.i64	d0, #0
    vmov.i64	d1, #0
    vmov.i64	d2, #0
    vmov.i64	d3, #0
    vmov.i64	d4, #0
    vmov.i64	d5, #0
    vmov.i64	d6, #0
    vmov.i64	d7, #0
    vmov.i64	d8, #0
    vmov.i64	d9, #0
    vmov.i64	d10, #0
    vmov.i64	d11, #0
    vmov.i64	d12, #0
    vmov.i64	d13, #0
    vmov.i64	d14, #0
    vmov.i64	d15, #0
    vmov.i64	d16, #0
    vmov.i64	d17, #0
    vmov.i64	d18, #0
    vmov.i64	d19, #0
    vmov.i64	d20, #0
    vmov.i64	d21, #0
    vmov.i64	d22, #0
    vmov.i64	d23, #0
    vmov.i64	d24, #0
    vmov.i64	d25, #0
    vmov.i64	d26, #0
    vmov.i64	d27, #0
    vmov.i64	d28, #0
    vmov.i64	d29, #0
    vmov.i64	d30, #0
    vmov.i64	d31, #0

    mov    pc, lr
ENDPROC(fp_init)

.section ".data"
.align 3
init_stack:
    .space STACK_SIZE * CONFIG_MAX_CPUS
