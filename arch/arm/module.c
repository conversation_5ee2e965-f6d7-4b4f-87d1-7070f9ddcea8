﻿
#include <elf.h>
#include <asm_module.h>

#include <system/types.h>
#include <errno.h>
#include <module.h>

#undef KLOG_TAG
#define KLOG_TAG "Module"
#include <klog.h>

#define cpu_to_le64(x) ((uint64_t)(x))
#define le64_to_cpu(x) ((uint64_t)(x))
#define cpu_to_le32(x) ((uint32_t)(x))
#define le32_to_cpu(x) ((uint32_t)(x))
#define cpu_to_le16(x) ((uint16_t)(x))
#define le16_to_cpu(x) ((uint16_t)(x))


# define fallthrough                    do {} while (0)  /* fallthrough */

#ifndef BIT /* in case third-party project also defined marco BIT */
#define BIT(nr) (1ULL << (nr))
#endif



int apply_relocate(Elf32_Shdr *sechdrs, const char *strtab, unsigned int symindex,
	       unsigned int relindex, struct module *module)
{
    Elf32_Shdr *symsec = sechdrs + symindex;
    Elf32_Shdr *relsec = sechdrs + relindex;
    Elf32_Shdr *dstsec = sechdrs + relsec->sh_info;
    Elf32_Rel *rel = (void *)relsec->sh_addr;
    unsigned int i;

    for (i = 0; i < relsec->sh_size / sizeof(Elf32_Rel); i++, rel++) 
    {
        unsigned long loc;
        Elf32_Sym *sym;
        const char *symname;
        s32 symidx;
        s32 offset;
        u32 tmp;
      
        symidx = ELF32_R_SYM(rel->r_info);
        if (symidx < 0 || symidx > (symsec->sh_size / sizeof(Elf32_Sym))) 
        {
          KLOG_E("%s: section %u reloc %u: bad relocation sym offset\n", module->name, relindex, i);
          return -ENOEXEC;
        }
        
        sym = ((Elf32_Sym *)symsec->sh_addr) + symidx;
        symname = strtab + sym->st_name;
        
        if (rel->r_offset < 0 || rel->r_offset > dstsec->sh_size - sizeof(u32)) 
        {
          KLOG_E("%s: section %u reloc %u sym '%s': out of bounds relocation, offset %d size %u\n",
                  module->name, relindex, i, symname, rel->r_offset, dstsec->sh_size);
          return -ENOEXEC;
        }
    
        loc = dstsec->sh_addr + rel->r_offset;
    
        switch (ELF32_R_TYPE(rel->r_info)) 
        {
        case R_ARM_NONE:
          /* ignore */
          break;
        
        case R_ARM_ABS32:
        case R_ARM_TARGET1:
          *(u32 *)loc += sym->st_value;
          break;
        
        case R_ARM_PC24:
        case R_ARM_CALL:
        case R_ARM_JUMP24:
          if (sym->st_value & 3) 
          {
            KLOG_E("%s: section %u reloc %u sym '%s': unsupported interworking call (ARM -> Thumb)\n",
                    module->name, relindex, i, symname);
            return -ENOEXEC;
          }
        
          offset = __mem_to_opcode_arm(*(u32 *)loc);
          offset = (offset & 0x00ffffff) << 2;
          if (offset & 0x02000000)
            offset -= 0x04000000;
        
          offset += sym->st_value - loc;
    
          /*
          * Route through a PLT entry if 'offset' exceeds the
          * supported range. Note that 'offset + loc + 8'
          * contains the absolute jump target, i.e.,
          * @sym + addend, corrected for the +8 PC bias.
          */
//#ifdef CONFIG_ARM_MODULE_PLTS
          if ( (offset <= (s32)0xfe000000 || offset >= (s32)0x02000000) )
            offset = get_module_plt(module, loc, offset + loc + 8) - loc - 8;
//#endif

          if ( offset <= (s32)0xfe000000 || offset >= (s32)0x02000000 ) 
          {
            KLOG_E("%s: section %u reloc %u sym '%s': relocation %u out of range (%#lx -> %#x)\n",
                   module->name, relindex, i, symname, ELF32_R_TYPE(rel->r_info), loc, sym->st_value);
            return -ENOEXEC;
          }
        
          offset >>= 2;
          offset &= 0x00ffffff;
          
          //*(u32 *)loc &= __opcode_to_mem_arm(0xff000000);
          //*(u32 *)loc |= __opcode_to_mem_arm(offset);
          *(u32 *)loc &= 0xff000000;
          *(u32 *)loc |= offset;
          break;
        
        case R_ARM_V4BX:
          /* Preserve Rm and the condition code. Alter
          * other bits to re-code instruction as
          * MOV PC,Rm.
          */
          //*(u32 *)loc &= __opcode_to_mem_arm(0xf000000f);
          //*(u32 *)loc |= __opcode_to_mem_arm(0x01a0f000);
          *(u32 *)loc &= 0xf000000f;
          *(u32 *)loc |= 0x01a0f000;
          break;

        case R_ARM_PREL31:
          offset = (*(s32 *)loc << 1) >> 1; /* sign extend */
          offset += sym->st_value - loc;
          if (offset >= 0x40000000 || offset < -0x40000000) 
          {
            KLOG_E("%s: section %u reloc %u sym '%s': relocation %u out of range (%#lx -> %#x)\n",
                   module->name, relindex, i, symname, ELF32_R_TYPE(rel->r_info), loc, sym->st_value);
            return -ENOEXEC;
          }
          *(u32 *)loc &= 0x80000000;
          *(u32 *)loc |= offset & 0x7fffffff;
          break;

        case R_ARM_MOVW_ABS_NC:
        case R_ARM_MOVT_ABS:
          //offset = tmp = __mem_to_opcode_arm(*(u32 *)loc);
          offset = tmp = *(u32 *)loc;
          offset = ((offset & 0xf0000) >> 4) | (offset & 0xfff);
          //offset = (offset ^ 0x8000) - 0x8000; 得到offset在32位下的补码

          offset += sym->st_value;
          if (ELF32_R_TYPE(rel->r_info) == R_ARM_MOVT_ABS)
              offset >>= 16;

          tmp &= 0xfff0f000;
          tmp |= ((offset & 0xf000) << 4) | (offset & 0x0fff);

          //*(u32 *)loc = __opcode_to_mem_arm(tmp);
          *(u32 *)loc = tmp;
          break;

        default:
          KLOG_E("%s: unknown relocation: %u\n",
                  module->name, ELF32_R_TYPE(rel->r_info));
          return -ENOEXEC;
        }
    }

    return 0;
}

int apply_relocate_add(Elf_Shdr *sechdrs,
			   const char *strtab,
			   unsigned int symindex,
			   unsigned int relsec,
			   struct module *me)
{
   KLOG_E("REL relocation unsupported, RELA\n");
   return -ENOEXEC;
}

int klp_apply_section_relocs(struct module *pmod, Elf_Shdr *sechdrs,
				 const char *shstrtab, const char *strtab,
				 unsigned int symindex, unsigned int secindex,
				 const char *objname)
{
    KLOG_E("module: REL relocation unsupported, LIVEPATCH\n");
    return -ENOEXEC;
}

int elfmodule_finalize(const Elf_Ehdr *hdr,
		    const Elf_Shdr *sechdrs,
		    struct module *me)
{
    //待实现
    return 0;
}

