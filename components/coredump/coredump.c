#ifdef CONFIG_COREDUMP

#include <elf.h>
#include <errno.h>
#include <mmu.h>
#include <stdio.h>
#include <stdlib.h>

#include <coredump.h>
#include <process_signal.h>
#include <assert.h>

#undef KLOG_TAG
#define KLOG_TAG "coredump"
#include <klog.h>

/**
 * @brief 删除文件名中可能包含的 / 和 . 字符避免被识别为新的目录
 */
static void format_corename (char *corename)
{
    pid_t pid = get_process_pid(ttosProcessSelf());
    char *cur_name = (char *)task_get_by_tid (pid)->objCore.objName;
    
    char helper[CORENAME_MAX_SIZE] = {'\0'};
    int idx = 0;

    while (*cur_name != '\0')
    {
        if (*cur_name != '.' && *cur_name != '/')
        {
            helper[idx++] = *cur_name;
        }

        cur_name++;
    }

    helper[idx] = '\0';
    
    snprintf (corename, sizeof (helper), "/var/corefile/core-%s", helper);
}

static bool coredump_to_file (int signo)
{
    int  ret = 0;
    bool coredumped = false;
    char *corename = calloc(sizeof(char), CORENAME_MAX_SIZE);

    pcb_t self = ttosProcessSelf();
    assert (self != NULL);

    pcb_t leader = self->group_leader;
    assert (leader != NULL);

    format_corename (corename);

    ret = elf_coredump (corename, signo);
    if (ret != 0)
    {
        KLOG_E ("elf_coredump failed, ret %d", ret);
        goto out;
    }

    free(corename);
    coredumped = true;

out:

    return coredumped;

}

int ttos_coredump (int signo)
{
    return coredump_to_file (signo);
}

#endif /* CONFIG_COREDUMP */