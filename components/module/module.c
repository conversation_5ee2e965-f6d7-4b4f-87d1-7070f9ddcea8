#include <cache.h>
#include <elf.h>
#include <errno.h>
#include <fcntl.h>
#include <kmalloc.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <symtab.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <system/bitops.h>

#include <ttos.h>
#include <ttosProcess.h>
#include <ttosUtils.h>
#include <unistd.h>

#include <asm_module.h>
#include <module.h>
#include <system/err.h>

#include "module_internal.h"

#undef KLOG_TAG
#define KLOG_TAG "module"
#include <klog.h>

#include <ttos_init.h>

#include <coredump.h>

#define HWCAP_TLS (1 << 15)

#define mutex_lock(lock) TTOS_ObtainMutex(*lock, TTOS_MUTEX_WAIT_FOREVER)
#define mutex_unlock(lock) TTOS_ReleaseMutex(*lock)

#define kmalloc(size, flags) malloc(size)
#define kfree(ptr) free(ptr)
#define kstrdup(s) strdup(s)

#ifndef ARRAY_SIZE
#define ARRAY_SIZE(x) (sizeof(x) / sizeof((x)[0]))
#endif

//#ifdef CONFIG_ARCH_HAS_STRICT_MODULE_RWX
#if 1
#define rwx_config_align(X) ALIGN(X, PAGE_SIZE)
#else
#define rwx_config_align(X) (X)
#endif

/* If this is set, the section belongs in the init part of the module */
#define INIT_OFFSET_MASK (1UL << (BITS_PER_LONG - 1))

#define ISELF32(elfFile) (((Ehdr *)elfFile)->e_ident[EI_CLASS] == ELFCLASS32)
#define ISELF64(elfFile) (((Ehdr *)elfFile)->e_ident[EI_CLASS] == ELFCLASS64)
/*
 * Mutex protects:
 * 1) List of modules (also safely readable with preempt_disable),
 * 2) module_use links,
 * 3) module_addr_min/module_addr_max.
 * (delete and add uses RCU list operations). */
// DEFINE_MUTEX(elfmodule_mutex);  //ttos的mutex不能静态初始化
MUTEX_ID elfmodule_mutex;
static LIST_HEAD(elfmodules_list);

static int TTOS_ElfModule_Init(void)
{
    T_TTOS_ReturnCode ret;
    ret = TTOS_CreateMutex(1, 0, &elfmodule_mutex);
    return ret == TTOS_OK ? 0 : -1;
}

INIT_EXPORT_COMPONENTS(TTOS_ElfModule_Init, "init elfmodule");

#ifdef CONFIG_STRICT_MODULE_RWX
static void elfmodule_enable_x(const struct module *mod)
{
    // 待实现
}

static void elfmodule_enable_nx(const struct module *mod)
{
    // 待实现
}
static void elfmodule_enable_ro(const struct module *mod, bool after_init)
{
    // 待实现
}
static int elfmodule_enforce_rwx_sections(Elf_Ehdr *hdr, Elf_Shdr *sechdrs, char *secstrings,
                                          struct module *mod)
{
    // 待实现
    return 0;
}

#else  /* !CONFIG_STRICT_MODULE_RWX */
static void elfmodule_enable_x(const struct module *mod) {}
static void elfmodule_enable_nx(const struct module *mod) {}
static void elfmodule_enable_ro(const struct module *mod, bool after_init) {}
static int elfmodule_enforce_rwx_sections(Elf_Ehdr *hdr, Elf_Shdr *sechdrs, char *secstrings,
                                          struct module *mod)
{
    return 0;
}
#endif /*  CONFIG_STRICT_MODULE_RWX */

/*
 * Search for module by name: must hold module_mutex (or preempt disabled
 * for read-only access).
 */
static struct module *find_all_elfmodule(const char *name, size_t len, bool include_unformed)
{
    struct module *mod;

    list_for_each_entry(mod, &elfmodules_list, list)
    {
        // even_unformed 1 代表即使是unformed状态的module也会查找
        if (!include_unformed && mod->state == MODULE_STATE_UNFORMED)
            continue;
        if (strlen(mod->name) == len && !memcmp(mod->name, name, len))
            return mod;
    }
    return NULL;
}

void foreach_elfmodule(void (*fn)(struct module *, void *), void *data)
{
    struct module *mod;

    list_for_each_entry(mod, &elfmodules_list, list)
    {
        fn(mod, data);
    }
}

struct module *find_module(const char *name)
{
    return find_all_elfmodule(name, strlen(name), false);
}

//#ifdef CONFIG_SMP
#if 0

static inline void __percpu *elfmodule_percpu (struct module *mod)
{
    return mod->percpu;
}

static int elfmodule_percpu_alloc (struct module        *mod,
                                   struct mod_load_info *info)
{
    Elf_Shdr     *pcpusec = &info->sechdrs[info->index.pcpu];
    unsigned long align   = pcpusec->sh_addralign;

    if (!pcpusec->sh_size)
        return 0;

    if (align > PAGE_SIZE)
    {
        KLOG_E ("%s: per-cpu alignment %li > %li\n", mod->name, align,
                PAGE_SIZE);
        align = PAGE_SIZE;
    }

    mod->percpu = __alloc_reserved_percpu (pcpusec->sh_size, align);
    if (!mod->percpu)
    {
        KLOG_E ("%s: Could not allocate %lu bytes percpu data\n", mod->name,
                (unsigned long)pcpusec->sh_size);
        return -ENOMEM;
    }

    mod->percpu_size = pcpusec->sh_size;
    return 0;
}

static void elfmodule_percpu_free (struct module *mod)
{
    free_percpu (mod->percpu);
}

static unsigned int elfmodule_find_pcpusec (struct mod_load_info *info)
{
    return elfmodule_find_sec (info, ".data..percpu");
}

static void elfmodule_percpu_copy (struct module *mod, const void *from,
                                   unsigned long size)
{
    int cpu;

    for_each_possible_cpu (cpu)
        memcpy (per_cpu_ptr (mod->percpu, cpu), from, size);
}

bool __is_module_percpu_address (unsigned long addr, unsigned long *can_addr)
{
    struct module *mod;
    unsigned int   cpu;

    preempt_disable ();

    list_for_each_entry_rcu (mod, &modules, list)
    {
        if (mod->state == MODULE_STATE_UNFORMED)
            continue;
        if (!mod->percpu_size)
            continue;
        for_each_possible_cpu (cpu)
        {
            void *start = per_cpu_ptr (mod->percpu, cpu);
            void *va    = (void *)addr;

            if (va >= start && va < start + mod->percpu_size)
            {
                if (can_addr)
                {
                    *can_addr = (unsigned long)(va - start);
                    *can_addr += (unsigned long)per_cpu_ptr (
                        mod->percpu, get_boot_cpu_id ());
                }
                preempt_enable ();
                return true;
            }
        }
    }

    preempt_enable ();
    return false;
}

/**
 * is_module_percpu_address - test whether address is from module static percpu
 * @addr: address to test
 *
 * Test whether @addr belongs to module static percpu area.
 *
 * RETURNS:
 * %true if @addr is from module static percpu area
 */
bool is_module_percpu_address (unsigned long addr)
{
    return __is_module_percpu_address (addr, NULL);
}

#else /* ... !CONFIG_SMP */

static inline void __percpu *elfmodule_percpu(struct module *mod)
{
    return NULL;
}
static int elfmodule_percpu_alloc(struct module *mod, struct mod_load_info *info)
{
    /* UP modules shouldn't have this section: ENOMEM isn't quite right */
    if (info->sechdrs[info->index.pcpu].sh_size != 0)
        return -ENOMEM;
    return 0;
}
static inline void elfmodule_percpu_free(struct module *mod) {}
static unsigned int elfmodule_find_pcpusec(struct mod_load_info *info)
{
    return 0;
}
static inline void elfmodule_percpu_copy(struct module *mod, const void *from, unsigned long size)
{
    /* pcpusec should be 0, and size of that section should be 0. */
    BUG_ON(size != 0);
}
bool is_module_percpu_address(unsigned long addr)
{
    return false;
}

bool __is_module_percpu_address(unsigned long addr, unsigned long *can_addr)
{
    return false;
}

#endif /* CONFIG_SMP */

/* try_module_get的强化版本 ,更严格的条件 不能是coming 也不能是unformed */
/*
 * We require a truly strong try_module_get(): 0 means success.
 * Otherwise an error is returned due to ongoing or failed
 * initialization etc.
 */
static inline int strong_try_module_get(struct module *mod)
{
    BUG_ON(mod && mod->state == MODULE_STATE_UNFORMED);
    if (mod && mod->state == MODULE_STATE_COMING)
        return -EBUSY;

    if (try_module_get(mod))
        return 0;
    else
        return -ENOENT;
}

#ifdef CONFIG_MODULE_UNLOAD

/* MODULE_REF_BASE is the base reference count by kmodule loader. */
#define MODULE_REF_BASE 1

/* Init the unload section of the module. */
static int elfmodule_unload_init(struct module *mod)
{
    /*
     * Initialize reference counter to MODULE_REF_BASE.
     * refcnt == 0 means module is going.
     */
    atomic_set(&mod->refcnt, MODULE_REF_BASE);

    INIT_LIST_HEAD(&mod->source_list);
    INIT_LIST_HEAD(&mod->target_list);

    /* Hold reference count during initialization. */
    atomic_inc(&mod->refcnt);

    return 0;
}

int foreach_module_use(struct module *mod, int (*fn)(struct module_use *, void *), void *data)
{
    struct module_use *use;

    list_for_each_entry(use, &mod->source_list, source_list)
    {
        fn(use, data);
    }
    return 0;
}

/* Does a already use b? */
static int moduleA_already_uses_moduleB(struct module *a, struct module *b)
{
    struct module_use *use;

    list_for_each_entry(use, &b->source_list, source_list)
    {
        if (use->source == a)
        {
            KLOG_E("%s uses %s!\n", a->name, b->name);
            return 1;
        }
    }

    KLOG_E("%s does not use %s!\n", a->name, b->name);
    return 0;
}

/*
 * Module a uses b
 *  - we add 'a' as a "source", 'b' as a "target" of module use
 *  - the module_use is added to the list of 'b' sources (so
 *    'b' can walk the list to see who sourced them), and of 'a'
 *    targets (so 'a' can see what modules it targets).
 */
static int elfmodule_add_dependency(struct module *a, struct module *b)
{
    struct module_use *use;

    KLOG_E("Allocating new usage for %s.\n", a->name);
    use = kmalloc(sizeof(*use), GFP_ATOMIC);
    if (!use)
        return -ENOMEM;

    use->source = a;
    use->target = b;
    list_add(&use->source_list, &b->source_list);
    list_add(&use->target_list, &a->target_list);
    return 0;
}

/* Module a uses b: caller needs module_mutex() */
static int ref_module(struct module *a, struct module *b)
{
    int err;

    /* already_uses函数, a已经使用了b, 就不再增加引用计数 */
    if (b == NULL || moduleA_already_uses_moduleB(a, b))
        return 0;

    /* If module isn't available, we fail. */
    err = strong_try_module_get(b);
    if (err)
        return err;

    err = elfmodule_add_dependency(a, b);
    if (err)
    {
        module_put(b);
        return err;
    }
    return 0;
}

/* Clear the unload stuff of the module. */
static void elfmodule_unload_free(struct module *mod)
{
    struct module_use *use, *tmp;

    mutex_lock(&elfmodule_mutex);
    list_for_each_entry_safe(use, tmp, &mod->target_list, target_list)
    {
        struct module *i = use->target;
        KLOG_E("%s unusing %s\n", mod->name, i->name);
        module_put(i);
        list_del(&use->source_list);
        list_del(&use->target_list);
        kfree(use);
    }
    mutex_unlock(&elfmodule_mutex);
}

#ifdef CONFIG_MODULE_FORCE_UNLOAD
static inline int try_force_unload_elfmodule(unsigned int flags)
{
    int ret = (flags & O_TRUNC);
    if (ret)
        add_taint(TAINT_FORCED_RMMOD, LOCKDEP_NOW_UNRELIABLE);
    return ret;
}
#else
static inline int try_force_unload_elfmodule(unsigned int flags)
{
    return 0;
}
#endif /* CONFIG_MODULE_FORCE_UNLOAD */

static int try_release_elfmodule_ref(struct module *mod)
{
    int ret;

    /* 自减后,返回新值 */
    /* Try to decrement refcnt which we set at loading */
    ret = atomic_sub_return(&mod->refcnt, MODULE_REF_BASE);
    BUG_ON(ret < 0);
    /* 非0,代表有别人在使用 */
    if (ret)
        /* refcnt  等于0，返回false0，不自增; 不等于0，返回true1，自增 */
        /* Someone can put this right now, recover with checking */
        ret = atomic_add_unless(&mod->refcnt, MODULE_REF_BASE, 0);

    return ret;
}

static int try_stop_elfmodule(struct module *mod, int flags, int *forced)
{
    /* If it's not unused, quit unless we're forcing. */
    if (try_release_elfmodule_ref(mod) != 0)
    {
        /* 进入该if, 有别人在使用 */
        *forced = try_force_unload_elfmodule(flags);
        if (!(*forced))
            return -EWOULDBLOCK;
    }

    /* Mark it as dying. */
    mod->state = MODULE_STATE_GOING;

    return 0;
}

/* 确认状态不是going,增加引用计数 */
bool try_module_get(struct module *module)
{
    bool ret = 1;

    if (module)
    {
        preempt_disable();

        /* Note: here, we can fail to get a reference */
        if (likely(module_is_live(module) && atomic_inc_not_zero(&module->refcnt) != 0))
        {
        } // trace_module_get(module, _RET_IP_);
        else
        {
            ret = 0;
        }

        preempt_enable();
    }
    return ret;
}
// EXPORT_SYMBOL(try_module_get);

void module_put(struct module *module)
{
    int ret;

    if (module)
    {
        preempt_disable();

        ret = atomic_dec_if_positive(&module->refcnt);
        // WARN_ON(ret < 0);	/* Failed to put refcount */

        preempt_enable();
    }
}
// EXPORT_SYMBOL(module_put);

#else /* !CONFIG_MODULE_UNLOAD */

static inline void elfmodule_unload_free(struct module *mod) {}

static int ref_module(struct module *a, struct module *b)
{
    return strong_try_module_get(b);
}

static inline int elfmodule_unload_init(struct module *mod)
{
    return 0;
}

#endif /* CONFIG_MODULE_UNLOAD */

#define MODINFO_ATTR(field)                                                                        \
    static void setup_modinfo_##field(struct module *mod, const char *s)                           \
    {                                                                                              \
        mod->field = kstrdup(s);                                                                   \
    }                                                                                              \
    static size_t show_modinfo_##field(struct module_attribute *mattr, char *buffer)               \
    {                                                                                              \
        return 0;                                                                                  \
    }                                                                                              \
    static int modinfo_##field##_exists(struct module *mod)                                        \
    {                                                                                              \
        return mod->field != NULL;                                                                 \
    }                                                                                              \
    static void free_modinfo_##field(struct module *mod)                                           \
    {                                                                                              \
        kfree(mod->field);                                                                         \
        mod->field = NULL;                                                                         \
    }                                                                                              \
    static struct module_attribute modinfo_##field = {                                             \
        .attr = {.name = #field, .mode = 0444},                                                    \
        .show = show_modinfo_##field,                                                              \
        .setup = setup_modinfo_##field,                                                            \
        .test = modinfo_##field##_exists,                                                          \
        .free = free_modinfo_##field,                                                              \
    };

MODINFO_ATTR(version);
MODINFO_ATTR(srcversion);

#define __ATTR(_name, _mode, _show, _store)                                                        \
    {                                                                                              \
        .attr = {.name = #_name, .mode = (_mode)}, .show = _show, .store = _store,                 \
    }

static size_t show_initstate(struct module_attribute *mattr, char *buffer)
{
    // 待实现,用来配合sysfs读取代表该属性的文件
    return 0;
}
static struct module_attribute modinfo_initstate = __ATTR(initstate, 0444, show_initstate, NULL);

static size_t store_uevent(struct module_attribute *mattr, const char *buffer, size_t count)
{
    // 待实现,用来配合sysfs写入代表该属性的文件
    return 0;
}
struct module_attribute module_uevent = __ATTR(uevent, 0200, NULL, store_uevent);

static size_t show_coresize(struct module_attribute *mattr, char *buffer)
{
    // 待实现,用来配合sysfs读取代表该属性的文件
    return 0;
}
static struct module_attribute modinfo_coresize = __ATTR(coresize, 0444, show_coresize, NULL);

static size_t show_initsize(struct module_attribute *mattr, char *buffer)
{
    // 待实现,用来配合sysfs读取代表该属性的文件
    return 0;
}
static struct module_attribute modinfo_initsize = __ATTR(initsize, 0444, show_initsize, NULL);

static size_t show_taint(struct module_attribute *mattr, char *buffer)
{
    // 待实现,用来配合sysfs读取代表该属性的文件
    return 0;
}
static struct module_attribute modinfo_taint = __ATTR(taint, 0444, show_taint, NULL);

static size_t show_refcnt(struct module_attribute *mattr, char *buffer)
{
    // 待实现,用来配合sysfs读取代表该属性的文件
    return 0;
}
static struct module_attribute modinfo_refcnt = __ATTR(refcnt, 0444, show_refcnt, NULL);

static struct module_attribute *modinfo_attrs[] = {
    //&module_uevent,
    &modinfo_version,
    //&modinfo_srcversion,
    &modinfo_initstate,
    &modinfo_coresize,
    &modinfo_initsize,
    &modinfo_taint,
#ifdef CONFIG_MODULE_UNLOAD
    &modinfo_refcnt,
#endif
    NULL,
};

#ifdef CONFIG_SYSFS
static int mod_sysfs_setup(struct module *mod, const struct mod_load_info *info,
                           // struct kernel_param        *kparam,
                           unsigned int num_params)
{
    return 0;
}

static void mod_sysfs_fini(struct module *mod) {}

static void module_remove_modinfo_attrs(struct module *mod, int end) {}

static void del_usage_links(struct module *mod) {}

static void init_param_lock(struct module *mod) {}

#else
static int mod_sysfs_setup(struct module *mod, const struct mod_load_info *info,
                           // struct kernel_param        *kparam,
                           unsigned int num_params)
{
    return 0;
}

static void mod_sysfs_fini(struct module *mod) {}

static void module_remove_modinfo_attrs(struct module *mod, int end) {}

static void del_usage_links(struct module *mod) {}

static void init_param_lock(struct module *mod) {}

#endif

bool elfmodule_prefix_init_section(const char *name)
{
    return strncmp(name, ".init", strlen(".init")) == 0;
}

bool elfmodule_prefix_exit_section(const char *name)
{
    return strncmp(name, ".exit", strlen(".exit")) == 0;
}

/* 返回指定section的索引,0:没找到 */
static unsigned int elfmodule_find_sec(const struct mod_load_info *info, const char *name)
{
    unsigned int i;

    for (i = 1; i < info->hdr->e_shnum; i++)
    {
        Elf_Shdr *shdr = &info->sechdrs[i];
        /* Alloc bit cleared means "ignore it." */
        if ((shdr->sh_flags & SHF_ALLOC) && strcmp(info->secstrings + shdr->sh_name, name) == 0)
            // if ( strcmp(info->secstrings + shdr->sh_name, name) == 0 )
            return i;
    }
    return 0;
}

/* 返回指定section的addr */
static void *elfmodule_section_addr(const struct mod_load_info *info, const char *name)
{
    /* Section 0 has sh_addr 0. */
    return (void *)info->sechdrs[elfmodule_find_sec(info, name)].sh_addr;
}

/* 对于内容是num个object_size的section,  设置num,返回addr */
static void *elfmodule_section_objs(const struct mod_load_info *info, const char *name,
                                    size_t object_size, unsigned int *num)
{
    unsigned int sec = elfmodule_find_sec(info, name);

    /* Section 0 has sh_addr 0 and sh_size 0. */
    *num = info->sechdrs[sec].sh_size / object_size;
    return (void *)info->sechdrs[sec].sh_addr;
}

/* 对size先用sh_addralign对齐,再加上sh_size,返回sec基址 */
/* Update size with this section: return offset. */
static unsigned long update_size_get_offset(struct module *mod, unsigned int *size,
                                            Elf_Shdr *sechdr, unsigned int section)
{
    unsigned long ret;

    //*size += arch_mod_section_prepend(mod, section);
    ret = ALIGN(*size, sechdr->sh_addralign ? sechdr->sh_addralign : 1);
    *size = ret + sechdr->sh_size;
    return ret;
}

/* Parse tag=value strings from .modinfo section */
static char *next_string(char *string, unsigned long *secsize)
{
    /* Skip non-zero chars */
    while (string[0])
    {
        string++;
        if ((*secsize)-- <= 1)
            return NULL;
    }

    /* Skip any zero padding. */
    while (!string[0])
    {
        string++;
        if ((*secsize)-- <= 1)
            return NULL;
    }

    return string;
}

/* 获取elf文件.modinfo sec内,指定的tag对应的value */
static char *get_next_modinfo_in_elfsec(const struct mod_load_info *info, const char *tag,
                                        char *prev)
{
    char *p;
    unsigned int taglen = strlen(tag);
    Elf_Shdr *modinfosec = &info->sechdrs[info->index.info];
    unsigned long size = modinfosec->sh_size;

    /*
     * get_modinfo() calls made before rewrite_section_headers()
     * must use sh_offset, as sh_addr isn't set!
     */
    char *modinfo = (char *)info->hdr + modinfosec->sh_offset;

    if (prev)
    {
        size -= prev - modinfo;
        modinfo = next_string(prev, &size);
    }

    for (p = modinfo; p; p = next_string(p, &size))
    {
        if (strncmp(p, tag, taglen) == 0 && p[taglen] == '=')
            return p + taglen + 1;
    }

    return NULL;
}
/* 获取elf文件.modinfo sec内,指定的tag对应的value */
static char *get_modinfo_in_elfsec(const struct mod_load_info *info, const char *tag)
{
    return get_next_modinfo_in_elfsec(info, tag, NULL);
}

/* 仅对设置了setup方法的attr进行调用,例如MODINFO_ATTR(x)定义的attr,同时需要保证module结构内含有该成员
 */
static void elfmodule_setup_modinfo(struct module *mod, struct mod_load_info *info)
{
    struct module_attribute *attr;
    int i;

    for (i = 0; (attr = modinfo_attrs[i]); i++)
    {
        if (attr->setup)
            attr->setup(mod, get_modinfo_in_elfsec(info, attr->attr.name));
    }
}

static void elfmodule_free_modinfo(struct module *mod)
{
    struct module_attribute *attr;
    int i;

    for (i = 0; (attr = modinfo_attrs[i]); i++)
    {
        if (attr->free)
            attr->free(mod);
    }
}

#ifdef CONFIG_KALLSYMS
static void elfmodule_calcucalte_layout_symtab(struct module *mod, struct mod_load_info *info)
{
    // 待实现
    /* 该函数作用
       启用了CONFIG_KALLSYMS,os会保留所有的符号,
       对于该内核模块的符号,这些符号都在symtab
       section里,而该section没有alloc标志,需要额外函数将其搬移 */
}

static void elfmodule_add_kallsyms(struct module *mod, const struct mod_load_info *info)
{
    // 待实现
}

#else
static void elfmodule_calcucalte_layout_symtab(struct module *mod, struct mod_load_info *info)
{
    // 空
}

static void elfmodule_add_kallsyms(struct module *mod, const struct mod_load_info *info)
{
    // 空
}
#endif /* CONFIG_KALLSYMS */

int __weak elfmodule_frob_arch_sections(Elf_Ehdr *hdr, Elf_Shdr *sechdrs, char *secstrings,
                                        struct module *mod)
{
    return 0;
}

void *elfmodule_alloc(unsigned long size)
{
    // 待实现
    void *ptr;
    ptr = kernelmodule_malloc(size);
    return ptr;
}

static int elfmodule_check_modinfo(struct module *mod, struct mod_load_info *info, int flags)
{
    // 待实现 检查modinfo段里的各个属性
    return 0;
}

#ifdef CONFIG_MODULE_SIG
static int elfmodule_sig_check(struct mod_load_info *info, int flags)
{
    // 待实现
}
#else  /* !CONFIG_MODULE_SIG */
static int elfmodule_sig_check(struct mod_load_info *info, int flags)
{
    return 0;
}
#endif /* !CONFIG_MODULE_SIG */

/* 有效性检查 核对 二进制,架构,elf版本 是否有效 */
static int elfmodule_header_check(struct mod_load_info *info)
{
    if (info->len < sizeof(*(info->hdr)))
        return -ENOEXEC;

    if (memcmp(info->hdr->e_ident, ELFMAG, SELFMAG) != 0 || info->hdr->e_type != ET_REL ||
        !elf_check_arch(info->hdr) || info->hdr->e_shentsize != sizeof(Elf_Shdr))
        return -ENOEXEC;

    if (info->hdr->e_shoff >= info->len ||
        (info->hdr->e_shnum * sizeof(Elf_Shdr) > info->len - info->hdr->e_shoff))
        return -ENOEXEC;

    return 0;
}

static void free_copy_hdr(struct mod_load_info *info)
{
    kfree(info->hdr);
}

static int rewrite_loadinfo_shdr_entry(struct mod_load_info *info)
{
    unsigned int i;

    // 跳过section 0
    for (i = 1; i < info->hdr->e_shnum; i++)
    {
        Elf_Shdr *shdr = &info->sechdrs[i];
        if (shdr->sh_type != SHT_NOBITS && (info->len < shdr->sh_offset + shdr->sh_size))
        {
            KLOG_E("ELF_Module len %lu truncated\n", info->len);
            return -ENOEXEC;
        }

        /* sh_addr指向临时的elf文件在内存的映像 */
        /* Mark all sections sh_addr with their address in the temporary image.
         */
        shdr->sh_addr = (size_t)info->hdr + shdr->sh_offset;

#ifndef CONFIG_MODULE_UNLOAD
        /* 不支持卸载功能,则不加载   .exit 段 */
        if (elfmodule_prefix_exit_section(info->secstrings + shdr->sh_name))
            shdr->sh_flags &= ~(unsigned long)SHF_ALLOC;
#endif
    }

    /* Track but don't keep modinfo and version sections. */
    info->sechdrs[info->index.vers].sh_flags &= ~(unsigned long)SHF_ALLOC;
    info->sechdrs[info->index.info].sh_flags &= ~(unsigned long)SHF_ALLOC;

    return 0;
}

/*
 * Set up our basic convenience variables (pointers to section headers,
 * search for module section index etc), and do some basic section
 * verification.
 *
 * 该函数先将info->mod设为临时内存里的值,在别的函数里最后分配真正的mod后,再更改
 * Set info->mod to the temporary copy of the module in info->hdr. The final one
 * will be allocated in move_module().
 */
static int firstly_setup_loadinfo(struct mod_load_info *info, int flags)
{
    unsigned int i;

    /* 先将sechdr,secstrings指向临时内存里的部分 */
    info->sechdrs = (void *)info->hdr + info->hdr->e_shoff;
    info->secstrings = (void *)info->hdr + info->sechdrs[info->hdr->e_shstrndx].sh_offset;

    /* 找到模块名字 */
    info->index.info = elfmodule_find_sec(info, ".modinfo");
    if (info->index.info)
        info->name = get_modinfo_in_elfsec(info, "name");

    /* 找到模块自己的符号表,字符串表 */
    for (i = 1; i < info->hdr->e_shnum; i++)
    {
        if (info->sechdrs[i].sh_type == SHT_SYMTAB)
        {
            info->index.sym = i;
            info->index.str = info->sechdrs[i].sh_link;
            info->strtab = (char *)info->hdr + info->sechdrs[info->index.str].sh_offset;
            break;
        }
    }
    if (info->index.sym == 0)
    {
        KLOG_E("%s: module has no symbols(or stripped?)",
               info->name ? info->name : "(missing .modinfo section or name field)");
        return -ENOEXEC;
    }

    info->index.mod = elfmodule_find_sec(info, ".gnu.linkonce.this_module");
    if (!info->index.mod)
    {
        KLOG_E("%s: No module found in elf",
               info->name ? info->name
                          : "(missing .gnu.linkonce.this_module section or name field)");
        return -ENOEXEC;
    }
    /* mod指向临时的struct module镜像所在的内存 */
    /* This is temporary: point mod into copy of data. */
    info->mod = (void *)info->hdr + info->sechdrs[info->index.mod].sh_offset;

    /*
     * If we didn't load the .modinfo 'name' field earlier, fall back to
     * on-disk struct mod 'name' field.
     */
    if (!info->name)
        info->name = info->mod->name;

    if (flags & MODULE_INIT_IGNORE_MODVERSIONS)
        info->index.vers = 0; /* Pretend no __versions section! */
    else
        info->index.vers = elfmodule_find_sec(info, "__versions");

    info->index.pcpu = elfmodule_find_pcpusec(info);

    return 0;
}

#ifdef CONFIG_MODVERSIONS
static int elfmodule_check_version(const struct mod_load_info *info, const char *symname,
                                   struct module *mod, const s32 *crc)
{
    Elf_Shdr *sechdrs = info->sechdrs;
    unsigned int versindex = info->index.vers;
    unsigned int i, num_versions;
    struct modversion_info *versions;

    /* Exporting module didn't supply crcs?  OK, we're already tainted. */
    if (!crc)
        return 1;

    /* No versions at all?  modprobe --force does this. */
    if (versindex == 0)
        return try_to_force_load(mod, symname) == 0;

    versions = (void *)sechdrs[versindex].sh_addr;
    num_versions = sechdrs[versindex].sh_size / sizeof(struct modversion_info);

    for (i = 0; i < num_versions; i++)
    {
        u32 crcval;

        if (strcmp(versions[i].name, symname) != 0)
            continue;

        if (IS_ENABLED(CONFIG_MODULE_REL_CRCS))
            crcval = resolve_rel_crc(crc);
        else
            crcval = *crc;
        if (versions[i].crc == crcval)
            return 1;
        pr_debug("Found checksum %X vs module %lX\n", crcval, versions[i].crc);
        goto bad_version;
    }

    /* Broken toolchain. Warn once, then let it go.. */
    pr_warn_once("%s: no symbol version for %s\n", info->name, symname);
    return 1;

bad_version:
    pr_warn("%s: disagrees about version of symbol %s\n", info->name, symname);
    return 0;
}

static inline int elfmodule_check_structmodule_version(const struct mod_load_info *info,
                                                       struct module *mod)
{
    const s32 *crc;

    /*
     * Since this should be found in kernel (which can't be removed), no
     * locking is necessary -- use preempt_disable() to placate lockdep.
     */
    preempt_disable();
    if (find_symbol("module_layout", NULL, &crc, NULL, true, false))
    {
        preempt_enable();
        BUG();
    }
    preempt_enable();
    return elfmodule_check_version(info, "module_layout", mod, crc);
}

#else
static int elfmodule_check_version(const struct mod_load_info *info, const char *symname,
                                   struct module *mod, const s32 *crc)
{
    return 1;
}
static inline int elfmodule_check_structmodule_version(const struct mod_load_info *info,
                                                       struct module *mod)
{
    return 1;
}

#endif

void __weak elfmodule_memfree(void *module_region)
{
    kfree(module_region);
}

void __weak elfmodule_arch_cleanup(struct module *mod) {}

void __weak elfmodule_arch_freeing_init(struct module *mod) {}

/* module is no longer valid after this! */
static void elfmodule_deallocate(struct module *mod, struct mod_load_info *info)
{
    elfmodule_percpu_free(mod);
    elfmodule_arch_freeing_init(mod);
    elfmodule_memfree(mod->init_layout.base);
    elfmodule_memfree(mod->core_layout.base);
}

/* Lay out the SHF_ALLOC sections in a way not dissimilar to how ld
might -- code, read-only data, read-write data, small data.  Tally
sizes, and place the offsets into sh_entsize fields: high bit means it
belongs in init. */
static void elfmodule_calcucalte_layout_sections(struct module *mod, struct mod_load_info *info)
{
    static unsigned long const masks[][2] = {
        //  必须按照这个顺序
        {SHF_EXECINSTR | SHF_ALLOC, 0}, //  AX
        {SHF_ALLOC, SHF_WRITE | 0},     //  A且!W
        {SHF_WRITE | SHF_ALLOC, 0},     //  AW
        {0 | SHF_ALLOC, 0}              //  A
    };
    unsigned int m, i;

    /* entsize用来记录 module基址到该sec内存(module运行时分配给该sec的)的偏移量
     */
    for (i = 0; i < info->hdr->e_shnum; i++)
        info->sechdrs[i].sh_entsize = ~0UL; //~0UL用来做未经统计的flag值

    KLOG_D("Core section allocation order:");
    for (m = 0; m < ARRAY_SIZE(masks); ++m)
    {
        for (i = 0; i < info->hdr->e_shnum; ++i)
        {
            Elf_Shdr *sec = &info->sechdrs[i];
            const char *secname = info->secstrings + sec->sh_name;

            if ((sec->sh_flags & masks[m][0]) != masks[m][0] || (sec->sh_flags & masks[m][1]) ||
                sec->sh_entsize != ~0UL || elfmodule_prefix_init_section(secname))
                continue;

            sec->sh_entsize = update_size_get_offset(mod, &mod->core_layout.size, sec, i);
            KLOG_D("\t%s", secname);
        }

        switch (m)
        {
        case 0: /* executable */
            mod->core_layout.size = rwx_config_align(mod->core_layout.size);
            mod->core_layout.text_size = mod->core_layout.size;
            break;
        case 1: /* RO: text and ro-data */
            mod->core_layout.size = rwx_config_align(mod->core_layout.size);
            mod->core_layout.ro_size = mod->core_layout.size;
            break;
        case 3: /* whole core */
            mod->core_layout.size = rwx_config_align(mod->core_layout.size);
            break;
        }
    }

    KLOG_D("Init section allocation order:");
    for (m = 0; m < ARRAY_SIZE(masks); ++m)
    {
        for (i = 0; i < info->hdr->e_shnum; ++i)
        {
            Elf_Shdr *sec = &info->sechdrs[i];
            const char *secname = info->secstrings + sec->sh_name;

            if ((sec->sh_flags & masks[m][0]) != masks[m][0] || (sec->sh_flags & masks[m][1]) ||
                sec->sh_entsize != ~0UL || !elfmodule_prefix_init_section(secname))
                continue;

            /* 更新偏移量sh_entsize, 更新总大小init_layout.size
               设置INIT_OFFSET_MASK标记,代表属于init部分 */
            sec->sh_entsize =
                (update_size_get_offset(mod, &mod->init_layout.size, sec, i) | INIT_OFFSET_MASK);
            KLOG_D("\t%s", secname);
        }

        switch (m)
        {
        case 0: /* executable */
            mod->init_layout.size = rwx_config_align(mod->init_layout.size);
            mod->init_layout.text_size = mod->init_layout.size;
            break;
        case 1: /* RO: text and ro-data */
            mod->init_layout.size = rwx_config_align(mod->init_layout.size);
            mod->init_layout.ro_size = mod->init_layout.size;
            break;
        case 3: /* whole init */
            mod->init_layout.size = rwx_config_align(mod->init_layout.size);
            break;
        }
    }
}

static int elfmodule_find_specialsec_setup_mod(struct module *mod, struct mod_load_info *info)
{
    /* 找到__param段 记录在成员kp里 */
    // mod->kp = elfmodule_section_objs (info, "__param", sizeof (*mod->kp),
    //                                   &mod->num_kp);
    /* 模块导出的符号表 */
    mod->syms = elfmodule_section_objs(info, "__ksymtab", sizeof(*mod->syms), &mod->num_syms);
    mod->crcs = elfmodule_section_addr(info, "__kcrctab");
    mod->gpl_syms =
        elfmodule_section_objs(info, "__ksymtab_gpl", sizeof(*mod->gpl_syms), &mod->num_gpl_syms);
    mod->gpl_crcs = elfmodule_section_addr(info, "__kcrctab_gpl");
    mod->gpl_future_syms = elfmodule_section_objs(
        info, "__ksymtab_gpl_future", sizeof(*mod->gpl_future_syms), &mod->num_gpl_future_syms);
    mod->gpl_future_crcs = elfmodule_section_addr(info, "__kcrctab_gpl_future");

#ifdef CONFIG_UNUSED_SYMBOLS
    mod->unused_syms = elfmodule_section_objs(info, "__ksymtab_unused", sizeof(*mod->unused_syms),
                                              &mod->num_unused_syms);
    mod->unused_crcs = elfmodule_section_addr(info, "__kcrctab_unused");
    mod->unused_gpl_syms = elfmodule_section_objs(
        info, "__ksymtab_unused_gpl", sizeof(*mod->unused_gpl_syms), &mod->num_unused_gpl_syms);
    mod->unused_gpl_crcs = elfmodule_section_addr(info, "__kcrctab_unused_gpl");
#endif

#ifdef CONFIG_CONSTRUCTORS
    mod->ctors = elfmodule_section_objs(info, ".ctors", sizeof(*mod->ctors), &mod->num_ctors);

    if (!mod->ctors)
        mod->ctors =
            elfmodule_section_objs(info, ".init_array", sizeof(*mod->ctors), &mod->num_ctors);
    else if (elfmodule_find_sec(info, ".init_array"))
    {
        /*
         * This shouldn't happen with same compiler and binutils
         * building all parts of the module.
         */
        KLOG_E("%s: has both .ctors and .init_array.\n", mod->name);
        return -EINVAL;
    }
#endif

    mod->noinstr_text_start =
        elfmodule_section_objs(info, ".noinstr.text", 1, &mod->noinstr_text_size);

#ifdef CONFIG_TREE_SRCU
    mod->srcu_struct_ptrs = elfmodule_section_objs(
        info, "___srcu_struct_ptrs", sizeof(*mod->srcu_struct_ptrs), &mod->num_srcu_structs);
#endif

#ifdef CONFIG_JUMP_LABEL
    mod->jump_entries = elfmodule_section_objs(info, "__jump_table", sizeof(*mod->jump_entries),
                                               &mod->num_jump_entries);
#endif

#ifdef CONFIG_HAVE_STATIC_CALL_INLINE
    mod->static_call_sites = elfmodule_section_objs(
        info, ".static_call_sites", sizeof(*mod->static_call_sites), &mod->num_static_call_sites);
#endif
    // mod->extable = elfmodule_section_objs (
    //     info, "__ex_table", sizeof (*mod->extable), &mod->num_exentries);

    if (elfmodule_section_addr(info, "__obsparm"))
        KLOG_E("%s: Ignoring obsolete parameters\n", mod->name);

    return 0;
}

static int elfmodule_alloc_and_move_mod(struct module *mod, struct mod_load_info *info)
{
    int i;
    void *ptr;

    /* core部分 */
    /* Do the allocs. */
    ptr = elfmodule_alloc(mod->core_layout.size);
    /*
     * The pointer to this block is stored in the module structure
     * which is inside the block. Just mark it as not being a
     * leak.
     */
    // kmemleak_not_leak(ptr);
    // if (!ptr)
    //   return -ENOMEM;
    memset(ptr, 0, mod->core_layout.size);
    mod->core_layout.base = ptr;

    /* init部分 */
    if (mod->init_layout.size)
    {
        ptr = elfmodule_alloc(mod->init_layout.size);
        /*
         * The pointer to this block is stored in the module structure
         * which is inside the block. This block doesn't need to be
         * scanned as it contains data and code that will be freed
         * after the module is initialized.
         */
        // kmemleak_ignore(ptr);
        if (!ptr)
        {
            elfmodule_memfree(mod->core_layout.base);
            return -ENOMEM;
        }
        memset(ptr, 0, mod->init_layout.size);
        mod->init_layout.base = ptr;
    }
    else
        mod->init_layout.base = NULL;

    /* Transfer each section which specifies SHF_ALLOC */
    KLOG_D("final section addresses:");
    for (i = 0; i < info->hdr->e_shnum; i++)
    {
        void *dest;
        Elf_Shdr *shdr = &info->sechdrs[i];

        if (!(shdr->sh_flags & SHF_ALLOC))
            continue;

        if (shdr->sh_entsize & INIT_OFFSET_MASK)
            dest = mod->init_layout.base + (shdr->sh_entsize & ~INIT_OFFSET_MASK);
        else
            dest = mod->core_layout.base + shdr->sh_entsize;

        if (shdr->sh_type != SHT_NOBITS)
            memcpy(dest, (void *)shdr->sh_addr, shdr->sh_size);

        /* Update sh_addr to point to copy in image. */
        shdr->sh_addr = (unsigned long)dest;

        KLOG_D("\t0x%lx %s", (long)shdr->sh_addr, info->secstrings + shdr->sh_name);
    }

    return 0;
}

static struct module *elfmodule_layout_and_allocate(struct mod_load_info *info, int flags)
{
    struct module *mod;
    unsigned int ndx;
    int err;

    err = elfmodule_check_modinfo(info->mod, info, flags);
    if (err)
        return ERR_PTR(err);

    /* Allow arches to frob section contents and sizes.  */
    err = elfmodule_frob_arch_sections(info->hdr, info->sechdrs, info->secstrings, info->mod);
    if (err < 0)
        return ERR_PTR(err);

    /* We will do a special allocation for per-cpu sections later. */
    info->sechdrs[info->index.pcpu].sh_flags &= ~(unsigned long)SHF_ALLOC;

    /*
     * Mark ro_after_init section with SHF_RO_AFTER_INIT so that
     * layout_sections() can put it in the right place.
     * Note: ro_after_init sections also have SHF_{WRITE,ALLOC} set.
     */
    ndx = elfmodule_find_sec(info, ".data..ro_after_init");
    if (ndx)
        info->sechdrs[ndx].sh_flags |= SHF_RO_AFTER_INIT;

    /*
     * Mark the __jump_table section as ro_after_init as well: these data
     * structures are never modified, with the exception of entries that
     * refer to code in the __init section, which are annotated as such
     * at module load time.
     */
    ndx = elfmodule_find_sec(info, "__jump_table");
    if (ndx)
        info->sechdrs[ndx].sh_flags |= SHF_RO_AFTER_INIT;

    /* Determine total sizes, and put offsets in sh_entsize.  For now
    this is done generically; there doesn't appear to be any
    special cases for the architectures. */
    elfmodule_calcucalte_layout_sections(info->mod, info);
    elfmodule_calcucalte_layout_symtab(info->mod, info);

    /* Allocate and move to the final place */
    err = elfmodule_alloc_and_move_mod(info->mod, info);
    if (err)
        return ERR_PTR(err);

    /* 更新mod指向模块真正运行时的struct module地址 */
    /* Module has been copied to its final place now: return it. */
    mod = (void *)info->sechdrs[info->index.mod].sh_addr;

    // kmemleak_load_module(mod, info);
    return mod;
}

/* Free a module, remove from lists, etc. */
static void free_module(struct module *mod)
{
    /* We leave it in list to prevent duplicate loads, but make sure
     * that noone uses it while it's being deconstructed. */
    mutex_lock(&elfmodule_mutex);
    mod->state = MODULE_STATE_UNFORMED;
    mutex_unlock(&elfmodule_mutex);

    /* Arch-specific cleanup. */
    elfmodule_arch_cleanup(mod);

    /* Module unload stuff */
    elfmodule_unload_free(mod);

    /* Free any allocated parameters. */
    // destroy_params(mod->kp, mod->num_kp);

    // if (is_livepatch_module(mod))
    //   free_module_elf(mod);

    /* Now we can delete it from the lists */
    mutex_lock(&elfmodule_mutex);
    /* Unlink carefully: kallsyms could be walking list. */
    list_del(&mod->list);
    // mod_tree_remove(mod);
    mutex_unlock(&elfmodule_mutex);

    /* This may be empty, but that's OK */
    elfmodule_arch_freeing_init(mod);

    elfmodule_memfree(mod->init_layout.base);

    kfree(mod->args);

    elfmodule_percpu_free(mod);

    /* Finally, free the core (containing the module structure) */
    elfmodule_memfree(mod->core_layout.base);
}

/*
 * We try to place it in the list now to make sure it's unique before
 * we dedicate too many resources.  In particular, temporary percpu
 * memory exhaustion.
 */
static int elfmodule_add_unformed_mod(struct module *mod)
{
    int err;
    struct module *old;

    mod->state = MODULE_STATE_UNFORMED;

again:
    mutex_lock(&elfmodule_mutex);
    old = find_all_elfmodule(mod->name, strlen(mod->name), true);
    if (old != NULL)
    {
        if (old->state != MODULE_STATE_LIVE)
        {
            /* Wait in case it fails to load. */
            mutex_unlock(&elfmodule_mutex);
            /* 等待其加载完,然后again */
            // err = wait_event_interruptible(module_wq,
            // finished_loading(mod->name)); if (err)
            //   goto out_unlocked;

            goto again;
        }
        err = -EEXIST;
        goto out;
    }

    list_add(&mod->list, &elfmodules_list);
    // mod_tree_insert(mod);
    err = 0;

out:
    mutex_unlock(&elfmodule_mutex);
out_unlocked:
    return err;
}

/* Change all symbols so that st_value encodes the pointer directly. */
static int elfmodule_fixup_symbols(struct module *mod, const struct mod_load_info *info)
{
    Elf_Shdr *symsec = &info->sechdrs[info->index.sym];
    Elf_Sym *sym = (void *)symsec->sh_addr;
    unsigned long secbase;
    unsigned int i;
    int ret = 0;
    const struct symtab_item *ksymbol;

    for (i = 1; i < symsec->sh_size / sizeof(Elf_Sym); i++)
    {
        const char *name = info->strtab + sym[i].st_name;

        switch (sym[i].st_shndx)
        {
        case SHN_COMMON:
            /* Ignore common symbols */
            if (!strncmp(name, "__gnu_lto", 9))
                break;

            /* We compiled with -fno-common.  These are not supposed to happen.
             */
            KLOG_D("Common symbol: %s", name);
            KLOG_D("%s: please compile with -fno-common", mod->name);
            ret = -ENOEXEC;
            break;

        case SHN_ABS:
            /* Don't need to do anything */
            KLOG_D("Absolute symbol: 0x%08lx", (long)sym[i].st_value);
            break;

        case SHN_LIVEPATCH:
            /* Livepatch symbols are resolved by livepatch */
            break;

        case SHN_UNDEF:
            // ksym = resolve_symbol_wait(mod, info, name);
            ksymbol = symtab_findbyname(g_allsyms, name, g_nallsyms);
            /* Ok if resolved.  */
            if (ksymbol && !IS_ERR(ksymbol))
            {
                sym[i].st_value = (Elf_Addr)(ksymbol->sym_value);

                break;
            }

            /* Ok if weak.  */
            if (!ksymbol && ELF_ST_BIND(sym[i].st_info) == STB_WEAK)
                break;

            ret = PTR_ERR(ksymbol) ?: -ENOENT;
            KLOG_D("%s: Unknown symbol %s (err %d)", mod->name, name, ret);
            break;

        default:
            /* Divert to percpu allocation if a percpu var. */
            if (sym[i].st_shndx == info->index.pcpu)
                secbase = (unsigned long)elfmodule_percpu(mod);
            /* 普通的sec, st_value保存的偏移量 */
            else
                secbase = info->sechdrs[sym[i].st_shndx].sh_addr;

            /* 符号真正的虚拟地址 */
            sym[i].st_value += secbase;
            break;
        }
    }

    return ret;
}

static int elfmodule_apply_relocations(struct module *mod, const struct mod_load_info *info)
{
    unsigned int i;
    int err = 0;

    /* Now do relocations. */
    for (i = 1; i < info->hdr->e_shnum; i++)
    {
        unsigned int infosec = info->sechdrs[i].sh_info;

        /* Not a valid relocation section? */
        if (infosec >= info->hdr->e_shnum)
            continue;

        /* Don't bother with non-allocated sections */
        if (!(info->sechdrs[infosec].sh_flags & SHF_ALLOC))
            continue;

        if (info->sechdrs[i].sh_flags & SHF_RELA_LIVEPATCH)
            err = klp_apply_section_relocs(mod, info->sechdrs, info->secstrings, info->strtab,
                                           info->index.sym, i, NULL);
        else if (info->sechdrs[i].sh_type == SHT_REL)
            err = apply_relocate(info->sechdrs, info->strtab, info->index.sym, i, mod);
        else if (info->sechdrs[i].sh_type == SHT_RELA)
            err = apply_relocate_add(info->sechdrs, info->strtab, info->index.sym, i, mod);

        if (err < 0)
            break;
    }
    return err;
}

int __weak elfmodule_finalize(const Elf_Ehdr *hdr, const Elf_Shdr *sechdrs, struct module *me)
{
    return 0;
}

static int elfmodule_after_relocation(struct module *mod, const struct mod_load_info *info)
{
    /* Sort exception table now relocations are done. */
    // sort_extable(mod->extable, mod->extable + mod->num_exentries);

    /* Copy relocated percpu area over. */
    elfmodule_percpu_copy(mod, (void *)info->sechdrs[info->index.pcpu].sh_addr,
                          info->sechdrs[info->index.pcpu].sh_size);

    /* Setup kallsyms-specific fields. */
    elfmodule_add_kallsyms(mod, info);

    /* Arch-specific module finalizing. */
    return elfmodule_finalize(info->hdr, info->sechdrs, mod);
}

/**
 * elfmodule_strndup_user - duplicate an existing string from user space
 * @s: The string to duplicate
 * @n: Maximum number of bytes to copy, including the trailing NUL.
 *
 * Return: newly allocated copy of @s or an ERR_PTR() in case of error
 */
char *elfmodule_strndup_user(const char *s, long n)
{
    return strndup(s, n);
}

static int elfmodule_complete_formation(struct module *mod, struct mod_load_info *info)
{
    int err;

    mutex_lock(&elfmodule_mutex);

    /* 检查该模块导出的符号,是否在其他模块里也有定义 */
    /* Find duplicate symbols (must be called under lock). */
    // err = verify_exported_symbols(mod);
    // if (err < 0)
    //   goto out;

    /* This relies on module_mutex for list integrity. */
    // module_bug_finalize(info->hdr, info->sechdrs, mod);

    elfmodule_enable_ro(mod, false);
    elfmodule_enable_nx(mod);
    elfmodule_enable_x(mod);

    /* Mark state as coming so strong_try_module_get() ignores us,
     * but kallsyms etc. can see us. */
    mod->state = MODULE_STATE_COMING;
    mutex_unlock(&elfmodule_mutex);

    return 0;

out:
    mutex_unlock(&elfmodule_mutex);
    return err;
}

/* Call module constructors. */
static void elfmodule_exec_ctors(struct module *mod)
{
#ifdef CONFIG_CONSTRUCTORS
    unsigned long i;

    for (i = 0; i < mod->num_ctors; i++)
        mod->ctors[i]();
#endif
}

/*
 * This is where the real work happens.
 *
 * Keep it uninlined to provide a reliable breakpoint target, e.g. for the gdb
 * helper command 'lx-symbols'.
 */
static int do_init_module(struct module *mod)
{
    int ret = 0;
    /* 添加到free init_part的工作队列 */
    // struct mod_initfree *freeinit;
    // freeinit = kmalloc(sizeof(*freeinit));
    // if (!freeinit)
    //{
    //   ret = -ENOMEM;
    //   goto fail;
    // }
    // freeinit->module_init = mod->init_layout.base;

    elfmodule_exec_ctors(mod);

    /* Start the module */
    if (mod->init != NULL)
        ret = mod->init(); // do_one_initcall(mod->init);
    if (ret < 0)
    {
        goto fail_free_freeinit;
    }
    if (ret > 0)
    {
        KLOG_E("%s: '%s'->init suspiciously returned %d, it should follow 0/-E convention\n"
               "%s: loading module anyway...\n",
               __func__, mod->name, ret, __func__);
    }

    /* Now it's a first class citizen! */
    mod->state = MODULE_STATE_LIVE;
    // blocking_notifier_call_chain(&module_notify_list, MODULE_STATE_LIVE, mod);

    /*
     * We need to finish all async code before the module init sequence
     * is done.  This has potential to deadlock.  For example, a newly
     * detected block device can trigger request_module() of the
     * default iosched from async probing task.  Once userland helper
     * reaches here, async_synchronize_full() will wait on the async
     * task waiting on request_module() and deadlock.
     *
     * This deadlock is avoided by perfomring async_synchronize_full()
     * iff module init queued any async jobs.	This isn't a full
     * solution as it will deadlock the same if module loading from
     * async jobs nests more than once; however, due to the various
     * constraints, this hack seems to be the best option for now.
     * Please refer to the following thread for details.
     *
     */
    // if (!mod->async_probe_requested && (current->flags & PF_USED_ASYNC))
    //   async_synchronize_full();

    mutex_lock(&elfmodule_mutex);
    /* module_unload_init,会额外+1,到initial完毕后,在这里-1 */
    /* Drop initial reference. */
    module_put(mod);
    // trim_init_extable(mod);
#ifdef CONFIG_KALLSYMS
    /* Switch to core kallsyms now init is done: kallsyms may be walking! */
    rcu_assign_pointer(mod->kallsyms, &mod->core_kallsyms);
#endif
    // module_enable_ro(mod, true);
    // mod_tree_remove_init(mod);
    elfmodule_arch_freeing_init(mod);
    // mod->init_layout.base = NULL;
    // mod->init_layout.size = 0;
    // mod->init_layout.ro_size = 0;
    // mod->init_layout.ro_after_init_size = 0;
    // mod->init_layout.text_size = 0;
    /*
     * We want to free module_init, but be aware that kallsyms may be
     * walking this with preempt disabled.  In all the failure paths, we
     * call synchronize_rcu(), but we don't want to slow down the success
     * path. module_memfree() cannot be called in an interrupt, so do the
     * work and call synchronize_rcu() in a work queue.
     *
     * Note that module_alloc() on most architectures creates W+X page
     * mappings which won't be cleaned up until do_free_init() runs.  Any
     * code such as mark_rodata_ro() which depends on those mappings to
     * be cleaned up needs to sync with the queued work - ie
     * rcu_barrier()
     */
    // if (llist_add(&freeinit->node, &init_free_list))
    //   schedule_work(&init_free_wq);

    mutex_unlock(&elfmodule_mutex);
    // wake_up_all(&module_wq);

    return 0;

fail_free_freeinit:
    // kfree(freeinit);
fail:
    /* Try to protect us from buggy refcounters. */
    mod->state = MODULE_STATE_GOING;
    // synchronize_rcu();
    module_put(mod);
    // blocking_notifier_call_chain(&module_notify_list, MODULE_STATE_GOING, mod);
    // klp_module_going(mod);
    // ftrace_release_mod(mod);
    free_module(mod);
    // wake_up_all(&module_wq);
    return ret;
}

/* Allocate and load the module: note that size of section 0 is always
   zero, and we rely on this for optional sections. */
static int load_module(struct mod_load_info *info, const char *uargs, int flags)
{
    struct module *mod;
    long err = 0;
    char *after_dashes;

    err = elfmodule_header_check(info);
    if (err)
    {
        KLOG_E("Module has invalid ELF header");
        goto free_copy;
    }

    err = firstly_setup_loadinfo(info, flags);
    if (err)
        goto free_copy;

    /* 检查模块是否在黑名单列表 */
    // if (blacklisted(info->name)) {
    //   err = -EPERM;
    //   pr_err("Module %s is blacklisted\n", info->name);
    //   goto free_copy;
    // }

    err = elfmodule_sig_check(info, flags);
    if (err)
        goto free_copy;

    err = rewrite_loadinfo_shdr_entry(info);
    if (err)
        goto free_copy;

    /* Check module struct version now, before we try to use module. */
    if (!elfmodule_check_structmodule_version(info, info->mod))
    {
        err = -ENOEXEC;
        goto free_copy;
    }

    /* mod指向真正运行时struct module所在的内存 */
    /* Figure out module layout, and allocate all the memory. */
    mod = elfmodule_layout_and_allocate(info, flags);
    if (IS_ERR(mod))
    {
        err = PTR_ERR(mod);
        goto free_copy;
    }

    // audit_log_kern_module(mod->name);

    /* 将 该内核模块加入 到 内核管理的内核模块链表里, 状态变为unformed */
    /* Reserve our place in the list. */
    err = elfmodule_add_unformed_mod(mod);
    if (err)
        goto free_module;

#ifdef CONFIG_MODULE_SIG
    mod->sig_ok = info->sig_ok;
    if (!mod->sig_ok)
    {
        KLOG_D("%s: module verification failed: signature and/or required key "
               "missing - tainting kernel\n",
               mod->name);
        add_taint_module(mod, TAINT_UNSIGNED_MODULE, LOCKDEP_STILL_OK);
    }
#endif

    /* 对模块里的percpu进行特殊的内存申请 */
    /* To avoid stressing percpu allocator, do this once we're unique. */
    err = elfmodule_percpu_alloc(mod, info);
    if (err)
        goto unlink_mod;

    /* 为模块卸载做相关的准备(初始化unload时需要使用的数据结构) */
    /* Now module is in final location, initialize linked lists, etc. */
    err = elfmodule_unload_init(mod);
    if (err)
        goto unlink_mod;

    /* sysfs相关 */
    init_param_lock(mod);

    /* Now we've got everything in the final locations, we can
     * find optional sections. */
    err = elfmodule_find_specialsec_setup_mod(mod, info);
    if (err)
        goto free_unload;

    // err = check_module_license_and_versions(mod);
    // if (err)
    //   goto free_unload;

    /* 设置module结构内modinfo相关的成员 */
    /* Set up MODINFO_ATTR fields */
    elfmodule_setup_modinfo(mod, info);

    /* Fix up syms, so that st_value is a pointer to location. */
    err = elfmodule_fixup_symbols(mod, info);
    if (err < 0)
        goto free_modinfo;

    err = elfmodule_apply_relocations(mod, info);
    if (err < 0)
        goto free_modinfo;

    err = elfmodule_after_relocation(mod, info);
    if (err < 0)
        goto free_modinfo;

    /* 刷cache */
    // elfmodule_flush_icache(mod);

    /* Now copy in args */
    mod->args = elfmodule_strndup_user(uargs, ~0UL >> 1);
    if (IS_ERR(mod->args))
    {
        err = PTR_ERR(mod->args);
        goto free_arch_cleanup;
    }

    // dynamic_debug_setup(mod, info->debug, info->num_debug);

    /* Ftrace init must be called in the MODULE_STATE_UNFORMED state */
    // ftrace_module_init(mod);

    /*
      检查其导出符号 与 其他模块导出符号 是否同名
      设置权限保护
      执行完后, 从unformed转为comming */
    /* Finally it's fully formed, ready to start executing. */
    err = elfmodule_complete_formation(mod, info);
    if (err)
        goto ddebug_cleanup;

    // err = prepare_coming_module(mod);
    // if (err)
    //   goto bug_cleanup;

    /* linux下的kernel cmdline机制使用 */
    /* Module is ready to execute: parsing args may do that. */
    // after_dashes = parse_args(mod->name, mod->args, mod->kp, mod->num_kp,
    // -32768, 32767, mod, unknown_module_param_cb); if (IS_ERR(after_dashes))
    //{
    //   err = PTR_ERR(after_dashes);
    //   goto coming_cleanup;
    // }
    // else if (after_dashes)
    //{
    //   KLOG_E("%s: parameters '%s' after `--' ignored\n", mod->name,
    //   after_dashes);
    // }

    /* Link in to sysfs. */
    // err = mod_sysfs_setup(mod, info, mod->kp, mod->num_kp);
    // if (err < 0)
    //   goto coming_cleanup;

    // if (is_livepatch_module(mod))
    //{
    //   err = copy_module_elf(mod, info);
    //   if (err < 0)
    //     goto sysfs_cleanup;
    // }

    /* 释放 临时的模块镜像 */
    /* Get rid of temporary copy. */
    free_copy_hdr(info);

    /* Done! */
    // trace_module_load(mod);

    return do_init_module(mod);

/* 能恢复到调用mod_sysfs_setup()之前的状态 */
sysfs_cleanup:
    // mod_sysfs_teardown(mod);

/* 能恢复到调用prepare_coming_module()之前的状态 */
coming_cleanup:
    mod->state = MODULE_STATE_GOING;
    // destroy_params(mod->kp, mod->num_kp);
    // blocking_notifier_call_chain(&module_notify_list, MODULE_STATE_GOING,
    // mod); klp_module_going(mod);

/* 能恢复到调用elfmodule_complete_formation()之前的状态 */
bug_cleanup:
    /* module_bug_cleanup needs module_mutex protection */
    mutex_lock(&elfmodule_mutex);
    // module_bug_cleanup(mod);
    mutex_unlock(&elfmodule_mutex);

ddebug_cleanup:
    // ftrace_release_mod(mod);
    // dynamic_debug_remove(mod, info->debug);
    // synchronize_rcu();
    // kfree(mod->args);

/* 能恢复到调用elfmodule_flush_icache()之前的状态 */
free_arch_cleanup:
    elfmodule_arch_cleanup(mod);

/* 释放 struct module内与modinfo相关的成员 */
free_modinfo:
    elfmodule_free_modinfo(mod);

/* 释放   为了模块unload而初始化的那些数据结构 */
free_unload:
    elfmodule_unload_free(mod);

/* 将mod从管理结构中移除 */
unlink_mod:
    mutex_lock(&elfmodule_mutex);
    /* Unlink carefully: kallsyms could be walking list. */
    list_del(&mod->list);
    // mod_tree_remove(mod);
    // wake_up_all(&module_wq);
    /* Wait for RCU-sched synchronizing before releasing mod->list. */
    // synchronize_rcu();
    mutex_unlock(&elfmodule_mutex);

/* 释放 分配给模块(实际运行时)的内存 */
free_module:
    /* Free lock-classes; relies on the preceding sync_rcu() */
    // lockdep_free_key_range(mod->core_layout.base, mod->core_layout.size);

    elfmodule_deallocate(mod, info);

free_copy:
    free_copy_hdr(info);
    return err;
}

int TTOS_FD_LoadModule(int fd, const char *uargs, int flags)
{
    struct stat st;
    size_t read_len;
    struct mod_load_info info = {};
    void *hdr = NULL;
    int err;

    KLOG_D("finit_module: fd=%d, uargs=%p, flags=%i", fd, uargs, flags);

    /*
    if (flags & ~(MODULE_INIT_IGNORE_MODVERSIONS
              |MODULE_INIT_IGNORE_VERMAGIC))
        return -EINVAL;
    */

    /* 获取文件长度，分配buf，按照完整长度读内容到buf，赋值给hdr */
    err = fstat(fd, &st);
    if (err < 0)
    {
        KLOG_E("fstat error!");
        return err;
    }

    hdr = malloc(st.st_size);
    if (!hdr)
    {
        KLOG_E("not enough memory!");
        return -ENOMEM;
    }

    /* 从fd中读取段信息到内存中 */
    lseek(fd, 0, SEEK_SET);
    read_len = read(fd, hdr, st.st_size);
    if (read_len != st.st_size)
    {
        KLOG_E("file size error!");
        return -ENOEXEC;
    }

    info.hdr = hdr;
    info.len = read_len;

    return load_module(&info, uargs, flags);
}

/* Sets info->hdr and info->len. */
static int copy_ModuleImage_from_user(const void __user *umod, unsigned long len,
                                      struct mod_load_info *info)
{
    info->len = len;
    if (info->len < sizeof(*(info->hdr)))
        return -ENOEXEC;

    /* Suck in entire file: we'll want most of it. */
    info->hdr = malloc(info->len);
    if (!info->hdr)
        return -ENOMEM;

    memcpy(info->hdr, umod, info->len);

    return 0;
}

int TTOS_ModImage_LoadModule(void *umod, unsigned long len, const char *uargs)
{
    int err;
    struct mod_load_info info = {};

    KLOG_D("init_module: umod=%p, len=%lu, uargs=%p", umod, len, uargs);

    err = copy_ModuleImage_from_user(umod, len, &info);
    if (err)
        return err;

    return load_module(&info, uargs, 0);
}

#ifdef CONFIG_MODULE_UNLOAD

int TTOS_DeleteModule(const char __user *name_user, unsigned int flags)
{
    struct module *mod;
    char name[MODULE_NAME_LEN];
    int ret, forced = 0;

    if (strncpy(name, name_user, MODULE_NAME_LEN - 1) < 0)
        return -EFAULT;
    name[MODULE_NAME_LEN - 1] = '\0';

    if (mutex_lock(&elfmodule_mutex) != 0)
        return -EINTR;

    mod = find_module(name);
    if (!mod)
    {
        ret = -ENOENT;
        goto out;
    }

    if (!list_empty(&mod->source_list))
    {
        /* Other modules depend on us: get rid of them first. */
        ret = -EWOULDBLOCK;
        goto out;
    }

    /* find_module不会找unformed状态,所以只能是init or dying */
    /* Doing init or already dying? */
    if (mod->state != MODULE_STATE_LIVE)
    {
        /* FIXME: if (force), slam module count damn the torpedoes */
        KLOG_E("%s is initializing or dying, state %d\n", mod->name, mod->state);
        ret = -EBUSY;
        goto out;
    }

    /* If it has an init func, it must have an exit func to unload */
    if (mod->init && !mod->exit)
    {
        forced = try_force_unload_elfmodule(flags);
        if (!forced)
        {
            /* This module can't be removed */
            ret = -EBUSY;
            goto out;
        }
    }

    /* Stop the machine so refcounts can't move and disable module. */
    ret = try_stop_elfmodule(mod, flags, &forced);
    if (ret != 0)
        goto out;

    mutex_unlock(&elfmodule_mutex);

    /* Final destruction now no one is using it. */
    if (mod->exit != NULL)
        mod->exit();

    // blocking_notifier_call_chain(&module_notify_list, MODULE_STATE_GOING, mod);

    /* Store the name of the last unloaded module for diagnostic purposes */
    // strlcpy(last_unloaded_module, mod->name, sizeof(last_unloaded_module));

    free_module(mod);
    /* someone could wait for the module in add_unformed_module() */
    // wake_up_all(&module_wq);
    return 0;
out:
    mutex_unlock(&elfmodule_mutex);
    return ret;
}

#else

int TTOS_DeleteModule(const char __user *name_user, unsigned int flags)
{
    KLOG_E("CONFIG_MODULE_UNLOAD is not defined\n");
}
#endif
