/**
 * @file rt_sigpending.c
 * <AUTHOR> (zhang<PERSON><PERSON>@kyland.com)
 * @brief 获取待处理信号集
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <process_signal.h>
#include <ttos.h>
#include <uaccess.h>
#include <ttosProcess.h>
#include <errno.h>

void process_thread_signal_pending(pcb_t pcb, process_sigset_t *pending);

/**
 * @brief 系统调用实现：获取待处理信号集。
 *
 * 该函数实现了一个系统调用，用于获取当前进程中被阻塞的待处理信号集。
 *
 * @param[out] set 用于存储待处理信号集的缓冲区
 * @param[in] sigsetsize 信号集大小
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功。
 * @retval -EINVAL 参数无效。
 * @retval -EFAULT 内存访问错误。
 * @retval -ENOSYS 不支持的操作。
 *
 * @note 1. 支持实时信号。
 *       2. 线程安全。
 *       3. 进程级操作。
 *       4. 不会阻塞。
 */
DEFINE_SYSCALL (rt_sigpending, (sigset_t __user * set, size_t sigsetsize))
{
    if (sizeof(process_sigset_t) < sigsetsize)
    {
        return -EINVAL;
    }

    process_thread_signal_pending(ttosProcessSelf(), (process_sigset_t *)set);

    return 0;
}
