/**
 * @file recvmsg.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 从套接字接收消息
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <errno.h>
#include <net/net.h>
#include <uaccess.h>
/**
 * @brief 系统调用实现：从套接字接收消息。
 *
 * 该函数实现了一个系统调用，用于从套接字接收消息及其控制信息。
 *
 * @param[in] sockfd 套接字文件描述符
 * @param[in,out] msg msghdr结构，包含：
 *                    - msg_name：发送方地址
 *                    - msg_iov：数据缓冲区数组
 *                    - msg_control：控制信息
 * @param[in] flags 接收标志：
 *                  - MSG_PEEK：查看数据
 *                  - MSG_WAITALL：等待所有数据
 *                  - MSG_DONTWAIT：非阻塞
 * @return 成功时返回接收的字节数，失败时返回负值错误码。
 * @retval >0 成功接收的字节数。
 * @retval 0 连接已关闭。
 * @retval -EBADF 无效的文件描述符。
 * @retval -EINVAL 参数无效。
 *
 * @note 1. 支持分散接收。
 *       2. 可获取控制信息。
 *       3. 可能阻塞。
 *       4. 线程安全。
 */
DEFINE_SYSCALL (recvmsg, (int fd, struct msghdr __user *msg, unsigned flags))
{
    struct msghdr *kmsg;
    int            ret;
    ssize_t        recv_len = 0;

    if (NULL == msg)
    {
        return -EINVAL;
    }

    kmsg = calloc(1, sizeof(*kmsg));
    if (NULL == kmsg)
    {
        return -ENOMEM;
    }

    ret = copy_from_user (kmsg, msg, sizeof (struct msghdr));
    if (ret < 0)
    {
        recv_len = (ssize_t)ret;
        goto errout;
    }

    if (user_access_check (msg->msg_name, msg->msg_namelen, UACCESS_R))
    {
        kmsg->msg_name = malloc(msg->msg_namelen);
        if (kmsg->msg_name == NULL)
        {
            recv_len = -ENOMEM;
            goto errout;
        }
        ret = copy_from_user (kmsg->msg_name, msg->msg_name, msg->msg_namelen);
        if (ret < 0)
        {
            recv_len = (ssize_t)ret;
            goto errout;
        }
    }

    if (user_access_check (msg->msg_iov, msg->msg_iovlen, UACCESS_R))
    {
        kmsg->msg_iov = calloc (kmsg->msg_iovlen, sizeof (struct iovec));
        if (kmsg->msg_iov == NULL)
        {
            recv_len = -ENOMEM;
            goto errout;
        }
        for (int i = 0; i < kmsg->msg_iovlen; i++)
        {
            kmsg->msg_iov[i].iov_len  = msg->msg_iov[i].iov_len;
            kmsg->msg_iov[i].iov_base = malloc (msg->msg_iov[i].iov_len);
            if (kmsg->msg_iov[i].iov_base == NULL)
            {
                recv_len = -ENOMEM;
                goto errout;
            }
        }
    }

    if (user_access_check (msg->msg_control, msg->msg_controllen, UACCESS_R))
    {
        kmsg->msg_control = malloc (msg->msg_controllen);
        if (kmsg->msg_control == NULL)
        {
            recv_len = -ENOMEM;
            goto errout;
        }
        ret = copy_from_user (kmsg->msg_control, msg->msg_control,
                              msg->msg_controllen);
        if (ret < 0)
        {
            recv_len = (ssize_t)ret;
            goto errout;
        }
    }

    recv_len = vfs_recvmsg (fd, kmsg, flags);

    if (recv_len > 0)
    {
        if (msg->msg_name != NULL)
        {
            ret = copy_to_user (msg->msg_name, kmsg->msg_name, kmsg->msg_namelen);
            if (ret < 0)
            {
                recv_len = (ssize_t)ret;
                goto errout;
            }
        }

        if (msg->msg_iov != NULL)
        {
            for (int i = 0; i < kmsg->msg_iovlen; i++)
            {
                ret = copy_to_user (msg->msg_iov[i].iov_base,
                                    kmsg->msg_iov[i].iov_base,
                                    kmsg->msg_iov[i].iov_len);
                if (ret < 0)
                {
                    recv_len = (ssize_t)ret;
                    goto errout;
                }
            }
        }
        if (msg->msg_control != NULL)
        {
            ret = copy_to_user (msg->msg_control, kmsg->msg_control,
                                kmsg->msg_controllen);
            if (ret < 0)
            {
                recv_len = (ssize_t)ret;
                goto errout;
            }
        }
    }

errout:

    if (kmsg->msg_name)
    {
        free (kmsg->msg_name);
    }

    if (kmsg->msg_iov)
    {
        for (int i = 0; i < kmsg->msg_iovlen; i++)
        {
            if (kmsg->msg_iov[i].iov_base)
            {
                free (kmsg->msg_iov[i].iov_base);
            }
        }
        free (kmsg->msg_iov);
    }

    if (kmsg->msg_control)
    {
        free (kmsg->msg_control);
    }

    if (kmsg)
    {
        free (kmsg);
    }

    return recv_len;
}
