/**
 * @file ptrace.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 进程跟踪
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include "ptrace/ptrace.h"
#include <errno.h>
#include <stdint.h>
#include <ttos.h>
#include <ttosProcess.h>
#include <ttosUtils.inl>
#include <uaccess.h>
#include <assert.h>

#undef  KLOG_LEVEL
#define KLOG_LEVEL KLOG_DEBUG

#undef KLOG_TAG
#define KLOG_TAG "ptrace"
#include <klog.h>

extern int sys_ptrace(long request, long pid, long addr, long data);

/**
 * @brief 系统调用实现：进程跟踪。
 *
 * 该函数实现了一个系统调用，用于跟踪和调试其他进程。
 *
 * @param[in] request 跟踪请求：
 *                    - PTRACE_TRACEME：被跟踪
 *                    - PTRACE_PEEKTEXT：读取文本
 *                    - PTRACE_PEEKDATA：读取数据
 *                    - PTRACE_POKETEXT：写入文本
 *                    - PTRACE_POKEDATA：写入数据
 * @param[in] pid 目标进程ID
 * @param[in] addr 内存地址
 * @param[in] data 数据
 * @return 成功时返回0或数据，失败时返回负值错误码。
 * @retval 0 成功（部分请求）。
 * @retval >0 成功返回数据。
 * @retval -EPERM 权限不足。
 * @retval -ESRCH 进程不存在。
 *
 * @note 1. 需要特权。
 *       2. 用于调试。
 *       3. 可能阻塞。
 *       4. 影响性能。
 */
DECLARE_SYSCALL (ptrace, (long request, long pid, unsigned long addr,
                          unsigned long data))
{
    return sys_ptrace(request, pid, addr, data);
}
