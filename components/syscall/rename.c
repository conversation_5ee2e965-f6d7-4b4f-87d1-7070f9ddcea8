/**
 * @file rename.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 重命名文件或目录
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"

/**
 * @brief 系统调用实现：重命名文件或目录。
 *
 * 该函数实现了一个系统调用，用于重命名文件或目录。
 *
 * @param[in] oldpath 原路径名
 * @param[in] newpath 新路径名
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功。
 * @retval -EACCES 权限不足。
 * @retval -ENOENT 文件不存在。
 * @retval -EINVAL 参数无效。
 *
 * @note 1. 支持跨目录。
 *       2. 原子操作。
 *       3. 目标存在时覆盖。
 *       4. 不支持跨文件系统。
 */
DEFINE_SYSCALL (rename,
                (const char __user *oldname, const char __user *newname))
{
    return SYSCALL_FUNC (renameat) (AT_FDCWD, oldname, AT_FDCWD, newname);
}
