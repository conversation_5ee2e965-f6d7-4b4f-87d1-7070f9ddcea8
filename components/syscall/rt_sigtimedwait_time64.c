#include "syscall_internal.h"
#include <assert.h>

int kernel_sigtimedwait(const sigset_t *sigset, siginfo_t *info, 
                               const struct timespec *timeout, size_t sigsize);

/**
 * @brief 系统调用实现：等待信号。
 *
 * 该函数实现了一个系统调用，用于等待信号。
 *
 * @param uthese 信号集指针。
 * @param uinfo 信号信息指针。
 * @param uts 超时时间指针。
 * @param sigsetsize 信号集大小。
 * @return 成功时返回 0，失败时返回负值错误码。
 */
DEFINE_SYSCALL (rt_sigtimedwait_time64, (const sigset_t __user *uthese, siginfo_t __user *uinfo,
                      const struct timespec __user *uts, size_t sigsetsize))
{
    return kernel_sigtimedwait(uthese, uinfo, uts, sigsetsize);
}
