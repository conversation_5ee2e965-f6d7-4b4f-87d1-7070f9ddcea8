/**
 * @file rt_sigqueueinfo.c
 * <AUTHOR> (zhang<PERSON><EMAIL>)
 * @brief 发送带数据的信号
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <process_signal.h>
#include <uaccess.h>
#include <errno.h>

int kernel_sigqueueinfo (pid_t pid, int sig, siginfo_t *kinfo);

/**
 * @brief 系统调用实现：发送带数据的信号。
 *
 * 该函数实现了一个系统调用，用于向指定进程发送带有附加数据的信号。
 *
 * @param[in] pid 目标进程ID
 * @param[in] sig 信号编号
 * @param[in] uinfo 信号信息结构
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功。
 * @retval -EINVAL 参数无效。
 * @retval -EPERM 权限不足。
 * @retval -ESRCH 进程不存在。
 *
 * @note 1. 支持实时信号。
 *       2. 可携带数据。
 *       3. 进程间通信。
 *       4. 需要权限。
 */
DEFINE_SYSCALL (rt_sigqueueinfo, (pid_t pid, int sig, siginfo_t __user *uinfo))
{
    int ret;
    siginfo_t kinfo;

    ret = copy_from_user(&kinfo, uinfo, sizeof(siginfo_t));
    if (ret)
    {
        return -EFAULT;
    }

    kinfo.si_signo = sig;

    return kernel_sigqueueinfo(pid, sig, &kinfo);
}
