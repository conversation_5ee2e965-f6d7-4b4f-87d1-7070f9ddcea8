/**
 * @file rt_sigaction.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 设置信号处理动作
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <ttosProcess.h>
#include <process_signal.h>
#include <assert.h>
#include <uaccess.h>
#include <errno.h>

int process_signal_action(pcb_t pcb, int signo,
                           const struct process_sigaction * act,
                           struct process_sigaction * oact);

/**
 * @brief 系统调用实现：设置信号处理动作。
 *
 * 该函数实现了一个系统调用，用于设置指定信号的处理动作。
 *
 * @param[in] sig 信号编号
 * @param[in] act 新的信号处理动作结构
 * @param[out] oact 原信号处理动作结构
 * @param[in] sigsetsize 信号集大小
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功。
 * @retval -EINVAL 参数无效。
 * @retval -EFAULT 内存访问错误。
 * @retval -ENOSYS 不支持的信号。
 *
 * @note 1. 支持实时信号。
 *       2. 线程安全。
 *       3. 可恢复旧动作。
 *       4. 进程级设置。
 */
DEFINE_SYSCALL (rt_sigaction,
                     (int sig, const struct sigaction __user *act,
                      struct sigaction __user *oact, size_t sigsetsize))
{
    int ret = -EINVAL;
    pcb_t pcb;
    struct process_sigaction pkact;
    struct process_sigaction koact, *pkoact = NULL;

    if (!sigsetsize)
    {
        errno = EINVAL;
        goto out;
    }
    
    if (sigsetsize > sizeof(process_sigset_t))
    {
        return -EINVAL;
    }
    
    if (!act && !oact)
    {
        errno = EINVAL;
        goto out;
    }
    
    if (oact)
    {
        if (!user_access_check (oact, sizeof(*oact), UACCESS_W))
        {
            errno = EFAULT;
            goto out;
        }

        memset( &koact, 0, sizeof(struct process_sigaction));
        pkoact = &koact;
    }
    
    if (act)
    {
        if (!user_access_check((void *)act, sizeof(*act), UACCESS_R))
        {
            errno = EFAULT;
            goto out;
        }
        
        memcpy(&pkact, act, sizeof(struct process_sigaction));
    }

    pcb = ttosProcessSelf();
	
    assert(pcb);

    ret = process_signal_action(pcb, sig, act?&pkact:NULL, pkoact);

    if (ret == 0 && oact)
    {
        copy_to_user(oact, pkoact, sizeof(struct process_sigaction));
    }
    
out:
    return ret;
}