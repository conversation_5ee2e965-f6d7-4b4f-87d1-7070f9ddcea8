/**
 * @file rt_sigprocmask.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 设置信号屏蔽字
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"

#include <errno.h>
#include <process_signal.h>
#include <ttosProcess.h>
#include <uaccess.h>

#undef KLOG_TAG
#define KLOG_TAG "Signal"
#include <klog.h>

T_TTOS_TaskControlBlock *current_task (void);

static int mask_command_u2k[] = {
    [SIG_BLOCK]   = PROCESS_SIG_MASK_CMD_BLOCK,
    [SIG_UNBLOCK] = PROCESS_SIG_MASK_CMD_UNBLOCK,
    [SIG_SETMASK] = PROCESS_SIG_MASK_CMD_SET_MASK,
};

static const char * how_str[] = {
    [SIG_BLOCK]   = "SIG_BLOCK",
    [SIG_UNBLOCK] = "SIG_UNBLOCK",
    [SIG_SETMASK] = "SIG_SETMASK",
};

/**
 * @brief 系统调用实现：设置信号屏蔽字。
 *
 * 该函数实现了一个系统调用，用于设置或获取进程的信号屏蔽字。
 *
 * @param[in] how 操作类型：
 *                - SIG_BLOCK：添加信号到屏蔽字
 *                - SIG_UNBLOCK：从屏蔽字移除信号
 *                - SIG_SETMASK：设置新的屏蔽字
 * @param[in] set 新的信号集
 * @param[out] oldset 原信号集
 * @param[in] sigsetsize 信号集大小
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功。
 * @retval -EINVAL 参数无效。
 * @retval -EFAULT 内存访问错误。
 * @retval -ENOSYS 不支持的操作。
 *
 * @note 1. 支持实时信号。
 *       2. 线程安全。
 *       3. 进程级操作。
 *       4. 立即生效。
 */
DEFINE_SYSCALL (rt_sigprocmask, (int how, sigset_t __user *set,
                                 sigset_t __user *oset, size_t sigsetsize))
{
    int               ret     = -1;
    process_sigset_t *pnewset = NULL, *poldset = NULL;
    process_sigset_t  newset, oldset;

    if (!sigsetsize)
    {
        return -EINVAL;
    }

    if (!oset && !set)
    {
        return -EINVAL;
    }
    
    if (set)
    {
        KLOG_D ("process(%d):[%s] %s: rt_sigprocmask:0x%x", get_process_pid(ttosProcessSelf ()) , ttosProcessSelf ()->cmd_name, how_str[how],
                *(unsigned int *)set);
    }

    if (sigsetsize > sizeof (process_sigset_t))
    {
        sigsetsize = sizeof (process_sigset_t);
    }

    if (oset)
    {
        poldset = &oldset;
    }

    if (set)
    {
        copy_from_user (&newset, set, sigsetsize);

        pnewset = &newset;
    }

    ret = process_thread_signal_mask (ttosProcessSelf (), mask_command_u2k[how],
                                      pnewset, poldset);
    if (ret < 0)
    {
        return ret;
    }

    if (oset)
    {
        copy_to_user (oset, poldset, sigsetsize);
    }

    return (ret < 0 ? -EFAULT : ret);
}