/**
 * @file rt_sigsuspend.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 等待信号
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <errno.h>
#include <process_signal.h>
#include <ttosProcess.h>
#include <uaccess.h>

/**
 * @brief 系统调用实现：等待信号。
 *
 * 该函数实现了一个系统调用，用于临时替换信号屏蔽字并挂起进程直到收到信号。
 *
 * @param[in] unewset 临时的信号屏蔽字
 * @param[in] sigsetsize 信号集大小
 * @return 总是返回-EINTR。
 * @retval -EINTR 被信号中断。
 * @retval -EINVAL 参数无效。
 * @retval -EFAULT 内存访问错误。
 *
 * @note 1. 支持实时信号。
 *       2. 原子操作。
 *       3. 会被阻塞。
 *       4. 自动恢复。
 */
DEFINE_SYSCALL (rt_sigsuspend, (sigset_t __user *unewset, size_t sigsetsize))
{
    sigset_t kset;
    KLOG_D ("pcb:%p into rt_sigsuspend", ttosProcessSelf ());
    if (!user_access_check (unewset, sigsetsize, UACCESS_R))
    {
        return -EINVAL;
    }

    if (sigsetsize > sizeof (kset))
    {
        return -EINVAL;
    }

    memset (&kset, 0, sizeof (kset));
    copy_from_user (&kset, unewset, sigsetsize);

    sigsuspend (unewset);

    KLOG_D ("pcb:%p exit rt_sigsuspend", ttosProcessSelf ());
    return -ERESTARTNOHAND;
}
