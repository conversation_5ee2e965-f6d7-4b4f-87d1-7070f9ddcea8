/**
 * @file rt_sigtimedwait.c
 * <AUTHOR> (zhang<PERSON><EMAIL>)
 * @brief 带超时等待信号
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"
#include <assert.h>

/**
 * @brief 内部实现：等待信号。
 *
 * @param uthese 信号集指针。
 * @param uinfo 信号信息指针。
 * @param uts 超时时间指针。
 * @param sigsetsize 信号集大小。
 * @return 成功时返回 0，失败时返回负值错误码。
 */
int kernel_sigtimedwait(const sigset_t *sigset, siginfo_t *info, 
                               const struct timespec *timeout, size_t sigsize);

/**
 * @brief 系统调用实现：带超时等待信号。
 *
 * 该函数实现了一个系统调用，用于等待指定的信号集中的信号，可设置超时时间。
 *
 * @param[in] uset 等待的信号集
 * @param[out] uinfo 接收到的信号信息
 * @param[in] uts 超时时间结构
 * @param[in] sigsetsize 信号集大小
 * @return 成功时返回接收到的信号编号，失败时返回负值错误码。
 * @retval >0 接收到的信号编号。
 * @retval -EAGAIN 超时。
 * @retval -EINTR 被信号中断。
 * @retval -EINVAL 参数无效。
 *
 * @note 1. 支持实时信号。
 *       2. 可设置超时。
 *       3. 可获取信息。
 *       4. 可被中断。
 */
DEFINE_SYSCALL (rt_sigtimedwait, (const sigset_t __user *uthese, siginfo_t __user *uinfo,
                    const struct timespec __user *uts, size_t sigsetsize))
{
    return kernel_sigtimedwait(uthese, uinfo, uts, sigsetsize);
}
