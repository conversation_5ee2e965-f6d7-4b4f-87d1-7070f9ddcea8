/**
 * @file syslog.c
 * <AUTHOR> (<PERSON><PERSON><PERSON><PERSON>@kyland.com)
 * @brief h
 * @version 3.0.1
 * @date 2025-06-05
 *
 * @ingroup syscall
 *
 * @since 3.0.1
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */
#include "syscall_internal.h"
#include <klog.h>
#include <uaccess.h>

/* Close the log. Currently a NOP. */
#define SYSLOG_ACTION_CLOSE 0
/* Open the log. Currently a NOP. */
#define SYSLOG_ACTION_OPEN 1
/**
 * Read from the log. The call waits until the kernel log buffer is nonempty, and then reads at most
 * len bytes into the buffer pointed to by bufp. The call returns the number of bytes read. Bytes
 * read from the log disappear from the log buffer: the information can only be read once. This is
 * the function executed by the kernel when a user program reads /proc/kmsg.
 */
#define SYSLOG_ACTION_READ 2

/**
 * Read all messages remaining in the ring buffer, placing then in the buffer pointed to by bufp.
 * The call reads the last len bytes from the log buffer (nondestructively), but will not read more
 * than was written into the buffer since the last "clear ring buffer" command (see command 5
 * below)). The call returns the number of bytes read.
 */
#define SYSLOG_ACTION_READ_ALL 3

/**
 * Read and clear all messages remaining in the ring buffer. The call does precisely the same as for
 * a type of 3, but also executes the "clear ring buffer" command.
 */
#define SYSLOG_ACTION_READ_CLEAR 4

/**
 * The call executes just the "clear ring buffer" command. The bufp and len arguments are
 * ignored.This command does not really clear the ring buffer. Rather, it sets a kernel bookkeeping
 * variable that determines the results returned by commands 3(SYSLOG_ACTION_READ_ALL) and 4
 * (SYSLOG_ACTION_READ_CLEAR). This command has no effect on commands 2(SYSLOG_ACTION_READ) and 9
 * (SYSLOG_ACTION_SIZE_UNREAD).
 */
#define SYSLOG_ACTION_CLEAR 5

/**
 * Disable printk to console. The call sets the console log level to the minimum, so that no
 * messages are printed to the console. The bufp and len arguments are ignored.
 */
#define SYSLOG_ACTION_CONSOLE_OFF 6

/**
 * The call sets the console log level to the default, so that messages are printed to the console.
 * The bufp and len arguments are ignored.
 */
#define SYSLOG_ACTION_CONSOLE_ON 7

/**
 * The call sets the console log level to the value given in len, which must be an integer between 1
 * and 8 (inclusive). See the loglevel section for details. The bufp argument is ignored.
 */
#define SYSLOG_ACTION_CONSOLE_LEVEL 8

/**
 * The call returns the number of bytes currently available to be read from the kernel log buffer
 * via command 2 (SYSLOG_ACTION_READ). The bufp and len arguments are ignored.
 */
#define SYSLOG_ACTION_SIZE_UNREAD 9

/**
 * This command returns the total size of the kernel log buffer. The bufp and len arguments are
 * ignored.
 */
#define SYSLOG_ACTION_SIZE_BUFFER 10

/**
 * @brief 系统调用实现：内核日志获取
 *
 * 该函数实现了一个系统调用，用于获取内核日志。
 *
 * @param[in] type The type argument determines the action taken by this function. The list below
 * specifies the values for type. The symbolic names are defined in the kernel source, but are not
 * exported to user space; you will either need to use the numbers, or define the names yourself.
 * @param[in] buf The buf argument is used to pass data to and from the kernel.
 * @param[in] len The len argument is used to pass data to and from the kernel.
 * @return For type equal to 2, 3, or 4, a successful call to syslog() returns the number of bytes
 * read. For type 9, syslog() returns the number of bytes currently available to be read on the
 * kernel log buffer. For type 10, syslog() returns the total size of the kernel log buffer. For
 * other values of type, 0 is returned on success.
 * @retval -EINVAL Bad arguments (e.g., bad type; or for type 2, 3, or 4, buf is NULL, or len is
 * less than zero; or for type 8, the level is outside the range 1 to 8).
 * @retval -EFAULT 参数指向无效内存。
 * @retval -EINTR System call was interrupted by a signal; nothing was read. (This can be seen only
 * during a trace.)
 *
 */
DECLARE_SYSCALL(syslog, (int type, char __user *buf, int len))
{
    int ret = 0;
    switch (type)
    {
    case SYSLOG_ACTION_READ:
        ret = klog_read_from_klogctl(buf, len, false);
        break;
    case SYSLOG_ACTION_READ_ALL:
        ret = klog_read_from_klogctl(buf, len, true);
        break;
    case SYSLOG_ACTION_READ_CLEAR:
        ret = klog_read_from_klogctl(buf, len, true);
        break;
    case SYSLOG_ACTION_CLEAR:
        break;
    case SYSLOG_ACTION_CONSOLE_OFF:
        break;
    case SYSLOG_ACTION_CONSOLE_ON:
        break;
    case SYSLOG_ACTION_CONSOLE_LEVEL:
        ret = klog_level_set(len);
        break;
    case SYSLOG_ACTION_SIZE_UNREAD:
        return klog_get_size();
    case SYSLOG_ACTION_SIZE_BUFFER:
        return CONFIG_KLOG_BUFF_SIZE;
    default:
        break;
    }
    return ret;
}