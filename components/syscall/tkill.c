/**
 * @file tkill.c
 * <AUTHOR> (z<PERSON><PERSON><EMAIL>)
 * @brief 向指定线程发送信号
 * @version 3.0.0
 * @date 2024-11-14
 * 
 * @ingroup syscall
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include "syscall_internal.h"

#include <process_signal.h>
#include <uaccess.h>
#include <errno.h>

/**
 * @brief 系统调用实现：向指定线程发送信号。
 *
 * 该函数实现了一个系统调用，用于向指定线程ID的线程发送信号。
 *
 * @param[in] tid 目标线程ID
 * @param[in] sig 要发送的信号
 * @return 成功时返回0，失败时返回负值错误码。
 * @retval 0 成功发送信号。
 * @retval -ESRCH 指定的线程不存在。
 * @retval -EINVAL 无效的信号。
 * @retval -EPERM 权限不足。
 *
 * @note 1. 与kill不同，tkill只能向指定线程发送信号。
 *       2. 需要适当的权限。
 *       3. 线程必须存在且可被发送信号。
 *       4. 信号必须是有效的。
 */
DEFINE_SYSCALL (tkill, (pid_t tid, int sig))
{
    return kernel_tkill(tid, sig);
}
