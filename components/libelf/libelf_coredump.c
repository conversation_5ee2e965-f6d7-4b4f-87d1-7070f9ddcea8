#ifdef CONFIG_COREDUMP

#include <elf.h>
#include <errno.h>
#include <fcntl.h>
#include <fs/fs.h>
#include <mmu.h>
#include <spinlock.h>
#include <stdio.h>

#include <context.h>
#include <coredump.h>
#include <ttosMM.h>
#include <ttosProcess.h>
#include <uaccess.h>
#include <tglock.h>

#define KLOG_TAG "coredump"
#include <klog.h>
/************************ 宏定义 ********************************/

#define EF_FLAG           0
#define EI_MAGIC_SIZE     4
#define ELF_PAGESIZE      PAGE_SIZE
#define PROGRAM_ALIGNMENT 64

#define ROUNDUP(x, y)   ((x + (y - 1)) / (y)) * (y)
#define ROUNDDOWN(x, y) (((x) / (y)) * (y))

#ifndef ELF_OSABI
#define ELF_OSABI ELFOSABI_NONE
#endif

#ifdef CONFIG_ENDIAN_BIG
#define ELF_DATA ELFDATA2MSB
#else
#define ELF_DATA ELFDATA2LSB
#endif

#define COREDUMP_BUF_SIZE      (32 * 1024 * 1024)
#define COREDUMP_TASK_NAME     "coredump_task"
#define COREDUMP_TASK_PRIORITY (100)

/************************ 全局变量 ********************************/

/* 标识coredump写入的任务结果 */
static bool coredump_task_res = false;
static char *hexbuf = NULL;
static char *hexbuf_pos = NULL;
static int  hexbuf_len = 0;

/************************ 类型定义 ******************************/

#if ULONG_MAX == 0xffffffffUL
/* 32位 */
typedef Elf32_Ehdr Elf_Ehdr;
typedef Elf32_Phdr Elf_Phdr;
typedef Elf32_Shdr Elf_Shdr;
typedef Elf32_Nhdr Elf_Nhdr;
#define ELF_CLASS ELFCLASS32;

#else
/* 64位 */
typedef Elf64_Ehdr Elf_Ehdr;
typedef Elf64_Phdr Elf_Phdr;
typedef Elf64_Shdr Elf_Shdr;
typedef Elf64_Nhdr Elf_Nhdr;
#define ELF_CLASS ELFCLASS64;

#endif

/************************模块变量******************************/

/* 定义coredump锁 */
static DEFINE_SPINLOCK (coredump_lock);

/************************函数实现******************************/

static void dump_task_func (void *param)
{
    int         ret;
    int         fd;
    const char *dump_path = (const char *)param;

    fd = open (dump_path, O_CREAT | O_WRONLY | O_TRUNC, 0666);

    if (fd < 0)
    {
        KLOG_E ("Could not open file [%s], ret %d\n", dump_path, ret);
        return;
    }

    ret = vfs_write (fd, hexbuf, hexbuf_len);
    close (fd);
    hexbuf_len = 0;

    if (ret < 0)
    {
        KLOG_E ("coredump write back failed\n");
    }

    coredump_task_res = true;
    free(hexbuf);
    return;
}

static int elf_buffer_init()
{
    hexbuf_len = 0;
    hexbuf_pos = NULL;
    hexbuf = (char *)calloc(COREDUMP_BUF_SIZE, sizeof(char));
    if(!hexbuf)
    {
        KLOG_E("coredump buffer malloc failed");
        return -ENOMEM;
    }

    return 0;
}
/**
 * @brief
 *    将elf格式的coredump数据输出到文件
 * @param[in] buf 要输出的内容
 * @param[in] len 输出内容的长度
 * @retval
 *   <0 写入文件失败
 *   >0 实际写入文件的长度
 */
static int elf_emit (const void *buf, size_t len)
{
    if(hexbuf_pos == NULL)
    {
        /* 初始化 */
        hexbuf_pos = hexbuf;
    }

    if (hexbuf_pos + len > hexbuf + COREDUMP_BUF_SIZE)
    {
        KLOG_E ("reach core file limit 32M, dump stop");
        return 0;
    }

    memcpy (hexbuf_pos, buf, len);

    hexbuf_len += len;
    hexbuf_pos += len;

    return len;
}

/**
 * @brief
 *    计算ELF中note段的大小
 * @param[in] thread_num 堆栈个数，每个进程对应一个
 * @retval
 *   note段的大小
 */
static int elf_get_note_size (int thread_num)
{
    int total;

    total = thread_num
            * (sizeof (Elf_Nhdr) + ROUNDUP (TTOS_OBJECT_NAME_LENGTH, 8)
               + sizeof (elf_prpsinfo_t));

    total += thread_num
             * (sizeof (Elf_Nhdr) + ROUNDUP (TTOS_OBJECT_NAME_LENGTH, 8)
                + sizeof (elf_prstatus_t));

    return total;
}


/**
 * @brief
 *    填充ELF中的HDR字段
 * @param[in] segs ELF中字段的总个数
 */
static void elf_emit_hdr (int segs)
{
    Elf_Ehdr ehdr;

    memset (&ehdr, 0, sizeof (ehdr));
    memcpy (ehdr.e_ident, ELFMAG, EI_MAGIC_SIZE);

    ehdr.e_ident[EI_CLASS]   = ELF_CLASS;
    ehdr.e_ident[EI_DATA]    = ELF_DATA;
    ehdr.e_ident[EI_VERSION] = EV_CURRENT;
    ehdr.e_ident[EI_OSABI]   = ELF_OSABI;

    ehdr.e_type      = ET_CORE;
    ehdr.e_machine   = EM_ARCH;
    ehdr.e_version   = EV_CURRENT;
    ehdr.e_phoff     = sizeof (Elf_Ehdr);
    ehdr.e_flags     = EF_FLAG;
    ehdr.e_ehsize    = sizeof (Elf_Ehdr);
    ehdr.e_phentsize = sizeof (Elf_Phdr);
    ehdr.e_phnum     = segs;

    elf_emit (&ehdr, sizeof (ehdr));
}

/**
 * @brief
 *    填充ELF中的PHDR字段
 * @param[in] thread_num 进程个数
 * @param[in] memsegs 内存分段的个数
 */
static void elf_emit_phdr (pid_t pid, int thread_num, int memsegs)
{
    /* 从文件末尾写入, +1 为后续的emit_memory字段 */
    off_t    offset = hexbuf_len + (memsegs + 1) * sizeof (Elf_Phdr);
    Elf_Phdr phdr;

    memset (&phdr, 0, sizeof (Elf_Phdr));

    phdr.p_type   = PT_NOTE;
    phdr.p_offset = offset;
    phdr.p_flags  = PF_W | PF_R;
    /* 每个线程都有单独的note */
    phdr.p_filesz = elf_get_note_size (thread_num);
    offset += phdr.p_filesz;

    elf_emit (&phdr, sizeof (phdr));

    struct mm        *mm = get_process_mm (pcb_get_by_pid (pid));
    long              flags;
    struct mm_region *region_pos;

    spin_lock_irqsave (&mm->lock, flags);

    list_for_each_entry (region_pos, &mm->mm_region_list, list)
    {
        phdr.p_type   = PT_LOAD;
        phdr.p_offset = ROUNDUP (offset, ELF_PAGESIZE);
        phdr.p_vaddr  = region_pos->virtual_address;
        phdr.p_paddr  = phdr.p_vaddr;
        phdr.p_filesz = region_pos->region_page_count * ttosGetPageSize ();
        phdr.p_memsz  = phdr.p_filesz;
        if (!(region_pos->mem_attr & MT_NO_ACCESS))
        {
            phdr.p_flags = PF_R;
            if (!(region_pos->mem_attr & MT_EXECUTE_NEVER))
            {
                phdr.p_flags |= PF_X;
            }
            if (region_pos->mem_attr & MT_RW)
            {
                phdr.p_flags |= PF_W;
            }
        }
        else
        {
            phdr.p_flags = 0;
        }
        phdr.p_align = ELF_PAGESIZE;
        offset += ROUNDUP (phdr.p_memsz, ELF_PAGESIZE);

        elf_emit (&phdr, sizeof (phdr));
    }
    spin_unlock_irqrestore (&mm->lock, flags);

}

static void elf_emit_pcb_note(pcb_t pcb, void *param)
{
    char           name[ROUNDUP (TTOS_OBJECT_NAME_LENGTH, 8)] = { 0 };
    elf_prstatus_t status;
    elf_prpsinfo_t info;
    Elf_Nhdr       nhdr;
    int signo = (int)(intptr_t) param;

    memset (&info, 0x0, sizeof (info));
    memset (&status, 0x0, sizeof (status));

    /* 填充进程信息 */

    nhdr.n_namesz = sizeof (name);
    nhdr.n_descsz = sizeof (info);
    nhdr.n_type   = NT_PRPSINFO;

    elf_emit (&nhdr, sizeof (nhdr));

    strlcpy (name, pcb->cmd_name, sizeof (name));
    elf_emit (name, sizeof (name));

    if(pcb->group_leader)
    {
        info.pr_pgrp = get_process_pid(pcb->group_leader);
    }
    info.pr_pid = pcb->taskControlId->tid;

    strlcpy (info.pr_fname, pcb->cmd_name, sizeof (info.pr_fname));
    elf_emit (&info, sizeof (info));

    /* 填充进程状态 */

    nhdr.n_descsz = sizeof (status);
    nhdr.n_type   = NT_PRSTATUS;

    elf_emit (&nhdr, sizeof (nhdr));
    elf_emit (name, sizeof (name));

    status.pr_pid = pcb->taskControlId->tid;
    status.pr_cursig = signo;
    if(pcb->group_leader)
    {
        status.pr_pgrp = get_process_pid(pcb->group_leader);
    }

    elf_save_regs (&status, pcb);

    elf_emit (&status, sizeof (status));
}
/**
 * @brief
 *    填充ELF中的note字段（线程组的所有线程）
 *
 * @param[in] pid 异常进程的pid
 */
static void elf_emit_note (pid_t pid, int signo)
{
    foreach_task_group(pcb_get_by_pid(pid), elf_emit_pcb_note, (void *)(intptr_t) signo);
}

/**
 * @brief
 *    ELF文件对齐处理，按页对齐
 */
static int elf_emit_align ()
{
    off_t         align = ROUNDUP (hexbuf_len, ELF_PAGESIZE) - hexbuf_len;
    unsigned char null[256];
    off_t         total = align;
    off_t         ret   = 0;

    memset (null, 0, sizeof (null));

    while (total > 0)
    {
        ret = elf_emit (null, total > sizeof (null) ? sizeof (null) : total);
        if (ret <= 0)
        {
            break;
        }

        total -= ret;
    }

    return ret < 0 ? ret : align;
}

static void emit_region (struct mm_region *region, void *ctx)
{
    int           len;
    virt_addr_t kernel_map_start;
    struct mm_region kregion; 

    /* 需要映射的总长度 */
    len = region->region_page_count * ttosGetPageSize ();

    kernel_map_start = page_address(region->physical_address);

    /* 高位地址 */
    if(kernel_map_start == 0)
    {
        /* 创建pa的临时映射 */
        mm_region_init (&kregion, MT_KERNEL_MEM, region->physical_address, len);
        mm_region_map(get_kernel_mm(), &kregion);
        kernel_map_start = kregion.virtual_address;
    }

    elf_emit ((void *)kernel_map_start, len);
    elf_emit_align ();

    /* unmap */
    if(!page_address(region->physical_address))
    {
        mm_region_unmap(get_kernel_mm(), &kregion);
    }
}

static int mapped_region_cnt (pid_t pid)
{
    pcb_t      pcb = pcb_get_by_pid (pid);
    struct mm *mm  = get_process_mm (pcb);
    long       flags;
    int        cnt = 0;

    spin_lock_irqsave (&mm->lock, flags);
    cnt = list_count (&mm->mm_region_list);
    spin_unlock_irqrestore (&mm->lock, flags);

    return cnt;
}

/**
 * @brief
 *    记录用户态进程映射的地址范围
 *
 * @param[in] memsegs 内存段数量
 */
static void elf_emit_memory (pid_t pid, int memsegs)
{
    pcb_t pcb = pcb_get_by_pid (pid);

    mm_foreach_region (get_process_mm (pcb), emit_region, NULL);

    elf_emit_align ();
}

int elf_coredump (char *dump_path, int signo)
{
    long irq_flag = 0, tg_flags = 0;
    TASK_ID  coredump_tid;
    int      ret = 0;
    int thread_num;
    int memsegs;
    pcb_t self = ttosProcessSelf();
    pid_t pid = get_process_pid(self);

    if (!task_get_by_tid (pid))
    {
        KLOG_E ("coredump error, unable to get task by pid %d", pid);
        return -EINVAL;
    }

    tg_lock(self->group_leader);
    thread_num = list_count(&self->group_leader->thread_group);
    tg_unlock(self->group_leader);

    /* 进程映射的内存区域个数 */
    memsegs = mapped_region_cnt (pid);
    ret = elf_buffer_init();
    if(ret != 0)
    {
        return ret;
    }

    spin_lock_irqsave (&coredump_lock, irq_flag);

    /* 填充头部字段, +1 为后续的emit_memory字段 */
    elf_emit_hdr (memsegs + 1);

    /**
     * 填充所有的phdr字段，具体字段的写入应当按照phdr的顺序
     */
    elf_emit_phdr (pid, thread_num, memsegs);

    /* 填充note字段 */

    elf_emit_note (pid, signo);

    /* 按页对齐 */

    elf_emit_align ();

    /* 记录用户态进程的映射地址区间 */
    if (memsegs > 0)
    {
        elf_emit_memory (pid, memsegs);
    }

    spin_unlock_irqrestore (&coredump_lock, irq_flag);

    /* fixme: 临时修改栈大小，避免在libk/realpath.c 中栈溢出 */
    ret = TTOS_CreateTaskEx ((T_UBYTE *)COREDUMP_TASK_NAME, COREDUMP_TASK_PRIORITY, TRUE,
                             TRUE, dump_task_func, (void *)dump_path,
                             DEFAULT_TASK_STACK_SIZE * 4, &coredump_tid);
    if (ret != 0)
    {
        KLOG_E ("Task [%s] Create error %d", COREDUMP_TASK_NAME, ret);
        return ret;
    }

    return 0;
}

 
bool coredump_task_done()
{
    return coredump_task_res;
}


#endif /* CONFIG_COREDUMP */
