/**
 * @file clockchip.c
 * <AUTHOR> (z<PERSON><PERSON><EMAIL>)
 * @brief
 * @version 3.0.0
 * @date 2024-11-18
 *
 * @ingroup clock
 *
 * @since 3.0.0
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include <arch_timer.h>
#include <arch_types.h>
#include <clock/clockchip.h>
#include <cpuid.h>
#include <errno.h>
#include <limits.h>
#include <spinlock.h>
#include <stdio.h>
#include <time/ktime.h>
#include <ttos_pic.h>

#define KLOG_TAG "clockchip.c"
#include <klog.h>

int arch_timer_clockchip_init(void);

/** Control structure for clockchip manager */
struct clockchip_ctrl
{
    ttos_spinlock_t lock;
    struct list_head clkchip_list;
};

static struct clockchip_ctrl ccctrl;

static void default_event_handler(struct clockchip *cc)
{
    /* Just ignore. Do nothing. */
}

/**
 * @brief 设置时钟芯片的事件处理函数
 *
 * 该函数设置时钟芯片的事件处理函数
 *
 * @param[in] cc clockchip结构体指针
 * @param[in] event_handler 事件处理函数
 */
void clockchip_set_event_handler(struct clockchip *cc, void (*event_handler)(struct clockchip *))
{
    if (cc && event_handler)
    {
        cc->event_handler = event_handler;
    }
}

/**
 * @brief 设置未来的时间点expires_ns对应的定时器事件
 *
 * 该函数设置未来的时间点expires_ns对应的定时器事件
 *
 * @param[in] cc clockchip结构体指针
 * @param[in] now_ns 当前的时间
 * @param[in] expires_ns 过期时间，事件应该发生的时刻点
 * @return 成功时返回 0，失败时返回-1
 * @retval 0  成功。
 * @retval -1 失败。
 */
int clockchip_program_event(struct clockchip *cc, u64 now_ns, u64 expires_ns)
{
    u64 clc, delta;

    if (expires_ns < now_ns)
    {
        // return -1;
        /* 如果此处直接return 会在链表中一直遗留这个过期的事件导致后续事件无法触发
          这里取一个最小时间间隔触发一次 会将这个过期事件立即触发掉 */
        expires_ns = now_ns + cc->min_delta_ns;
    }

    if (cc->mode != CLOCKCHIP_MODE_ONESHOT)
    {
        KLOG_E("fail at %s:%d", __FILE__, __LINE__);
        return -1;
    }

    delta = expires_ns - now_ns;
    cc->next_event = expires_ns;

    if (delta > cc->max_delta_ns)
        delta = cc->max_delta_ns;
    if (delta < cc->min_delta_ns)
        delta = cc->min_delta_ns;

    clc = (delta * cc->mult) >> cc->shift;

    return cc->set_next_event((unsigned long long)clc, cc);
}

/**
 * @brief 设置时钟芯片的工作模式
 *
 * 该函数设置时钟芯片的工作模式
 *
 * @param[in] cc clockchip结构体指针
 * @param[in] mode 时钟芯片的工作模式，可选值如下：
 *              - CLOCKCHIP_MODE_UNUSED：未使用
 *              - CLOCKCHIP_MODE_SHUTDOWN：时钟芯片进入关机模式
 *              - CLOCKCHIP_MODE_PERIODIC：时钟芯片进入周期性模式
 *              - CLOCKCHIP_MODE_ONESHOT： 时钟芯片进入单次模式
 *              - CLOCKCHIP_MODE_RESUME：  时钟芯片从休眠状态恢复到正常工作状态
 */
void clockchip_set_mode(struct clockchip *cc, enum clockchip_mode mode)
{
    if (cc && cc->mode != mode)
    {
        cc->set_mode(mode, cc);
        cc->mode = mode;

        /* Multiplicator of 0 is invalid and we'd crash on it. */
        if (mode == CLOCKCHIP_MODE_ONESHOT)
        {
            if (!cc->mult)
            {
                KLOG_E("%s: clockchip mult=0 not allowed\n", __func__);
            }
        }
    }
}

/**
 * @brief 注册一个时钟芯片驱动
 *
 * 该函数注册一个时钟芯片驱动
 *
 * @param[in] cc clockchip结构体指针
 * @return 成功时返回 0，失败时返回负值错误码。
 * @retval 0 成功
 * @retval -EINVAL 参数无效
 */
int clockchip_register(struct clockchip *cc)
{
    bool found;
    irq_flags_t flags;
    struct clockchip *cct;

    if (!cc)
    {
        return -EINVAL;
    }

    cct = NULL;
    found = false;

    spin_lock_irqsave(&ccctrl.lock, flags);

    list_for_each_entry(cct, &ccctrl.clkchip_list, head)
    {
        if (cct == cc)
        {
            found = true;
            break;
        }
    }

    if (found)
    {
        spin_unlock_irqrestore(&ccctrl.lock, flags);
        return -EINVAL;
    }

    INIT_LIST_HEAD(&cc->head);
    cc->event_handler = default_event_handler;
    cc->bound_on = cpuid_get();
    list_add_tail(&cc->head, &ccctrl.clkchip_list);

    spin_unlock_irqrestore(&ccctrl.lock, flags);

    return 0;
}

/**
 * @brief 注销一个时钟芯片驱动
 *
 * 该函数注销一个时钟芯片驱动
 *
 * @param[in] cc clockchip结构体指针
 * @return 成功时返回 0，失败时返回负值错误码。
 * @retval 0 成功
 * @retval -EINVAL 参数无效
 */
int clockchip_unregister(struct clockchip *cc)
{
    bool found;
    irq_flags_t flags;
    struct clockchip *cct;

    if (!cc)
    {
        return -EINVAL;
    }

    spin_lock_irqsave(&ccctrl.lock, flags);

    if (list_empty(&ccctrl.clkchip_list))
    {
        spin_unlock_irqrestore(&ccctrl.lock, flags);
        return -EINVAL;
    }

    cct = NULL;
    found = false;
    list_for_each_entry(cct, &ccctrl.clkchip_list, head)
    {
        if (cct == cc)
        {
            found = true;
            break;
        }
    }

    if (!found)
    {
        spin_unlock_irqrestore(&ccctrl.lock, flags);
        return -EINVAL;
    }

    list_del(&cc->head);

    spin_unlock_irqrestore(&ccctrl.lock, flags);

    return 0;
}

/**
 * @brief 为cpu绑定一个最合适的时钟芯片
 *
 * 该函数为cpu绑定一个最合适的时钟芯片
 *
 * @param[in] hcpu cpu id
 * @return 成功时返回选中的时钟芯片，失败时返回NULL
 * @retval 成功时返回clockchip结构体指针
 * @retval 失败时返回NULL
 */
struct clockchip *clockchip_bind_best(u32 hcpu)
{
    int best_rating;
    irq_flags_t flags;
    // const struct cpumask *mask;
    struct clockchip *cc, *best_cc;

    if (CONFIG_MAX_CPUS <= hcpu)
    {
        return NULL;
    }

    // mask = vmm_cpumask_of(hcpu);
    cc = NULL;
    best_cc = NULL;
    best_rating = 0;

    spin_lock_irqsave(&ccctrl.lock, flags);

    list_for_each_entry(cc, &ccctrl.clkchip_list, head)
    {
#if 0
		if ((cc->rating > best_rating) &&
		    (cc->bound_on == UINT_MAX) )
#else
        if (cc->rating > best_rating && cc->bound_on == hcpu)

#endif
        {
            best_cc = cc;
            best_rating = cc->rating;
        }
    }

    if (best_cc)
    {
        // ttos_pic_affinity_set(best_cc->hirq, hcpu);
        best_cc->bound_on = hcpu;
    }

    spin_unlock_irqrestore(&ccctrl.lock, flags);

    return best_cc;
}

/**
 * @brief 解绑一个时钟芯片驱动
 *
 * 该函数解绑一个时钟芯片驱动
 *
 * @param[in] hcpu cpu id
 * @return 成功时返回选中的时钟芯片，失败时返回NULL
 * @retval 成功时返回clockchip结构体指针
 * @retval 失败时返回NULL
 */
int clockchip_unbind(struct clockchip *cc)
{
    irq_flags_t flags;

    if (!cc)
    {
        return -EINVAL;
    }

    spin_lock_irqsave(&ccctrl.lock, flags);
    cc->bound_on = UINT_MAX;
    spin_unlock_irqrestore(&ccctrl.lock, flags);

    return 0;
}

/**
 * @brief 根据索引获取指向时钟芯片的结构体指针
 *
 * 该函数根据索引获取指向时钟芯片的结构体指针
 *
 * @param[in] index 索引
 * @return 成功时返回时钟芯片结构体指针，失败时返回NULL
 * @retval 成功时返回clockchip结构体指针
 * @retval 失败时返回NULL
 */
struct clockchip *clockchip_get(int index)
{
    bool found;
    irq_flags_t flags;
    struct clockchip *cc;

    if (index < 0)
    {
        return NULL;
    }

    spin_lock_irqsave(&ccctrl.lock, flags);

    cc = NULL;
    found = false;

    list_for_each_entry(cc, &ccctrl.clkchip_list, head)
    {
        if (!index)
        {
            found = true;
            break;
        }

        index--;
    }

    spin_unlock_irqrestore(&ccctrl.lock, flags);

    if (!found)
    {
        return NULL;
    }

    return cc;
}

/**
 * @brief 获取当前系统中时钟芯片的数量
 *
 * 该函数获取当前系统中时钟芯片的数量
 *
 * @return 返回时钟芯片的数量
 * @retval 时钟芯片的数量
 */
u32 clockchip_count(void)
{
    u32 retval = 0;
    irq_flags_t flags;
    struct clockchip *cc;

    spin_lock_irqsave(&ccctrl.lock, flags);

    list_for_each_entry(cc, &ccctrl.clkchip_list, head)
    {
        retval++;
    }

    spin_unlock_irqrestore(&ccctrl.lock, flags);

    return retval;
}

/**
 * @brief 时钟芯片初始化
 *
 * 该函数初始化时钟芯片
 *
 * @return 成功时返回0
 * @retval 0 成功
 */
int clockchip_init(void)
{
    /* Initialize clockchip list lock */
    INIT_SPIN_LOCK(&ccctrl.lock);

    /* Initialize clockchip list */
    INIT_LIST_HEAD(&ccctrl.clkchip_list);

    /* Probe all device tree nodes matching
     * clockchip nodeid table enteries.
     */

    arch_timer_clockchip_init();

    return 0;
}
