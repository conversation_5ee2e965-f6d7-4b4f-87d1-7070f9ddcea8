/**
 * @file clocksource.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 
 * @version 3.0.0
 * @date 2024-11-18
 * 
 * @ingroup clock
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include <arch_timer.h>
#include <arch_types.h>
#include <clock/clocksource.h>
#include <spinlock.h>
#include <stdio.h>
#include <string.h>
#include <ttos.h>

int arch_timer_clocksource_init (void);

/** Control structure for clocksource manager */
struct clocksource_ctrl {
    ttos_spinlock_t  lock;
    struct list_head clksrc_list;
};

static struct clocksource_ctrl csctrl;

/**
 * @brief 获取timecounter关联的时钟源的频率
 *
 * 该函数获取timecounter关联的时钟源的频率
 *
 * @param[in] tc timecounter结构体指针
 * @return 成功时返回频率，失败时返回0
 */
u32 timecounter_clocksource_frequency (struct timecounter *tc)
{
    return (tc && tc->cs) ? tc->cs->freq : 0;
}

/**
 * @brief 获取timecounter关联的时钟源的计数
 *
 * 该函数获取timecounter关联的时钟源的计数
 *
 * @param[in] tc timecounter结构体指针
 * @return 成功时返回计数，失败时返回0
 */
u64 timecounter_clocksource_count (struct timecounter *tc)
{
    return (tc && tc->cs) ? tc->cs->read (tc->cs) : 0;
}

/**
 * @brief 获取timecounter关联的时钟源的时间戳
 *
 * 该函数获取timecounter关联的时钟源的时间戳
 *
 * @param[in] tc timecounter结构体指针
 * @return 成功时返回时间戳，失败时返回0
 */
u64 timecounter_read (struct timecounter *tc)
{
    u64 cycles_now, cycles_delta;
	u64 ns_offset;

    if (!tc || !tc->cs)
    {
        return 0;
    }

    cycles_now = tc->cs->read (tc->cs);

	cycles_delta = (cycles_now - tc->cycles_last) & tc->cs->mask;
	tc->cycles_last = cycles_now;

	ns_offset = clocksource_delta2nsecs(cycles_delta,
						tc->cs->mult, tc->cs->shift);
	tc->nsec += ns_offset;
    return tc->nsec;
}

/**
 * @brief 启动一个时间计数器
 *
 * 该函数启动一个时间计数器
 *
 * @param[in] tc timecounter结构体指针
 * @return 成功时返回0，失败时返回-1
 */
int timecounter_start (struct timecounter *tc)
{
    if (!tc || !tc->cs)
    {
        return -1;
    }

    if (tc->cs->enable)
    {
        tc->cs->enable (tc->cs);
    }

    return 0;
}

/**
 * @brief 停止一个时间计数器
 *
 * 该函数停止一个时间计数器
 *
 * @param[in] tc timecounter结构体指针
 * @return 成功时返回0，失败时返回-1
 */
int timecounter_stop (struct timecounter *tc)
{
    if (!tc || !tc->cs)
    {
        return -1;
    }

    if (tc->cs->disable)
    {
        tc->cs->disable (tc->cs);
    }

    return 0;
}

/**
 * @brief 初始化一个时间计数器
 *
 * 该函数初始化一个时间计数器
 *
 * @param[in] tc timecounter结构体指针
 * @param[in] cs timer_clocksource结构体指针
 * @param[in] start_nsec 计时器启动的初始时间
 * @return 成功时返回0，失败时返回-1
 */
int timecounter_init (struct timecounter *tc, struct timer_clocksource *cs,
                      u64 start_nsec)
{
    if (!tc || !cs)
    {
        return -1;
    }

    tc->cs          = cs;
    tc->cycles_last = cs->read (cs);
    tc->nsec        = start_nsec;

    return 0;
}

/**
 * @brief 注册一个时钟源
 *
 * 该函数注册一个时钟源
 *
 * @param[in] cs timer_clocksource结构体指针
 * @return 成功时返回0，失败时返回-1
 */
int clocksource_register (struct timer_clocksource *cs)
{
    bool                      found;
    irq_flags_t               flags;
    struct timer_clocksource *cst;

    if (!cs)
    {
        return -1;
    }

    cst   = NULL;
    found = false;

    spin_lock_irqsave (&csctrl.lock, flags);

    list_for_each_entry (cst, &csctrl.clksrc_list, head)
    {
        if (strcmp (cst->name, cs->name) == 0)
        {
            found = true;
            break;
        }
    }

    if (found)
    {
        spin_unlock_irqrestore (&csctrl.lock, flags);
        return -1;
    }

    INIT_LIST_HEAD (&cs->head);
    list_add_tail (&cs->head, &csctrl.clksrc_list);

    spin_unlock_irqrestore (&csctrl.lock, flags);

    return 0;
}

/**
 * @brief 注销一个时钟源
 *
 * 该函数注销一个时钟源
 *
 * @param[in] cs timer_clocksource结构体指针
 * @return 成功时返回0，失败时返回-1
 */
int clocksource_unregister (struct timer_clocksource *cs)
{
    bool                      found;
    irq_flags_t               flags;
    struct timer_clocksource *cst;

    if (!cs)
    {
        return -1;
    }

    spin_lock_irqsave (&csctrl.lock, flags);

    if (list_empty (&csctrl.clksrc_list))
    {
        spin_unlock_irqrestore (&csctrl.lock, flags);
        return -1;
    }

    cst   = NULL;
    found = false;
    list_for_each_entry (cst, &csctrl.clksrc_list, head)
    {
        if (strcmp (cst->name, cs->name) == 0)
        {
            found = true;
            break;
        }
    }

    if (!found)
    {
        spin_unlock_irqrestore (&csctrl.lock, flags);
        return -1;
    }

    list_del (&cs->head);

    spin_unlock_irqrestore (&csctrl.lock, flags);

    return 0;
}

/**
 * @brief 选择最佳时钟源
 *
 * 该函数选择最佳时钟源
 *
 * @return 成功时返回时钟源，失败时返回NULL
 */
struct timer_clocksource *clocksource_best (void)
{
    int                       rating = 0;
    irq_flags_t               flags;
    struct timer_clocksource *cs, *best_cs;

    cs      = NULL;
    best_cs = NULL;

    spin_lock_irqsave (&csctrl.lock, flags);

    list_for_each_entry (cs, &csctrl.clksrc_list, head)
    {
        if (cs->rating > rating)
        {
            best_cs = cs;
            rating  = cs->rating;
        }
    }

    spin_unlock_irqrestore (&csctrl.lock, flags);

    return best_cs;
}

/**
 * @brief 根据name查找时钟源
 *
 * 该函数根据name查找时钟源
 *
 * @param[in] name 时钟源名字
 * @return 成功时返回时钟源，失败时返回NULL
 */
struct timer_clocksource *clocksource_find (const char *name)
{
    bool                      found;
    irq_flags_t               flags;
    struct timer_clocksource *cs;

    if (!name)
    {
        return NULL;
    }

    found = false;
    cs    = NULL;

    spin_lock_irqsave (&csctrl.lock, flags);

    list_for_each_entry (cs, &csctrl.clksrc_list, head)
    {
        if (strcmp (cs->name, name) == 0)
        {
            found = true;
            break;
        }
    }

    spin_unlock_irqrestore (&csctrl.lock, flags);

    if (!found)
    {
        return NULL;
    }

    return cs;
}

/**
 * @brief 根据索引查找时钟源
 *
 * 该函数根据索引查找时钟源
 *
 * @param[in] index 时钟源索引
 * @return 成功时返回时钟源，失败时返回NULL
 */
struct timer_clocksource *clocksource_get (int index)
{
    bool                      found;
    irq_flags_t               flags;
    struct timer_clocksource *cs;

    if (index < 0)
    {
        return NULL;
    }

    cs    = NULL;
    found = false;

    spin_lock_irqsave (&csctrl.lock, flags);

    list_for_each_entry (cs, &csctrl.clksrc_list, head)
    {
        if (!index)
        {
            found = true;
            break;
        }

        index--;
    }

    spin_unlock_irqrestore (&csctrl.lock, flags);

    if (!found)
    {
        return NULL;
    }

    return cs;
}

/**
 * @brief 获取时钟源个数
 *
 * 该函数获取时钟源个数
 *
 * @return 返回时钟源个数
 */
u32 clocksource_count (void)
{
    u32                       retval = 0;
    irq_flags_t               flags;
    struct timer_clocksource *cs;

    spin_lock_irqsave (&csctrl.lock, flags);

    list_for_each_entry (cs, &csctrl.clksrc_list, head)
    {
        retval++;
    }

    spin_unlock_irqrestore (&csctrl.lock, flags);

    return retval;
}

/**
 * @brief 时钟源初始化
 *
 * 该函数用于初始化时钟源
 *
 * @return 成功时返回0
 */
int clocksource_init (void)
{
    /* Initialize clocksource list lock */
    INIT_SPIN_LOCK (&csctrl.lock);

    /* Initialize clocksource list */
    INIT_LIST_HEAD (&csctrl.clksrc_list);

    arch_timer_clocksource_init ();

    return 0;
}
