/**
 * @file kernel_nanosleep.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 
 * @version 3.0.0
 * @date 2024-11-18
 * 
 * @ingroup clock
 * 
 * @since 3.0.0
 * 
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 * 
 */

#include <time/ktime.h>

/**
 * @brief 设置时钟时间。
 *
 * @param[in] _rqtp 指向 timespec 结构体的指针，用于指定休眠时间。
 * @param[out] _rmtp 指向 timespec 结构体的指针，用于返回剩余休眠时间。
 * @return 成功时返回 0，失败时返回负值错误码。
 * @retval -EINVAL  参数无效
 */
int kernel_nanosleep (struct timespec64 *_rqtp, struct timespec64 *_rmtp)
{
    return kernel_clock_nanosleep (CLOCK_MONOTONIC, 0, _rqtp, _rmtp);
}
