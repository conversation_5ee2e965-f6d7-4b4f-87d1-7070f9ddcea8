
string(REGEX REPLACE "/$" "" CURRENT_FOLDER_ABSOLUTE ${CMAKE_CURRENT_SOURCE_DIR})

file(GLOB SOURCE_FILE_LIST ${CURRENT_FOLDER_ABSOLUTE}/*.c ${CURRENT_FOLDER_ABSOLUTE}/*.S)

foreach(file ${SOURCE_FILE_LIST})
    list(APPEND GLOB_SOURCE_LIST ${file})
endforeach()

ADD_SUBDIR(vfs)
ADD_SUBDIR(driver)
ADD_SUBDIR(device)
ADD_SUBDIR(mount)
ADD_SUBDIR(mmap)
ADD_SUBDIR(inode)
ADD_SUBDIR(partition)
ADD_SUBDIR(socket)
ADD_SUBDIR_IF(CONFIG_FS_TMPFS tmpfs)
ADD_SUBDIR_IF(CONFIG_FS_SHMFS shmfs)
ADD_SUBDIR_IF(CONFIG_FS_RAMFS ramfs)
ADD_SUBDIR_IF(CONFIG_NFS nfs)
ADD_SUBDIR_IF(CONFIG_FS_FAT fat)
ADD_SUBDIR_IF(CONFIG_FS_LWEXT4 lwext4)
ADD_SUBDIR_IF(CONFIG_FS_PROCFS procfs)
ADD_SUBDIR_IFNOT(CONFIG_DISABLE_MQUEUE mqueue)

set(GLOB_INC_PATH_LIST ${GLOB_INC_PATH_LIST} PARENT_SCOPE)
set(GLOB_SOURCE_LIST ${GLOB_SOURCE_LIST} PARENT_SCOPE)