/****************************************************************************
 * fs/vfs/fs_epoll.c
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <sys/epoll.h>

#include <errno.h>
#include <poll.h>
#include <string.h>
#include <ttos.h>
#include <unistd.h>

#include <fs/fs.h>
#include <inttypes.h>
#include <kmalloc.h>
#include <stdlib.h>
#include <time/ktime.h>
#include <ttosProcess.h>
#include <atomic.h>

#include "../inode/inode.h"

#define KLOG_TAG "epoll"
#include <klog.h>

/****************************************************************************
 * Private Types
 ****************************************************************************/

struct epoll_node_s
{
    struct list_node node;
    epoll_data_t data;
    bool notified;
    struct kpollfd pfd;
    struct file *filep;
    struct epoll_head_s *eph;
};

typedef struct epoll_node_s epoll_node_t;

struct epoll_head_s
{
    int size;
    atomic_t crefs;
    MUTEX_ID lock;
    SEMA_ID sem;
    struct list_node setup;    /* The setup list, store all the setuped
                                * epoll node.
                                */
    struct list_node teardown; /* The teardown list, store all the epoll
                                * node notified after epoll_wait finish,
                                * these epoll node should be setup again
                                * to check the pending poll notification.
                                */
    struct list_node oneshot;  /* The oneshot list, store all the epoll
                                * node notified after epoll_wait and with
                                * EPOLLONESHOT events, these oneshot epoll
                                * nodes can be reset by epoll_ctl (move
                                * from oneshot list to the setup list).
                                */
    struct list_node free;     /* The free list, store all the freed epoll
                                * node.
                                */
    struct list_node extend;   /* The extend list, store all the malloced
                                * first node, used to free the malloced
                                * memory in epoll_do_close().
                                */
};

typedef struct epoll_head_s epoll_head_t;

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static int epoll_do_open(struct file *filep);
static int epoll_do_close(struct file *filep);
static int epoll_do_poll(struct file *filep, struct kpollfd *fds, bool setup);
static int epoll_setup(epoll_head_t *eph);
static int epoll_teardown(epoll_head_t *eph, struct epoll_event *evs, int maxevents);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct file_operations g_epoll_ops = {
    .open = epoll_do_open,   /* open */
    .close = epoll_do_close, /* close */
    .poll = epoll_do_poll    /* poll */
};

static struct inode g_epoll_inode = {
    .i_crefs = 1,                      /* i_crefs */
    .i_flags = FSNODEFLAG_TYPE_DRIVER, /* i_flags */
    .u =
        {
            .i_ops = &g_epoll_ops, /* u */
        },
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

static epoll_head_t *epoll_head_from_fd(int fd, struct file **filep)
{
    int ret;

    /* Get file pointer by file descriptor */

    ret = fs_getfilep(fd, filep);
    if (ret < 0)
    {
        return NULL;
    }

    /* Check fd come from us */

    if ((*filep)->f_inode->u.i_ops != &g_epoll_ops)
    {
        return NULL;
    }

    return (*filep)->f_priv;
}

static int epoll_do_open(struct file *filep)
{
    epoll_head_t *eph = filep->f_priv;
    int ret;

    ret = TTOS_ObtainMutex(eph->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    atomic_inc(&eph->crefs);
    TTOS_ReleaseMutex(eph->lock);
    return ret;
}

static int epoll_do_close(struct file *filep)
{
    epoll_head_t *eph = filep->f_priv;
    epoll_node_t *epn;
    epoll_node_t *tmp;
    int vref;
    int ret;

    ret = TTOS_ObtainMutex(eph->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    vref = atomic_dec_if_positive(&eph->crefs);
    TTOS_ReleaseMutex(eph->lock);
    if (vref <= 0)
    {
        TTOS_DeleteMutex(eph->lock);
        list_for_each_entry(epn, &eph->setup, node)
        {
            file_poll(epn->filep, &epn->pfd, false);
        }

        list_for_each_entry_safe(epn, tmp, &eph->extend, node)
        {
            list_delete(&epn->node);
            free(epn);
        }

        free(eph);
    }

    return ret;
}

static int epoll_do_poll(struct file *filep, struct kpollfd *fds, bool setup)
{
    return 0;
}

static int epoll_do_create(int size, int flags)
{
    epoll_head_t *eph;
    epoll_node_t *epn;
    int fd;
    int i;

    size = size < 0 ? 1 : size;

    eph = zalloc(sizeof(epoll_head_t) + sizeof(epoll_node_t) * size);
    if (eph == NULL)
    {
        return -ENOMEM;
    }

    eph->size = size;
    TTOS_CreateMutex(1, 0, (&eph->lock));
    TTOS_CreateSemaEx(0, &eph->sem);

    /* List initialize */

    epn = (epoll_node_t *)(eph + 1);

    INIT_LIST_HEAD(&eph->setup);
    INIT_LIST_HEAD(&eph->teardown);
    INIT_LIST_HEAD(&eph->oneshot);
    INIT_LIST_HEAD(&eph->extend);
    INIT_LIST_HEAD(&eph->free);
    for (i = 0; i < size; i++)
    {
        list_add_tail(&epn[i].node, &eph->free);
    }

    ATOMIC_INIT(&eph->crefs, 1);

    /* Alloc the file descriptor */

    fd = files_allocate(&g_epoll_inode, flags, 0, eph, 0, false);
    if (fd < 0)
    {
        TTOS_DeleteMutex(eph->lock);
        free(eph);
    }

    return fd;
}

/****************************************************************************
 * Name: epoll_setup
 *
 * Description:
 *   Setup all the fd.
 *
 * Input Parameters:
 *   eph       - The epoll head pointer
 *
 * Returned Value:
 *   Positive on success, negative on fail
 *
 ****************************************************************************/

static int epoll_setup(epoll_head_t *eph)
{
    epoll_node_t *tepn;
    epoll_node_t *epn;
    int ret;

    ret = TTOS_ObtainMutex(eph->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    list_for_each_entry_safe(epn, tepn, &eph->teardown, node)
    {
        /* Setup again to check the notified pollfd last epoll_wait() to
         * cover the situation several poll event pending on one fd.
         */

        epn->notified = false;
        epn->pfd.pollfd.revents = 0;
        ret = file_poll(epn->filep, &epn->pfd, true);
        if (ret < 0)
        {
            KLOG_E("epoll setup failed, filep=%p, events=%08" PRIx32 ", "
                 "ret=%d",
                 epn->filep, epn->pfd.pollfd.events, ret);
            break;
        }

        list_delete(&epn->node);
        list_add_tail(&epn->node, &eph->setup);
    }

    TTOS_ReleaseMutex(eph->lock);
    return ret;
}

/****************************************************************************
 * Name: epoll_teardown
 *
 * Description:
 *   Teardown all the notified fd and check the notified fd's event with user
 *   expected event.
 *
 * Input Parameters:
 *   eph       - The epoll head pointer
 *   evs       - The epoll events array
 *   maxevents - The epoll events array size
 *
 * Returned Value:
 *   Return the number of fd that notified and the events is also user
 *   expected.
 *
 ****************************************************************************/

static int epoll_teardown(epoll_head_t *eph, struct epoll_event *evs, int maxevents)
{
    epoll_node_t *tepn;
    epoll_node_t *epn;
    int i = 0;
    int ret;

    ret = TTOS_ObtainMutex(eph->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    list_for_each_entry_safe(epn, tepn, &eph->setup, node)
    {
        /* Only check the notified fd */

        if (!epn->notified)
        {
            continue;
        }

        /* Teradown all the notified fd */

        file_poll(epn->filep, &epn->pfd, false);
        list_delete(&epn->node);

        if (epn->pfd.pollfd.revents != 0 && i < maxevents)
        {
            evs[i].data = epn->data;
            evs[i++].events = epn->pfd.pollfd.revents;
            if ((epn->pfd.pollfd.events & EPOLLONESHOT) != 0)
            {
                list_add_tail(&epn->node, &eph->oneshot);
            }
            else
            {
                list_add_tail(&epn->node, &eph->teardown);
            }
        }
        else
        {
            list_add_tail(&epn->node, &eph->teardown);
        }
    }

    TTOS_ReleaseMutex(eph->lock);
    return i;
}

/****************************************************************************
 * Name: epoll_default_cb
 *
 * Description:
 *   The default epoll callback function, this function do the final step of
 *   poll notification.
 *
 * Input Parameters:
 *   fds - The fds
 *
 * Returned Value:
 *   None
 *
 ****************************************************************************/

static void epoll_default_cb(struct kpollfd *fds)
{
    epoll_node_t *epn = fds->arg;

    epn->notified = true;
    if (fds->pollfd.revents != 0)
    {
        /* 这里不一定是1 也有可能是0 具体逻辑还没理 */
        TTOS_ResetSema(1, epn->eph->sem);
    }
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: epoll_create
 *
 * Description:
 *
 * Input Parameters:
 *
 * Returned Value:
 *
 ****************************************************************************/

int vfs_epoll_create(int size)
{
    return epoll_do_create(size, 0);
}

/****************************************************************************
 * Name: epoll_create1
 *
 * Description:
 *
 * Input Parameters:
 *
 * Returned Value:
 *
 ****************************************************************************/

int vfs_epoll_create1(int flags)
{
    return epoll_do_create(CONFIG_FS_NEPOLL_DESCRIPTORS, flags);
}

/****************************************************************************
 * Name: epoll_close
 *
 * Description:
 *
 * Input Parameters:
 *
 * Returned Value:
 *
 ****************************************************************************/

void vfs_epoll_close(int epfd)
{
    close(epfd);
}

/****************************************************************************
 * Name: epoll_ctl
 *
 * Description:
 *
 * Input Parameters:
 *
 * Returned Value:
 *
 ****************************************************************************/

int vfs_epoll_ctl(int epfd, int op, int fd, struct epoll_event *ev)
{
    struct list_node *extend;
    struct file *filep;
    epoll_head_t *eph;
    epoll_node_t *epn;
    epoll_node_t *tepn;
    int ret;
    int i;

    eph = epoll_head_from_fd(epfd, &filep);
    if (eph == NULL)
    {
        return -EBADF;
    }

    ret = TTOS_ObtainMutex(eph->lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        ret = -ttos_ret_to_errno(ret);
        goto err_without_lock;
    }

    switch (op)
    {
    case EPOLL_CTL_ADD:
        KLOG_D("%p CTL ADD: fd=%d ev=%08" PRIx32, eph, fd, ev->events);

        /* Check repetition */
        list_for_each_entry(epn, &eph->setup, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                ret = -EEXIST;
                goto err;
            }
        }

        list_for_each_entry(epn, &eph->teardown, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                ret = -EEXIST;
                goto err;
            }
        }

        list_for_each_entry(epn, &eph->oneshot, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                ret = -EEXIST;
                goto err;
            }
        }

        if (list_is_empty(&eph->free))
        {
            /* Malloc new epoll node, insert the first list_node to the
             * extend list and insert the remaining epoll nodes to the free
             * list.
             */

            extend = zalloc(sizeof(*extend) + 2 * sizeof(epoll_node_t) * eph->size);
            if (extend == NULL)
            {
                ret = -ENOMEM;
                goto err;
            }

            eph->size *= 2;
            list_add_tail(extend, &eph->extend);
            epn = (epoll_node_t *)(extend + 1);
            for (i = 0; i < eph->size; i++)
            {
                list_add_tail(&epn[i].node, &eph->free);
            }
        }

        epn = container_of(list_delete_head(&eph->free), epoll_node_t, node);
        epn->eph = eph;
        epn->data = ev->data;
        epn->notified = false;
        epn->pfd.pollfd.events = ev->events | POLLALWAYS;
        epn->pfd.pollfd.fd = fd;
        epn->pfd.arg = epn;
        epn->pfd.cb = epoll_default_cb;
        epn->pfd.pollfd.revents = 0;

        ret = fs_getfilep(fd, &epn->filep);
        if (ret < 0)
        {
            list_add_tail(&epn->node, &eph->free);
            goto err;
        }

        ret = file_poll(epn->filep, &epn->pfd, true);
        if (ret < 0)
        {
            list_add_tail(&epn->node, &eph->free);
            goto err;
        }

        list_add_tail( &epn->node, &eph->setup);
        break;

    case EPOLL_CTL_DEL:
        KLOG_D("%p CTL DEL: fd=%d", eph, fd);
        list_for_each_entry_safe(epn, tepn, &eph->setup, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                file_poll(epn->filep, &epn->pfd, false);
                list_delete(&epn->node);
                list_add_tail(&epn->node, &eph->free);
                goto out;
            }
        }

        list_for_each_entry_safe(epn, tepn, &eph->teardown, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                list_delete(&epn->node);
                list_add_tail(&epn->node, &eph->free);
                goto out;
            }
        }
        list_for_each_entry_safe(epn, tepn, &eph->oneshot, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                list_delete(&epn->node);
                list_add_tail(&epn->node, &eph->free);
                goto out;
            }
        }

        break;

    case EPOLL_CTL_MOD:
        
        KLOG_D("%p CTL MOD: fd=%d ev=%08" PRIx32, eph, fd, ev->events);
        list_for_each_entry(epn, &eph->setup, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                if (epn->pfd.pollfd.events != (ev->events | POLLALWAYS))
                {
                    file_poll(epn->filep, &epn->pfd, false);

                    epn->notified = false;
                    epn->data = ev->data;
                    epn->pfd.pollfd.events = ev->events | POLLALWAYS;
                    epn->pfd.pollfd.revents = 0;

                    ret = file_poll(epn->filep, &epn->pfd, true);
                    if (ret < 0)
                    {
                        goto err;
                    }
                }

                goto out;
            }
        }

        list_for_each_entry(epn, &eph->teardown, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                if (epn->pfd.pollfd.events != (ev->events | POLLALWAYS))
                {
                    epn->notified = false;
                    epn->data = ev->data;
                    epn->pfd.pollfd.events = ev->events | POLLALWAYS;
                    epn->pfd.pollfd.revents = 0;

                    ret = file_poll(epn->filep, &epn->pfd, true);
                    if (ret < 0)
                    {
                        goto err;
                    }

                    list_delete(&epn->node);
                    list_add_tail(&epn->node, &eph->setup);
                }

                goto out;
            }
        }

        list_for_each_entry(epn, &eph->oneshot, node)
        {
            if (epn->pfd.pollfd.fd == fd)
            {
                epn->notified = false;
                epn->data = ev->data;
                epn->pfd.pollfd.events = ev->events | POLLALWAYS;
                epn->pfd.pollfd.revents = 0;

                ret = file_poll(epn->filep, &epn->pfd, true);
                if (ret < 0)
                {
                    goto err;
                }

                list_delete(&epn->node);
                list_add_tail(&epn->node, &eph->setup);
                break;
            }
        }

        break;

    default:
        ret = -EINVAL;
        goto err;
    }

out:
    TTOS_ReleaseMutex(eph->lock);
    return 0;
err:
    TTOS_ReleaseMutex(eph->lock);
err_without_lock:
    return ret;
}

/****************************************************************************
 * Name: epoll_pwait
 ****************************************************************************/

int vfs_epoll_pwait(int epfd, struct epoll_event *evs, int maxevents, int timeout,
                const sigset_t *sigmask)
{
    struct file *filep;
    epoll_head_t *eph;
    sigset_t oldsigmask;
    int ret;
    pcb_t pcb = ttosProcessSelf();

    eph = epoll_head_from_fd(epfd, &filep);
    if (eph == NULL)
    {
        goto out;
    }

retry:
    ret = epoll_setup(eph);
    if (ret < 0)
    {
        goto err;
    }

    /* Wait the poll ready */

    process_thread_signal_mask(pcb, SIG_SETMASK, (process_sigset_t *)sigmask,
                                     (process_sigset_t *)&oldsigmask);

    if (timeout == 0)
    {
        ret = -ETIMEDOUT;
    }
    else if (timeout > 0)
    {
        ret = TTOS_ObtainSema(eph->sem, clock_ms_to_tick(timeout));
        ret = -ttos_ret_to_errno(ret);
    }
    else
    {
        ret = TTOS_ObtainSema(eph->sem, TTOS_WAIT_FOREVER);
        ret = -ttos_ret_to_errno(ret);
    }

    process_thread_signal_mask(pcb, SIG_SETMASK, (process_sigset_t *)&oldsigmask, NULL);

    if (ret < 0 && ret != -ETIMEDOUT)
    {
        goto err;
    }
    else /* ret >= 0 or ret == -ETIMEDOUT */
    {
        int num = epoll_teardown(eph, evs, maxevents);
        if (num == 0 && ret >= 0)
        {
            goto retry;
        }

        ret = num;
    }

    return ret;

err:
out:
    KLOG_E("epoll wait failed:%d, timeout:%d", -ret, timeout);
    return ret;
}

/****************************************************************************
 * Name: epoll_wait
 *
 * Description:
 *
 * Input Parameters:
 *
 * Returned Value:
 *
 ****************************************************************************/

int vfs_epoll_wait(int epfd, struct epoll_event *evs, int maxevents, int timeout)
{
    struct file *filep;
    epoll_head_t *eph;
    int ret;

    eph = epoll_head_from_fd(epfd, &filep);
    if (eph == NULL)
    {
        goto out;
    }

retry:
    ret = epoll_setup(eph);
    if (ret < 0)
    {
        goto err;
    }

    /* Wait the poll ready */

    if (timeout == 0)
    {
        ret = -ETIMEDOUT;
    }
    else if (timeout > 0)
    {
        ret = TTOS_ObtainSema(eph->sem, clock_ms_to_tick(timeout));
        ret = -ttos_ret_to_errno(ret);
    }
    else
    {
        ret = TTOS_ObtainSema(eph->sem, TTOS_WAIT_FOREVER);
        ret = -ttos_ret_to_errno(ret);
    }

    if (ret < 0 && ret != -ETIMEDOUT)
    {
        goto err;
    }
    else /* ret >= 0 or ret == -ETIMEDOUT */
    {
        int num = epoll_teardown(eph, evs, maxevents);
        if (num == 0 && ret >= 0)
        {
            goto retry;
        }

        ret = num;
    }

    return ret;

err:
out:
    KLOG_E("epoll wait failed:%d, timeout:%d", -ret, timeout);
    return ret;
}