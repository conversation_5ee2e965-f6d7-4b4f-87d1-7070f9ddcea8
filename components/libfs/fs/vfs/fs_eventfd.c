/****************************************************************************
 * fs/vfs/fs_eventfd.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include "ttosUtils.h"
#ifdef CONFIG_EVENT_FD

#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <poll.h>
#include <stdio.h>
#include <stdlib.h>

#include <ttos.h>

#include <sys/eventfd.h>
#include <fs/fs.h>

#define KLOG_TAG "EVENTFD"
#include <klog.h>

/****************************************************************************
 * Private Types
 ****************************************************************************/

typedef struct eventfd_waiter_sem_s
{
    SEMA_ID sem;
    struct eventfd_waiter_sem_s *next;
} eventfd_waiter_sem_t;

/* This structure describes the internal state of the driver */

struct eventfd_priv_s
{
    MUTEX_ID lock;    /* Enforces device exclusive access */
    eventfd_waiter_sem_t *rdsems;  /* List of blocking readers */
    eventfd_waiter_sem_t *wrsems;  /* List of blocking writers */
    eventfd_t             counter; /* eventfd counter */
    unsigned int          minor;   /* eventfd minor number */
    uint8_t               crefs;   /* References counts on eventfd (max: 255) */
    bool mode_semaphore;           /* eventfd mode (semaphore or counter) */

    /* The following is a list if poll structures of threads waiting for
     * driver events.
     */

#ifdef CONFIG_EVENT_FD_POLL
    struct kpollfd *fds[CONFIG_EVENT_FD_NPOLLWAITERS];
#endif
};

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static int eventfd_do_open (struct file *filep);
static int eventfd_do_close (struct file *filep);

static ssize_t eventfd_do_read (struct file *filep, char *buffer, size_t len);
static ssize_t eventfd_do_write (struct file *filep, const char *buffer,
                                 size_t len);
#ifdef CONFIG_EVENT_FD_POLL
static int eventfd_do_poll (struct file *filep, struct kpollfd *fds, bool setup);
#endif

static int eventfd_blocking_io (struct eventfd_priv_s *dev,
                                eventfd_waiter_sem_t  *sem,
                                eventfd_waiter_sem_t **slist);

static unsigned int eventfd_get_unique_minor (void);
static void         eventfd_release_minor (unsigned int minor);

static struct eventfd_priv_s *eventfd_allocdev (void);
static void                   eventfd_destroy (struct eventfd_priv_s *dev);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct file_operations g_eventfd_fops = {
    eventfd_do_open,  /* open */
    eventfd_do_close, /* close */
    eventfd_do_read,  /* read */
    eventfd_do_write, /* write */
    NULL,             /* seek */
    NULL,             /* ioctl */
    NULL,             /* mmap  */
#ifdef CONFIG_EVENT_FD_POLL
    eventfd_do_poll /* poll */
#else
    NULL /* poll */
#endif
#ifndef CONFIG_DISABLE_PSEUDOFS_OPERATIONS
    ,
    NULL /* unlink */
#endif
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

static struct eventfd_priv_s *eventfd_allocdev (void)
{
    struct eventfd_priv_s *dev;

    dev = (struct eventfd_priv_s *)calloc (1, sizeof (struct eventfd_priv_s));

    if (dev)
    {
        /* Initialize the private structure */
        TTOS_CreateMutex (1, 0, &dev->lock);
        TTOS_ObtainMutex (dev->lock, TTOS_MUTEX_WAIT_FOREVER);
    }

    return dev;
}

static void eventfd_destroy (struct eventfd_priv_s *dev)
{
    TTOS_DeleteMutex(dev->lock);
    free (dev);
}

static unsigned int eventfd_get_unique_minor (void)
{
    static unsigned int minor;

    return minor++;
}

static void eventfd_release_minor (unsigned int minor) {}

static int eventfd_do_open (struct file *filep)
{
    struct inode          *inode = filep->f_inode;
    struct eventfd_priv_s *priv  = inode->i_private;
    int                    ret;

    /* Get exclusive access to the device structures */
    ret = TTOS_ObtainMutex (priv->lock, TTOS_MUTEX_WAIT_FOREVER);

    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    KLOG_D ("crefs: %d <%s>\n", priv->crefs, inode->i_name);

    if (priv->crefs >= 255)
    {
        /* More than 255 opens; uint8_t would overflow to zero */

        ret = -EMFILE;
    }
    else
    {
        /* Save the new open count on success */

        priv->crefs += 1;
        ret = 0;
    }
    TTOS_ReleaseMutex (priv->lock);
    return ret;
}

static int eventfd_do_close (struct file *filep)
{
    int                    ret;
    struct inode          *inode = filep->f_inode;
    struct eventfd_priv_s *priv  = inode->i_private;

    /* devpath: EVENT_FD_VFS_PATH + /efd (4) + %u (10) + null char (1) */

    char devpath[sizeof (CONFIG_EVENT_FD_VFS_PATH) + 4 + 10 + 1];

    /* Get exclusive access to the device structures */

    ret = TTOS_ObtainMutex (priv->lock, TTOS_MUTEX_WAIT_FOREVER);

    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    KLOG_D ("crefs: %d <%s>\n", priv->crefs, inode->i_name);

    /* Decrement the references to the driver.  If the reference count will
     * decrement to 0, then uninitialize the driver.
     */

    if (priv->crefs > 1)
    {
        /* Just decrement the reference count and release the semaphore */

        priv->crefs -= 1;
        TTOS_ReleaseMutex (priv->lock);
        return 0;
    }

    /* Re-create the path to the driver. */

    KLOG_D ("destroy\n");
    sprintf (devpath, CONFIG_EVENT_FD_VFS_PATH "/efd%u", priv->minor);

    /* Will be unregistered later after close is done */
    TTOS_ReleaseMutex (priv->lock);
    unregister_driver (devpath);

    assert (priv->lock->nestCount == 0);
    eventfd_release_minor (priv->minor);
    eventfd_destroy (priv);

    return 0;
}

static int eventfd_blocking_io (struct eventfd_priv_s *dev,
                                eventfd_waiter_sem_t  *sem,
                                eventfd_waiter_sem_t **slist)
{
    int ret;
    sem->next = *slist;
    *slist    = sem;

    TTOS_ReleaseMutex (dev->lock);

    /* Wait for eventfd to notify */

    ret = TTOS_ObtainSema (sem->sem, TTOS_SEMA_WAIT_FOREVER);

    if (ret != TTOS_OK)
    {
        ret = -ttos_ret_to_errno(ret);
        /* Interrupted wait, unregister semaphore
         * TODO ensure that lock wait does not fail (ECANCELED)
         */

        TTOS_ObtainMutex (dev->lock, TTOS_MUTEX_WAIT_FOREVER);

        eventfd_waiter_sem_t *cur_sem = *slist;

        if (cur_sem == sem)
        {
            *slist = sem->next;
        }
        else
        {
            while (cur_sem)
            {
                if (cur_sem->next == sem)
                {
                    cur_sem->next = sem->next;
                    break;
                }
            }
        }

        TTOS_ReleaseMutex (dev->lock);
        return ret;
    }

    return -ttos_ret_to_errno(TTOS_ObtainMutex (dev->lock, TTOS_MUTEX_WAIT_FOREVER));
}

static ssize_t eventfd_do_read (struct file *filep, char *buffer, size_t len)
{
    struct inode          *inode = filep->f_inode;
    struct eventfd_priv_s *dev   = inode->i_private;
    ssize_t                ret;

    if (len < sizeof (eventfd_t) || buffer == NULL)
    {
        return -EINVAL;
    }

    ret = TTOS_ObtainMutex (dev->lock, TTOS_MUTEX_WAIT_FOREVER);

    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    /* Wait for an incoming event */

    if (dev->counter == 0)
    {
        if (filep->f_oflags & O_NONBLOCK)
        {
            TTOS_ReleaseMutex (dev->lock);
            return -EAGAIN;
        }

        eventfd_waiter_sem_t sem;
        TTOS_CreateSemaEx (0, &sem.sem);

        do
        {
            ret = eventfd_blocking_io (dev, &sem, &dev->rdsems);

            if (ret < 0)
            {
                TTOS_DeleteSema (sem.sem);
                return ret;
            }
        } while (dev->counter == 0);

        TTOS_DeleteSema (sem.sem);
    }

    /* Device ready for read */

    if (dev->mode_semaphore)
    {
        *(eventfd_t *)buffer = 1;
        dev->counter -= 1;
    }
    else
    {
        *(eventfd_t *)buffer = dev->counter;
        dev->counter         = 0;
    }

#ifdef CONFIG_EVENT_FD_POLL
    /* Notify all poll/select waiters */

    kpoll_notify (dev->fds, CONFIG_EVENT_FD_NPOLLWAITERS, POLLOUT, NULL);
#endif

    /* Notify all waiting writers that counter have been decremented */

    eventfd_waiter_sem_t *cur_sem = dev->wrsems;

    while (cur_sem != NULL)
    {
        TTOS_ReleaseSema(cur_sem->sem);
        cur_sem = cur_sem->next;
    }

    dev->wrsems = NULL;

    TTOS_ReleaseMutex (dev->lock);
    return sizeof (eventfd_t);
}

static ssize_t eventfd_do_write (struct file *filep, const char *buffer,
                                 size_t len)
{
    struct inode          *inode = filep->f_inode;
    struct eventfd_priv_s *dev   = inode->i_private;
    ssize_t                ret;
    eventfd_t              new_counter;

    if (len < sizeof (eventfd_t) || buffer == NULL
        || (*(eventfd_t *)buffer == (eventfd_t)-1)
        || (*(eventfd_t *)buffer == (eventfd_t)0))
    {
        return -EINVAL;
    }

    ret = TTOS_ObtainMutex (dev->lock, TTOS_MUTEX_WAIT_FOREVER);

    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    new_counter = dev->counter + *(eventfd_t *)buffer;

    if (new_counter < dev->counter)
    {
        /* Overflow detected */

        if (filep->f_oflags & O_NONBLOCK)
        {
            TTOS_ReleaseMutex (dev->lock);
            return -EAGAIN;
        }

        eventfd_waiter_sem_t sem;
        TTOS_CreateSemaEx (0, &sem.sem);

        do
        {
            ret = eventfd_blocking_io (dev, &sem, &dev->wrsems);

            if (ret < 0)
            {
                TTOS_DeleteSema (sem.sem);
                return ret;
            }
        } while ((new_counter = dev->counter + *(eventfd_t *)buffer)

                 < dev->counter);

        TTOS_DeleteSema (sem.sem);
    }

    /* Ready to write, update counter */

    dev->counter = new_counter;

#ifdef CONFIG_EVENT_FD_POLL
    /* Notify all poll/select waiters */

    kpoll_notify (dev->fds, CONFIG_EVENT_FD_NPOLLWAITERS, POLLIN, NULL);
#endif

    /* Notify all of the waiting readers */

    eventfd_waiter_sem_t *cur_sem = dev->rdsems;

    while (cur_sem != NULL)
    {
        TTOS_ReleaseSema(cur_sem->sem);
        cur_sem = cur_sem->next;
    }

    dev->rdsems = NULL;

    TTOS_ReleaseMutex (dev->lock);
    return sizeof (eventfd_t);
}

#ifdef CONFIG_EVENT_FD_POLL
static int eventfd_do_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    struct inode          *inode = filep->f_inode;
    struct eventfd_priv_s *dev   = inode->i_private;
    int                    ret;
    int                    i;
    unsigned short         eventset;

    ret = TTOS_ObtainMutex (dev->lock, TTOS_MUTEX_WAIT_FOREVER);

    if (ret != TTOS_OK)
    {
        return -ttos_ret_to_errno(ret);
    }

    ret = 0;

    if (!setup)
    {
        /* This is a request to tear down the poll. */

        struct kpollfd **slot = (struct kpollfd **)fds->priv;

        /* Remove all memory of the poll setup */

        *slot     = NULL;
        fds->priv = NULL;
        goto out;
    }

    /* This is a request to set up the poll. Find an available
     * slot for the poll structure reference
     */

    for (i = 0; i < CONFIG_EVENT_FD_NPOLLWAITERS; i++)
    {
        /* Find an available slot */

        if (!dev->fds[i])
        {
            /* Bind the poll structure and this slot */

            dev->fds[i] = fds;
            fds->priv   = &dev->fds[i];
            break;
        }
    }

    if (i >= CONFIG_EVENT_FD_NPOLLWAITERS)
    {
        fds->priv = NULL;
        ret       = -EBUSY;
        goto out;
    }

    /* Notify the POLLOUT event if the pipe is not full, but only if
     * there is readers.
     */

    eventset = 0;

    if (dev->counter < (eventfd_t)-1)
    {
        eventset |= POLLOUT;
    }

    /* Notify the POLLIN event if the pipe is not empty */

    if (dev->counter > 0)
    {
        eventset |= POLLIN;
    }

    kpoll_notify (dev->fds, CONFIG_EVENT_FD_NPOLLWAITERS, eventset, NULL);

out:
    TTOS_ReleaseMutex (dev->lock);
    return ret;
}
#endif

/****************************************************************************
 * Public Functions
 ****************************************************************************/

int vfs_eventfd (unsigned int count, int flags)
{
    int                    ret;
    int                    new_fd;
    struct eventfd_priv_s *new_dev;

    /* devpath: EVENT_FD_VFS_PATH + /efd (4) + %u (10) + null char (1) */

    char devpath[sizeof (CONFIG_EVENT_FD_VFS_PATH) + 4 + 10 + 1];

    /* Allocate instance data for this driver */

    new_dev = eventfd_allocdev ();

    if (new_dev == NULL)
    {
        /* Failed to allocate new device */

        ret = -ENOMEM;
        goto exit_set_errno;
    }

    new_dev->counter        = count;
    new_dev->mode_semaphore = !!(flags & EFD_SEMAPHORE);

    /* Request a unique minor device number */

    new_dev->minor = eventfd_get_unique_minor ();

    /* Get device path */

    sprintf (devpath, CONFIG_EVENT_FD_VFS_PATH "/efd%u", new_dev->minor);

    /* Register the driver */

    ret = register_driver (devpath, &g_eventfd_fops, 0666, new_dev);

    if (ret < 0)
    {
        KLOG_E ("Failed to register new device %s: %d\n", devpath, ret);
        goto exit_release_minor;
    }

    /* Device is ready for use */

    TTOS_ReleaseMutex (new_dev->lock);

    /* Try open new device */

    new_fd = vfs_open (
        devpath,
        O_RDWR | (flags & (EFD_NONBLOCK | EFD_SEMAPHORE | EFD_CLOEXEC)));

    if (new_fd < 0)
    {
        ret = new_fd;
        goto exit_unregister_driver;
    }

    return new_fd;

exit_unregister_driver:
    unregister_driver (devpath);
exit_release_minor:
    eventfd_release_minor (new_dev->minor);
    eventfd_destroy (new_dev);
exit_set_errno:
    errno = (-ret);
    return -1;
}
#endif /* CONFIG_EVENT_FD */
