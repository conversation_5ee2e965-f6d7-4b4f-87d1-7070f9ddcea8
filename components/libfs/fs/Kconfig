#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "File system configuration"

config DISABLE_MOUNTPOINT
	bool "Disable support for mount points"
	default n

config FS_LARGEFILE
	bool "Large File Support"
	default n
	---help---
		Support files which's length is larger than 4GB:
		https://www.opengroup.org/platform/lfs.html

		Note: the protected and kernel mode on 32bit platform can't exceed
		the 4GB limitation since the auto generated proxy and stub still
		cut 64bit to 32bit value. Please check tools/mksyscall.c for more
		information.

config FS_AUTOMOUNTER
	bool "Auto-mounter"
	default n
	depends on !DISABLE_MOUNTPOINT
	select SCHED_LPWORK
	---help---
		The automounter provides an OS-internal mechanism for automatically
		mounting and unmounting removable media as the media is inserted and
		removed.  See include/nuttx/fs/automout.h for interfacing details.

config FS_AUTOMOUNTER_DEBUG
	bool "Auto-mounter debug"
	default n
	depends on FS_AUTOMOUNTER && DEBUG_FEATURES
	---help---
		Normally, the auto-mounter will generate debug output when sub-system
		level file system debug is enabled.  This option will select debug
		output from the logic related to the auto-mount feature even when file
		system debug is not enable.  This is useful primarily for in vivo
		unit testing of the auto-mount feature.

config FS_AUTOMOUNTER_DRIVER
	bool "Auto-mounter driver"
	default n
	depends on FS_AUTOMOUNTER
	---help---
		Enabling this option will lead to registering of a character driver
		on FS_AUTOMOUNTER_VFS_PATH + mount point path for auto-mounter.
		Example: /var/mnt/sdcard0
		
config FS_AUTOMOUNTER_VFS_PATH
	string "Path to auto-mounter driver"
	default "/var"
	depends on FS_AUTOMOUNTER_DRIVER
	---help---
		The path to where auto-mounter driver will exist in the VFS namespace.

config FS_NEPOLL_DESCRIPTORS
	int "Maximum number of default epoll descriptors for epoll_create1(2)"
	default 8
	---help---
		The maximum number of default epoll descriptors for epoll_create1(2)

config DISABLE_PSEUDOFS_OPERATIONS
	bool "Disable pseudo-filesystem operations"
	default DEFAULT_SMALL
	---help---
		Disable certain operations on pseudo-file systems include mkdir,
		rmdir, unlink, and rename.  These are necessary for the logical
		completeness of the illusion created by the pseudo-filesystem.
		However, in practical embedded system, they are seldom needed and
		you can save a little FLASH space by disabling the capability.

config PSEUDOFS_ATTRIBUTES
	bool "Pseudo-filesystem attributes"
	default y
	depends on !DISABLE_PSEUDOFS_OPERATIONS
	---help---
		Enable support for attributes(e.g. mode, uid, gid and time)
		in the pseudo file system.

config PSEUDOFS_SOFTLINKS
	bool "Pseudo-filesystem soft links"
	default n
	depends on !DISABLE_PSEUDOFS_OPERATIONS
	---help---
		Enable support for soft links in the pseudo file system.  Soft
		links are not supported within mounted volumes by any NuttX file
		system.  However, if this option is selected, then soft links
		may be add in the pseudo file system.  This might be useful, for
		to link a directory in the pseudo-file system, such as /bin, to
		to a directory in a mounted volume, say /mnt/sdcard/bin.

config SENDFILE_BUFSIZE
	int "sendfile() buffer size"
	default 512
	---help---
		Size of the I/O buffer to allocate in sendfile().  Default: 512b

source "components/libfs/fs/vfs/Kconfig"
source "components/libfs/fs/semaphore/Kconfig"
source "components/libfs/fs/mqueue/Kconfig"
source "components/libfs/fs/shm/Kconfig"
source "components/libfs/fs/partition/Kconfig"
source "components/libfs/fs/lwext4/Kconfig"
source "components/libfs/fs/fat/Kconfig"
source "components/libfs/fs/nfs/Kconfig"
source "components/libfs/fs/romfs/Kconfig"
source "components/libfs/fs/cromfs/Kconfig"
source "components/libfs/fs/tmpfs/Kconfig"
source "components/libfs/fs/shmfs/Kconfig"
source "components/libfs/fs/ramfs/Kconfig"
source "components/libfs/fs/smartfs/Kconfig"
source "components/libfs/fs/procfs/Kconfig"
source "components/libfs/fs/spiffs/Kconfig"
source "components/libfs/fs/littlefs/Kconfig"
source "components/libfs/fs/unionfs/Kconfig"
source "components/libfs/fs/userfs/Kconfig"
source "components/libfs/fs/hostfs/Kconfig"
source "components/libfs/fs/rpmsgfs/Kconfig"
