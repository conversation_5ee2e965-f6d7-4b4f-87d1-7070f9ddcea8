

#include <driver/device.h>
#include <errno.h>
#include <stdio.h>
#include <sys/types.h>

#include <fs/fs.h>
#include <sys/stat.h>

#include "../inode/inode.h"

int vfs_bind_path(const char *path, const struct file_operations *fops, dev_t dev, mode_t mode, void *data)
{
    struct inode *node;
    int ret;

    /* Insert a dummy node -- we need to hold the inode semaphore because we
     * will have a momentarily bad structure.
     */

    ret = inode_lock();
    if (ret < 0)
    {
        return ret;
    }

    ret = inode_reserve(path, mode & ~S_IFMT, &node);
    if (ret >= 0)
    {
        /* We have it, now populate it with driver specific information.
         * NOTE that the initial reference count on the new inode is zero.
         */

        if (S_ISCHR(mode))
        {
            INODE_SET_DRIVER(node);
        }
        else if (S_ISBLK(mode))
        {
            INODE_SET_BLOCK(node);
        }
        else if(S_ISFIFO(mode))
        {
            INODE_SET_PIPE(node);
        }
        else if(S_ISSOCK(mode))
        {
            INODE_SET_SOCKET(node);
        }

        node->u.i_ops = fops;
        node->i_private = data;
        node->dev = dev;
        ret = 0;
    }

    inode_unlock();
    return ret;
}

int device_bind_path(struct device *device, const char *path, const struct file_operations *fops,
                     mode_t mode)
{
    char dev_path[64];
    int ret;

    if (path == NULL)
    {
        ret = snprintf(dev_path, 128, "/dev/%s", device->name);
        if (ret < 0)
        {
            inode_unlock();
            return -1;
        }

        path = dev_path;
    }

    return vfs_bind_path(path, fops, 0, mode | S_IFCHR, device);
}