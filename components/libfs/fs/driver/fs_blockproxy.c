/****************************************************************************
 * fs/driver/fs_blockproxy.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <sys/stat.h>
#include <sys/types.h>

#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include <stdlib.h>
#include <ttos.h>

#include "../driver/driver.h"
#include <fs/fs.h>
#include <ttos.h>

#define KLOG_TAG "blockproxy"
#include <klog.h>

#if !defined(CONFIG_DISABLE_MOUNTPOINT)                                        \
    && !defined(CONFIG_DISABLE_PSEUDOFS_OPERATIONS)

/****************************************************************************
 * Private Data
 ****************************************************************************/

static uint32_t g_devno;
static MUTEX_ID g_devno_lock;

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: unique_chardev
 *
 * Description:
 *   Create a unique temporary device name in the /dev/ directory of the
 *   pseudo-file system.  We cannot use mktemp for this because it will
 *   attempt to open() the file.
 *
 * Input Parameters:
 *   None
 *
 * Returned Value:
 *   The allocated path to the device.  This must be released by the caller
 *   to prevent memory links.  NULL will be returned only the case where
 *   we fail to allocate memory.
 *
 ****************************************************************************/

static char *unique_chardev (void)
{
    struct stat statbuf;
    char        devbuf[16];
    uint32_t    devno;
    int         ret;

    /* Loop until we get a unique device name */

    for (;;)
    {
        /* Get the mutex protecting the path number */

        ret = TTOS_ObtainMutex (g_devno_lock, TTOS_WAIT_FOREVER);
        if (ret < 0)
        {
            KLOG_E ("ERROR: pthread_mutex_lock failed: %d", ret);
            return NULL;
        }

        /* Get the next device number and release the semaphore */

        devno = ++g_devno;
        TTOS_ReleaseMutex (g_devno_lock);

        /* Construct the full device number */

        devno &= 0xffffff;
        snprintf (devbuf, 16, "/dev/tmpc%06lx", (unsigned long)devno);

        /* Make sure that file name is not in use */

        ret = vfs_stat (devbuf, &statbuf, 1);
        if (ret < 0)
        {
            assert (ret == -ENOENT);
            return strdup (devbuf);
        }

        /* It is in use, try again */
    }
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: block_proxy
 *
 * Description:
 *   Create a temporary char driver using drivers/bch to mediate character
 *   oriented accessed to the block driver.
 *
 * Input Parameters:
 *   filep  - The caller provided location in which to return the 'struct
 *            file' instance.
 *   blkdev - The path to the block driver
 *   oflags - Character driver open flags
 *
 * Returned Value:
 *   Zero (0) is returned on success.  On failure, a negated errno value is
 *   returned.
 *
 ****************************************************************************/

int block_proxy (struct file *filep, const char *blkdev, int oflags)
{
    char *chardev;
    bool  readonly;
    int   ret;

    assert (blkdev);

    /* Create a unique temporary file name for the character device */

    chardev = unique_chardev ();
    if (chardev == NULL)
    {
        KLOG_E ("ERROR: Failed to create temporary device name");
        return -ENOMEM;
    }

    /* Should this character driver be read-only? */

    readonly = ((oflags & O_ACCMODE) == O_RDONLY);

    /* Wrap the block driver with an instance of the BCH driver */

    ret = bchdev_register (blkdev, chardev, readonly);
    if (ret < 0)
    {
        KLOG_E ("ERROR: bchdev_register(%s, %s) failed: %d", blkdev, chardev,
              ret);

        goto errout_with_chardev;
    }

    /* Open the newly created character driver */

    oflags &= ~(O_CREAT | O_EXCL | O_APPEND | O_TRUNC);
    ret = file_open (filep, chardev, oflags);
    if (ret < 0)
    {
        KLOG_E ("ERROR: Failed to open %s: %d", chardev, ret);
        goto errout_with_bchdev;
    }

    /* Unlink the character device name.  The driver instance will persist,
     * provided that CONFIG_DISABLE_PSEUDOFS_OPERATIONS=y (otherwise, we have
     * a problem here!)
     */

    ret = vfs_unlink (chardev);
    if (ret < 0)
    {
        KLOG_E ("ERROR: Failed to unlink %s: %d", chardev, ret);
        goto errout_with_chardev;
    }

    /* Free the allocated character driver name. */

    free (chardev);
    return 0;

errout_with_bchdev:
    vfs_unlink (chardev);

errout_with_chardev:
    free (chardev);
    return ret;
}

#endif /* !CONFIG_DISABLE_MOUNTPOINT && !CONFIG_DISABLE_PSEUDOFS_OPERATIONS */
