#include <errno.h>
#include <fcntl.h>
#include <fs/fs.h>
#include <net/net.h>
#include <spinlock.h>
#include <string.h>
#include <time.h>
#include <ttos.h>

#define KLOG_TAG "psocket"
#include <klog.h>

static LIST_HEAD (sock_intf_list);

static DEFINE_SPINLOCK (sockif_lock);

/**
 * 返回值
 * 0 - 成功匹配相应socket interface
 * 1 - family、type、protocol都不支持
 * 2 - 匹配到了family，其他不支持
 * 3 - 匹配到了family、type，protocol不支持
 */
static int sockif_probe (struct sock_intf_s *sif, uint16_t family, uint16_t type,
                         uint16_t protocol)
{
    int errlevel = 0;
    int i;

    for (i = 0; i < sif->type_count; i++)
    {
        errlevel = errlevel > 0 ? errlevel : 1;
        if (sif->type[i].family == family)
        {
            errlevel = errlevel < 2 ? 2 : errlevel;
            if (sif->type[i].type == type)
            {
                errlevel = errlevel < 3 ? 3 : errlevel;
                if (family == AF_PACKET || sif->type[i].protocol == protocol)
                {
                    return 0;
                }
            }
        }
    }

    return errlevel;
}

static int net_sockif (uint16_t family, uint16_t type, uint16_t protocol,
                       const struct sock_intf_s **sockif)
{
    struct sock_intf_s *sif;
    long                flags;
    int                 errlevel = 0;

    spin_lock_irqsave (&sockif_lock, flags);
    list_for_each_entry (sif, &sock_intf_list, list)
    {
        errlevel = sockif_probe (sif, family, type, protocol);
        switch (errlevel)
        {
        case 0:
            *sockif = sif;
            spin_unlock_irqrestore (&sockif_lock, flags);
            return 0;
        case 1:
            errlevel = -EAFNOSUPPORT;
            break;
        case 2:
            errlevel = -ESOCKTNOSUPPORT;
            break;
        case 3:
            errlevel = -EPROTONOSUPPORT;
            break;

        default:
            errlevel = -1;
            break;
        }
    }
    spin_unlock_irqrestore (&sockif_lock, flags);

    *sockif = NULL;
    return errlevel;
}

int register_sockif (struct sock_intf_s *si)
{
    long flags;
    spin_lock_irqsave (&sockif_lock, flags);
    list_add (&si->list, &sock_intf_list);
    spin_unlock_irqrestore (&sockif_lock, flags);
    return 0;
}

int unregister_sockif (struct sock_intf_s *si)
{
    long flags;
    spin_lock_irqsave (&sockif_lock, flags);
    list_del (&si->list);
    spin_unlock_irqrestore (&sockif_lock, flags);
    return 0;
}

int psock_socket (int domain, int type, int protocol, struct socket *psock)
{
    const struct sock_intf_s *sockif = NULL;
    int                       ret;

    /* Initialize the socket structure */

#define SOCK_TYPE_MASK 0xf

    if (type & ~(SOCK_CLOEXEC | SOCK_NONBLOCK | SOCK_TYPE_MASK))
    {
        return -EINVAL;
    }

    psock->s_domain = (uint16_t)domain;
    psock->s_proto  = (uint16_t)protocol;
    psock->s_priv   = NULL;
    psock->s_type   = (uint16_t)type & SOCK_TYPE_MASK;

    /* Get the socket interface */

    ret = net_sockif (psock->s_domain, psock->s_type, psock->s_proto, &sockif);

    if (ret < 0 || sockif == NULL)
    {
        KLOG_E ("ERROR: socket unsupported: d: %d t: %d p: %d", psock->s_domain,
                psock->s_type, psock->s_proto);
        return ret;
    }

    /* The remaining of the socket initialization depends on the address
     * family.
     */
    psock->s_sockif = sockif;

    ret = sockif->si_setup (psock);

    if (ret < 0)
    {
        KLOG_E ("ERROR: socket si_setup() failed: %d", ret);
    }

    return ret;
}

int vfs_socket (int domain, int type, int protocol)
{
    struct socket *psock;
    int            oflags = O_RDWR;
    int            sockfd;
    int            ret;

    if (type & SOCK_CLOEXEC)
    {
        oflags |= O_CLOEXEC;
    }

    if (type & SOCK_NONBLOCK)
    {
        oflags |= O_NONBLOCK;
    }

    psock = calloc (1, sizeof (*psock));

    if (psock == NULL)
    {
        ret = -ENOMEM;
        goto errout;
    }

    /* Initialize the socket structure */

    ret = psock_socket (domain, type, protocol, psock);

    if (ret < 0)
    {
        KLOG_E ("ERROR: psock_socket() failed: %d", ret);
        goto errout_with_alloc;
    }

    /* Allocate a socket descriptor */

    sockfd = sockfd_allocate (psock, oflags, 0);

    if (sockfd < 0)
    {
        KLOG_E ("ERROR: Failed to allocate a socket descriptor");
        ret = sockfd;
        goto errout_with_psock;
    }

    return sockfd;

errout_with_psock:
    psock_close (psock);

errout_with_alloc:
    free (psock);

errout:
    errno = (-ret);
    return ret;
}

int kernel_vfs_socket (int domain, int type, int protocol)
{
    struct socket *psock;
    int            oflags = O_RDWR;
    int            sockfd;
    int            ret;

    if (type & SOCK_CLOEXEC)
    {
        oflags |= O_CLOEXEC;
    }

    if (type & SOCK_NONBLOCK)
    {
        oflags |= O_NONBLOCK;
    }

    psock = calloc (1, sizeof (*psock));

    if (psock == NULL)
    {
        ret = -ENOMEM;
        goto errout;
    }

    /* Initialize the socket structure */

    ret = psock_socket (domain, type, protocol, psock);

    if (ret < 0)
    {
        KLOG_E ("ERROR: psock_socket() failed: %d", ret);
        goto errout_with_alloc;
    }

    /* Allocate a socket descriptor */

    sockfd = sockfd_allocate (psock, oflags, 1);

    if (sockfd < 0)
    {
        KLOG_E ("ERROR: Failed to allocate a socket descriptor");
        ret = sockfd;
        goto errout_with_psock;
    }

    return sockfd;

errout_with_psock:
    psock_close (psock);

errout_with_alloc:
    free (psock);

errout:
    errno = (-ret);
    return ret;
}

int psock_close (struct socket *psock)
{
    int ret = -ENOSYS;

    /* Verify that the sockfd corresponds to valid, allocated socket */

    if (psock == NULL)
    {
        return -EBADF;
    }

    /* We perform the close operation only if this is the last count on
     * the socket. (actually, I think the socket crefs only takes the values
     * 0 and 1 right now).
     *
     * It is possible for a psock to have no connection, e.g. a TCP socket
     * waiting in accept.
     */

    if (psock->s_sockif != NULL && psock->s_sockif->si_close != NULL)
    {
        ret = psock->s_sockif->si_close (psock);
    }

    /* The socket will not persist... reset it */

    memset (psock, 0, sizeof (*psock));

    return ret;
}

int psock_bind (struct socket *psock, const struct sockaddr *addr,
                socklen_t addrlen)
{
    int ret = 0;

    /* Verify that the psock corresponds to valid, allocated socket */

    if (!psock || psock->s_priv == NULL)
    {
        return -ENOTSOCK;
    }

    if (psock->s_sockif->si_bind == NULL)
    {
        return -EOPNOTSUPP;
    }

    ret = psock->s_sockif->si_bind (psock, addr, addrlen);

    /* Was the bind successful */

    return ret;
}

int vfs_bind (int sockfd, const struct sockaddr *addr, socklen_t addrlen)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Then let psock_bind do all of the work */

    if (psock != NULL)
    {
        ret = psock_bind (psock, addr, addrlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int kernel_vfs_bind (int sockfd, const struct sockaddr *addr, socklen_t addrlen)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = kernel_sockfd_socket (sockfd, &psock);

    /* Then let psock_bind do all of the work */

    if (psock != NULL)
    {
        ret = psock_bind (psock, addr, addrlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int psock_listen (struct socket *psock, int backlog)
{
    int ret;

    /* Verify that the sockfd corresponds to a connected SOCK_STREAM */

    if (psock == NULL || psock->s_priv == NULL)
    {
        KLOG_E ("ERROR: Invalid or unconnected socket");
        return -EINVAL;
    }

    if (psock->s_sockif->si_listen == NULL)
    {
        return -EOPNOTSUPP;
    }

    ret = psock->s_sockif->si_listen (psock, backlog);

    if (ret < 0)
    {
        KLOG_E ("ERROR: si_listen failed: %d", ret);
    }

    return ret;
}

int vfs_listen (int sockfd, int backlog)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* The let psock_listen to the work. If psock_listen() fails, it will have
     * set the errno variable.
     */

    if (psock != NULL)
    {
        ret = psock_listen (psock, backlog);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int psock_accept (struct socket *psock, struct sockaddr *addr,
                  socklen_t *addrlen, struct socket *newsock, int flags)
{
    int ret;
    /* May sure that the socket has been opened with socket() */

    if (psock == NULL || psock->s_priv == NULL)
    {
        KLOG_E ("ERROR: Socket invalid or not opened");
        return -EINVAL;
    }

    /* Let the address family's accept() method handle the operation */

    if (psock->s_sockif->si_accept == NULL)
    {
        return -EOPNOTSUPP;
    }

    ret = psock->s_sockif->si_accept (psock, addr, addrlen, newsock, flags);

    if (ret < 0)
    {
        KLOG_E ("ERROR: si_accept failed: %d", ret);
    }

    return ret;
}

int vfs_accept4 (int sockfd, struct sockaddr *addr, socklen_t *addrlen,
                 int flags)
{
    struct socket *psock = NULL;
    struct socket *newsock;
    int            oflags = O_RDWR;
    int            errcode;
    int            newfd;
    int            ret;

    if (flags & ~(SOCK_NONBLOCK | SOCK_CLOEXEC))
    {
        errcode = EINVAL;
        goto errout;
    }

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Verify that the sockfd corresponds to valid, allocated socket */

    if (psock == NULL)
    {
        errcode = -ret;
        goto errout;
    }

    newsock = calloc (1, sizeof (*newsock));

    if (newsock == NULL)
    {
        errcode = ENOMEM;
        goto errout;
    }

    ret = psock_accept (psock, addr, addrlen, newsock, flags);

    if (ret < 0)
    {
        errcode = -ret;
        goto errout_with_alloc;
    }

    /* Allocate a socket descriptor for the new connection now (so that it
     * cannot fail later)
     */

    if (flags & SOCK_CLOEXEC)
    {
        oflags |= O_CLOEXEC;
    }

    if (flags & SOCK_NONBLOCK)
    {
        oflags |= O_NONBLOCK;
    }

    newfd = sockfd_allocate (newsock, oflags, 0);

    if (newfd < 0)
    {
        errcode = ENFILE;
        goto errout_with_psock;
    }

    return newfd;

errout_with_psock:
    psock_close (newsock);

errout_with_alloc:
    free (newsock);

errout:

    errno = (errcode);
    return -errcode;
}

int psock_connect (struct socket *psock, const struct sockaddr *addr,
                   socklen_t addrlen)
{
    int ret;

    /* Verify that the psock corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }

    /* Make sure that an address was provided */

    if (addr == NULL)
    {
        return -EFAULT;
    }

    /* Let the address family's connect() method handle the operation */

    if (psock->s_sockif->si_connect == NULL)
    {
        return -EOPNOTSUPP;
    }

    ret = psock->s_sockif->si_connect (psock, addr, addrlen);

    return ret;
}

int vfs_connect (int sockfd, const struct sockaddr *addr, socklen_t addrlen)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Then let psock_connect() do all of the work */

    if (psock != NULL)
    {
        ret = psock_connect (psock, addr, addrlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int kernel_vfs_connect (int sockfd, const struct sockaddr *addr, socklen_t addrlen)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = kernel_sockfd_socket (sockfd, &psock);

    /* Then let psock_connect() do all of the work */

    if (psock != NULL)
    {
        ret = psock_connect (psock, addr, addrlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

ssize_t psock_sendmsg (struct socket *psock, struct msghdr *msg, int flags)
{
    /* Verify that non-NULL pointers were passed */

    if (msg == NULL || msg->msg_iov == NULL || msg->msg_iov->iov_base == NULL)
    {
        return -EINVAL;
    }

    /* Verify that the sockfd corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }
    if (psock->s_sockif == NULL || psock->s_sockif->si_sendmsg == NULL)
    {
        return -EBADF;
    }

    /* Let logic specific to this address family handle the sendmsg()
     * operation.
     */
    return psock->s_sockif->si_sendmsg (psock, msg, flags);
}

ssize_t vfs_sendmsg (int sockfd, struct msghdr *msg, int flags)
{
    struct socket *psock;
    ssize_t        ret;

    /* Get the underlying socket structure */

    ret = (ssize_t)sockfd_socket (sockfd, &psock);

    /* Let psock_sendmsg() do all of the work */

    if (psock != NULL)
    {
        ret = psock_sendmsg (psock, msg, flags);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

ssize_t kernel_vfs_sendmsg (int sockfd, struct msghdr *msg, int flags)
{
    struct socket *psock;
    ssize_t        ret;

    /* Get the underlying socket structure */

    ret = (ssize_t)kernel_sockfd_socket (sockfd, &psock);

    /* Let psock_sendmsg() do all of the work */

    if (psock != NULL)
    {
        ret = psock_sendmsg (psock, msg, flags);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

ssize_t psock_recvmsg (struct socket *psock, struct msghdr *msg, int flags)
{
    unsigned long msg_controllen;
    void         *msg_control;
    int           ret;

    /* Verify that non-NULL pointers were passed */

    if (msg == NULL || msg->msg_iov == NULL || msg->msg_iov->iov_base == NULL)
    {
        return -EINVAL;
    }

    if (msg->msg_name != NULL && msg->msg_namelen <= 0)
    {
        return -EINVAL;
    }

    if (msg->msg_iovlen != 1)
    {
        return -ENOTSUP;
    }

    /* Verify that the sockfd corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }

    if (psock->s_sockif == NULL || psock->s_sockif->si_recvmsg == NULL)
    {
        return -EBADF;
    }
    /* Let logic specific to this address family handle the recvmsg()
     * operation.
     */

    /* Save the original cmsg information */

    msg_control    = msg->msg_control;
    msg_controllen = msg->msg_controllen;

    ret = psock->s_sockif->si_recvmsg (psock, msg, flags);
    /* Recover the pointer and calculate the cmsg's true data length */

    msg->msg_control    = msg_control;
    msg->msg_controllen = msg_controllen - msg->msg_controllen;

    return ret;
}

ssize_t vfs_recvmsg (int sockfd, struct msghdr *msg, int flags)
{
    struct socket *psock;
    ssize_t        ret;

    /* recvmsg() is a cancellation point */

    /* Get the underlying socket structure */

    ret = (ssize_t)sockfd_socket (sockfd, &psock);

    /* Let psock_recvmsg() do all of the work */

    if (psock != NULL)
    {
        ret = psock_recvmsg (psock, msg, flags);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

ssize_t psock_send (struct socket *psock, const void *buf, size_t len,
                    int flags)
{
    struct msghdr msg;
    struct iovec  iov;

    iov.iov_base       = (void *)buf;
    iov.iov_len        = len;
    msg.msg_name       = NULL;
    msg.msg_namelen    = 0;
    msg.msg_iov        = &iov;
    msg.msg_iovlen     = 1;
    msg.msg_control    = NULL;
    msg.msg_controllen = 0;
    msg.msg_flags      = 0;

    /* And let psock_sendmsg do all of the work */

    return psock_sendmsg (psock, &msg, flags);
}

ssize_t psock_sendto (struct socket *psock, const void *buf, size_t len,
                      int flags, const struct sockaddr *to, socklen_t tolen)
{
    struct iovec  iov;
    struct msghdr msg;

    if (tolen != 0 && to == NULL)
    {
        return -EINVAL;
    }

    iov.iov_base       = (void *)buf;
    iov.iov_len        = len;
    msg.msg_name       = (struct sockaddr *)to;
    msg.msg_namelen    = tolen;
    msg.msg_iov        = &iov;
    msg.msg_iovlen     = 1;
    msg.msg_control    = NULL;
    msg.msg_controllen = 0;
    msg.msg_flags      = 0;

    /* And let psock_sendmsg do all of the work */

    return psock_sendmsg (psock, &msg, flags);
}

/****************************************************************************
 * Name: sendto
 *
 * Description:
 *   If sendto() is used on a connection-mode (SOCK_STREAM, SOCK_SEQPACKET)
 *   socket, the parameters to and 'tolen' are ignored (and the error EISCONN
 *   may be returned when they are not NULL and 0), and the error ENOTCONN is
 *   returned when the socket was not actually connected.
 *
 * Input Parameters:
 *   sockfd   Socket descriptor of socket
 *   buf      Data to send
 *   len      Length of data to send
 *   flags    Send flags
 *   to       Address of recipient
 *   tolen    The length of the address structure
 *
 * Returned Value:
 *   On success, returns the number of characters sent.  On error,
 *   -1 is returned, and errno is set appropriately:
 *
 *   EAGAIN or EWOULDBLOCK
 *     The socket is marked non-blocking and the requested operation
 *     would block.
 *   EBADF
 *     An invalid descriptor was specified.
 *   ECONNRESET
 *     Connection reset by peer.
 *   EDESTADDRREQ
 *     The socket is not connection-mode, and no peer address is set.
 *   EFAULT
 *      An invalid user space address was specified for a parameter.
 *   EINTR
 *      A signal occurred before any data was transmitted.
 *   EINVAL
 *      Invalid argument passed.
 *   EISCONN
 *     The connection-mode socket was connected already but a recipient
 *     was specified. (Now either this error is returned, or the recipient
 *     specification is ignored.)
 *   EMSGSIZE
 *     The socket type requires that message be sent atomically, and the
 *     size of the message to be sent made this impossible.
 *   ENOBUFS
 *     The output queue for a network interface was full. This generally
 *     indicates that the interface has stopped sending, but may be
 *     caused by transient congestion.
 *   ENOMEM
 *     No memory available.
 *   ENOTCONN
 *     The socket is not connected, and no target has been given.
 *   ENOTSOCK
 *     The argument s is not a socket.
 *   EOPNOTSUPP
 *     Some bit in the flags argument is inappropriate for the socket
 *     type.
 *   EPIPE
 *     The local end has been shut down on a connection oriented socket.
 *     In this case the process will also receive a SIGPIPE unless
 *     MSG_NOSIGNAL is set.
 *
 ****************************************************************************/

ssize_t vfs_sendto (int sockfd, const void *buf, size_t len, int flags,
                    const struct sockaddr *to, socklen_t tolen)
{
    struct socket *psock;
    ssize_t        ret;

    /* sendto() is a cancellation point */

    /* Get the underlying socket structure */

    ret = (ssize_t)sockfd_socket (sockfd, &psock);

    /* And let psock_sendto do all of the work */

    if (psock != NULL)
    {
        ret = psock_sendto (psock, buf, len, flags, to, tolen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

ssize_t kernel_vfs_sendto (int sockfd, const void *buf, size_t len, int flags,
                    const struct sockaddr *to, socklen_t tolen)
{
    struct socket *psock;
    ssize_t        ret;

    /* sendto() is a cancellation point */

    /* Get the underlying socket structure */

    ret = (ssize_t)kernel_sockfd_socket (sockfd, &psock);

    /* And let psock_sendto do all of the work */

    if (psock != NULL)
    {
        ret = psock_sendto (psock, buf, len, flags, to, tolen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

ssize_t psock_recvfrom (struct socket *psock, void *buf, size_t len, int flags,
                        struct sockaddr *from, socklen_t *fromlen)
{
    struct msghdr msg;
    struct iovec  iov;
    ssize_t       ret;

    iov.iov_base       = buf;
    iov.iov_len        = len;
    msg.msg_name       = from;
    msg.msg_namelen    = fromlen ? *fromlen : 0;
    msg.msg_iov        = &iov;
    msg.msg_iovlen     = 1;
    msg.msg_control    = NULL;
    msg.msg_controllen = 0;
    msg.msg_flags      = 0;

    /* And let psock_recvmsg do all of the work */

    ret = psock_recvmsg (psock, &msg, flags);

    if (ret >= 0 && fromlen != NULL)
    {
        *fromlen = msg.msg_namelen;
    }

    return ret;
}

int psock_ioctl (struct socket *psock, unsigned int cmd, unsigned long arg)
{
    if (psock->s_sockif && psock->s_sockif->si_ioctl)
    {
        return psock->s_sockif->si_ioctl (psock, cmd, arg);
    }

    return -ENOTSUP;
}

ssize_t vfs_recvfrom (int sockfd, void *buf, size_t len, int flags,
                      struct sockaddr *from, socklen_t *fromlen)
{
    struct socket *psock;
    ssize_t        ret;

    /* recvfrom() is a cancellation point */

    /* Get the underlying socket structure */

    ret = (ssize_t)sockfd_socket (sockfd, &psock);

    /* Then let psock_recvfrom() do all of the work */

    if (psock != NULL)
    {
        ret = psock_recvfrom (psock, buf, len, flags, from, fromlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

ssize_t kernel_vfs_recvfrom (int sockfd, void *buf, size_t len, int flags,
                      struct sockaddr *from, socklen_t *fromlen)
{
    struct socket *psock;
    ssize_t        ret;

    /* recvfrom() is a cancellation point */

    /* Get the underlying socket structure */

    ret = (ssize_t)kernel_sockfd_socket (sockfd, &psock);

    /* Then let psock_recvfrom() do all of the work */

    if (psock != NULL)
    {
        ret = psock_recvfrom (psock, buf, len, flags, from, fromlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int psock_getsockopt (struct socket *psock, int level, int option, void *value,
                      socklen_t *value_len)
{
    int ret = -ENOPROTOOPT;

    /* Verify that the sockfd corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }

    /* Perform the socket interface operation */

    if (psock->s_sockif->si_getsockopt != NULL)
    {
        ret = psock->s_sockif->si_getsockopt (psock, level, option, value,
                                              value_len);
    }

    /* -ENOTTY really mean -ENOPROTOOPT, but skip the default action */

    if (ret == -ENOTTY)
    {
        ret = -ENOPROTOOPT;
    }

    return ret;
}

int vfs_getsockopt (int sockfd, int level, int option, void *value,
                    socklen_t *value_len)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Then let psock_getsockopt() do all of the work */

    if (psock != NULL)
    {
        ret = psock_getsockopt (psock, level, option, value, value_len);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int psock_setsockopt (struct socket *psock, int level, int option,
                      const void *value, socklen_t value_len)
{
    int ret = -ENOPROTOOPT;

    /* Verify that the socket option if valid (but might not be supported ) */

    if (!value)
    {
        return -EFAULT;
    }

    /* Verify that the sockfd corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }

    /* Perform the socket interface operation */

    if (psock->s_sockif->si_setsockopt != NULL)
    {
        ret = psock->s_sockif->si_setsockopt (psock, level, option, value,
                                              value_len);
    }

    /* -ENOTTY really mean -ENOPROTOOPT, but skip the default action */

    else if (ret == -ENOTTY)
    {
        ret = -ENOPROTOOPT;
    }

    return ret;
}

int vfs_setsockopt (int sockfd, int level, int option, const void *value,
                    socklen_t value_len)
{
    struct socket *psock;
    int            ret = -EBADFD;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Then let psock_setockopt() do all of the work */

    if (psock != NULL)
    {
        ret = psock_setsockopt (psock, level, option, value, value_len);
    }

    return ret;
}

int kernel_vfs_setsockopt (int sockfd, int level, int option, const void *value,
                    socklen_t value_len)
{
    struct socket *psock;
    int            ret = -EBADFD;

    /* Get the underlying socket structure */

    ret = kernel_sockfd_socket (sockfd, &psock);

    /* Then let psock_setockopt() do all of the work */

    if (psock != NULL)
    {
        ret = psock_setsockopt (psock, level, option, value, value_len);
    }

    return ret;
}

int psock_getsockname (struct socket *psock, struct sockaddr *addr,
                       socklen_t *addrlen)
{
    /* Verify that the psock corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }

    /* Some sanity checking... */

    if (addr == NULL || addrlen == NULL)
    {
        return -EINVAL;
    }

    /* Let the address family's send() method handle the operation */

    if (psock->s_sockif->si_getsockname == NULL)
    {
        return -EOPNOTSUPP;
    }

    return psock->s_sockif->si_getsockname (psock, addr, addrlen);
}

int vfs_getsockname (int sockfd, struct sockaddr *addr, socklen_t *addrlen)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Let psock_getsockname() do all of the work */

    if (psock != NULL)
    {
        ret = psock_getsockname (psock, addr, addrlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int psock_getpeername (struct socket *psock, struct sockaddr *addr,
                       socklen_t *addrlen)
{
    /* Verify that the psock corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }

    /* Some sanity checking... Shouldn't need this on a buckled up embedded
     * system (?)
     */

    if (addr == NULL || addrlen == NULL || *addrlen <= 0)
    {
        return -EINVAL;
    }

    /* Let the address family's send() method handle the operation */

    if (psock->s_sockif->si_getpeername == NULL)
    {
        return -EOPNOTSUPP;
    }

    return psock->s_sockif->si_getpeername (psock, addr, addrlen);
}

int vfs_getpeername (int sockfd, struct sockaddr *addr, socklen_t *addrlen)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Let psock_getpeername() do all of the work */

    if (psock != NULL)
    {
        ret = psock_getpeername (psock, addr, addrlen);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int psock_shutdown (struct socket *psock, int how)
{
    /* Verify that the psock corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        return -EBADF;
    }

    /* Let the address family's shutdown() method handle the operation */

    if (psock->s_sockif && psock->s_sockif->si_shutdown)
    {
        return psock->s_sockif->si_shutdown (psock, how);
    }

    return -EOPNOTSUPP;
}

int vfs_shutdown (int sockfd, int how)
{
    struct socket *psock;
    int            ret;

    /* Get the underlying socket structure */

    ret = sockfd_socket (sockfd, &psock);

    /* Then let psock_shutdown() do all of the work */

    if (psock != NULL)
    {
        ret = psock_shutdown (psock, how);
    }

    if (ret < 0)
    {
        errno = (-ret);
    }

    return ret;
}

int psock_poll (struct socket *psock, struct kpollfd *fds, bool setup)
{
    if ((psock->s_sockif == NULL) || (psock->s_sockif->si_poll == NULL))
    {
        return -EOPNOTSUPP;
    }

    return psock->s_sockif->si_poll (psock, fds, setup);
}

int psock_dup2 (struct socket *psock1, struct socket *psock2)
{
    /* Parts of this operation need to be atomic */

    long flags;
    spin_lock_irqsave (&sockif_lock, flags);

    /* Duplicate the relevant socket state (zeroing everything else) */

    memset (psock2, 0, sizeof (struct socket));

    psock2->s_domain
        = psock1->s_domain; /* IP domain: PF_INET, PF_INET6, or PF_PACKET */
    psock2->s_type
        = psock1->s_type; /* Protocol type: Only SOCK_STREAM or SOCK_DGRAM */
    psock2->s_sockif = psock1->s_sockif; /* Socket interface */
    psock2->s_priv   = psock1->s_priv;   /* UDP or TCP connection structure */

    /* Increment the reference count on the underlying connection structure
     * for this address family type.
     */
    if (psock2->s_sockif->si_addref)
    {
        psock2->s_sockif->si_addref (psock2);
    }
    else
    {
        spin_unlock_irqrestore (&sockif_lock, flags);
        return -ENOSYS;
    }

    spin_unlock_irqrestore (&sockif_lock, flags);

    return 0;
}

ssize_t psock_sendfile (struct socket *psock, struct file *infile,
                        off_t *offset, size_t count)
{
    ssize_t ret = -ENOSYS;

    /* Verify that the sockfd corresponds to valid, allocated socket */

    if (psock == NULL || psock->s_priv == NULL)
    {
        KLOG_E ("ERROR: Invalid socket");
        return -EBADF;
    }

    /* Check if the address family supports the optimized sendfile().  If not,
     * revert to the slow version.
     *
     * The address family indicates is support with a non-NULL si_sendfile()
     * method in the socket interface.
     */

    if (psock->s_sockif->si_sendfile != NULL)
    {
        /* The address family can handle the optimized file send */

        ret = psock->s_sockif->si_sendfile (psock, infile, offset, count);
    }

    return ret;
}

int psock_socketpair (int domain, int type, int protocol,
                      struct socket *psocks[2])
{
    int ret;

    /* Initialize the socket structure */

    ret = psock_socket (domain, type, protocol, psocks[0]);

    if (ret < 0)
    {
        return ret;
    }

    if (psocks[0]->s_sockif == NULL || psocks[0]->s_sockif->si_socketpair == NULL)
    {
        ret = -EAFNOSUPPORT;
        goto errsock;
    }

    ret = psock_socket (domain, type, protocol, psocks[1]);

    if (ret < 0)
    {
        goto errsock;
    }

    /* Perform socketpair process */

    ret = psocks[0]->s_sockif->si_socketpair (psocks);

    if (ret == 0)
    {
        return ret;
    }

    psock_close (psocks[1]);
errsock:
    psock_close (psocks[0]);
    return ret;
}

int vfs_socketpair (int domain, int type, int protocol, int sv[2])
{
    struct socket *psocks[2];
    int            oflags = O_RDWR;
    int            ret;
    int            i;
    int            j = 0;
    int            k;

    if (sv == NULL)
    {
        ret = -EINVAL;
        goto errout;
    }

    for (k = 0; k < 2; k++)
    {
        psocks[k] = calloc (1, sizeof (*psocks[k]));

        if (psocks[k] == NULL)
        {
            ret = -ENOMEM;
            goto errout_with_alloc;
        }
    }

    ret = psock_socketpair (domain, type, protocol, psocks);

    if (ret < 0)
    {
        goto errout_with_alloc;
    }

    if (type & SOCK_CLOEXEC)
    {
        oflags |= O_CLOEXEC;
    }

    if (type & SOCK_NONBLOCK)
    {
        oflags |= O_NONBLOCK;
    }

    /* Allocate a socket descriptor */

    for (; j < 2; j++)
    {
        sv[j] = sockfd_allocate (psocks[j], oflags, 0);

        if (sv[j] < 0)
        {
            ret = sv[j];
            goto errout_with_psock;
        }
    }

    return 0;

errout_with_psock:

    for (i = 0; i < j; i++)
    {
        vfs_close (sv[i]);
    }

    for (i = j; i < k; i++)
    {
        psock_close (psocks[i]);
    }

errout_with_alloc:

    for (i = j; i < k; i++)
    {
        free (psocks[i]);
    }

errout:
    errno = (-ret);
    return ret;
}
