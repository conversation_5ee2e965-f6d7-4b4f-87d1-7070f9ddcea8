/****************************************************************************
 * fs/socket/socket.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <fs/fs.h>
#include <stdlib.h>

#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <net/net.h>
#include <sys/socket.h>

#include "../inode/inode.h"

/****************************************************************************
 * Private Functions Prototypes
 ****************************************************************************/

static int     sock_file_open (struct file *filep);
static int     sock_file_close (struct file *filep);
static ssize_t sock_file_read (struct file *filep, char *buffer, size_t buflen);
static ssize_t sock_file_write (struct file *filep, const char *buffer,
                                size_t buflen);
static int     sock_file_ioctl (struct file *filep, unsigned int cmd, unsigned long arg);
static int sock_file_poll (struct file *filep, struct kpollfd *fds, bool setup);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct file_operations g_sock_fileops = {
    sock_file_open,  /* open */
    sock_file_close, /* close */
    sock_file_read,  /* read */
    sock_file_write, /* write */
    NULL,            /* seek */
    sock_file_ioctl, /* ioctl */
    NULL,            /* mmap  */
    sock_file_poll   /* poll */
#ifndef CONFIG_DISABLE_PSEUDOFS_OPERATIONS
    ,
    NULL /* unlink */
#endif
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

static int sock_file_open (struct file *filep)
{
    struct socket *psock;
    int            ret;

    if(filep->f_priv == NULL)
    {
        return -ENOMEM;
    }

    psock = calloc (1, sizeof (*psock));
    if (psock == NULL)
    {
        return -ENOMEM;
    }

    ret = psock_dup2 (filep->f_priv, psock);

    if (ret >= 0)
    {
        filep->f_priv = psock;
    }
    else
    {
        free (psock);
    }

    return ret;
}

static int sock_file_close (struct file *filep)
{
    /* 当socket的inode引用计数为1时，最后一个引用关闭，才真正执行psock_close */
    if (filep->f_inode && (1 == filep->f_inode->i_crefs))
    {
        psock_close (filep->f_priv);
        free (filep->f_priv);
        filep->f_priv = NULL;
    }
    return 0;
}

static ssize_t sock_file_read (struct file *filep, char *buffer, size_t buflen)
{
    return psock_recv (filep->f_priv, buffer, buflen, 0);
}

static ssize_t sock_file_write (struct file *filep, const char *buffer,
                                size_t buflen)
{
    return psock_send (filep->f_priv, buffer, buflen, 0);
}

static int sock_file_ioctl (struct file *filep, unsigned int cmd, unsigned long arg)
{
    return psock_ioctl (filep->f_priv, cmd, arg);
}

static int sock_file_poll (struct file *filep, struct kpollfd *fds, bool setup)
{
    return psock_poll (filep->f_priv, fds, setup);
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: sockfd_allocate
 *
 * Description:
 *   Allocate a socket descriptor
 *
 * Input Parameters:
 *   psock    A pointer to socket structure.
 *   oflags   Open mode flags.
 *
 * Returned Value:
 *   Allocate a struct files instance and associate it with an socket
 *   instance.  Returns the file descriptor == index into the files array.
 *
 ****************************************************************************/

int sockfd_allocate (struct socket *psock, int oflags, int is_kernel)
{
    struct inode *sock_inode;
    int           sockfd;

    /* 申请当前新建socket的inode */
    sock_inode = calloc (1, sizeof (struct inode));
    if (NULL == sock_inode)
    {
        errno = -ENOMEM;
        return -1;
    }
    sock_inode->i_flags = FSNODEFLAG_TYPE_SOCKET;
    sock_inode->u.i_ops = &g_sock_fileops;

    sockfd = files_allocate (sock_inode, oflags, 0, psock, 0, is_kernel);

    if (sockfd >= 0)
    {
        /* 创建成功后增加一次引用计数 */
        inode_addref (sock_inode);
    }

    return sockfd;
}

/****************************************************************************
 * Name: sockfd_socket
 *
 * Description:
 *   Given a socket descriptor, return the underlying socket structure.
 *
 * Input Parameters:
 *   sockfd - The socket descriptor index to use.
 *
 * Returned Value:
 *   On success, a reference to the socket structure associated with the
 *   the socket descriptor is returned.  NULL is returned on any failure.
 *
 ****************************************************************************/

int file_socket (struct file *filep, struct socket **sock)
{
    if (filep != NULL && filep->f_inode != NULL
        && INODE_IS_SOCKET (filep->f_inode))
    {
        *sock = (struct socket *)filep->f_priv;
        return 0;
    }

    *sock = NULL;
    return -ENOTSOCK;
}

int sockfd_socket (int sockfd, struct socket **sock)
{
    struct file *filep;
    int          ret;

    ret = fs_getfilep (sockfd, &filep);
    if (ret < 0)
    {
        *sock = NULL;
        return -EBADF;
    }

    return file_socket (filep, sock);
}

int kernel_sockfd_socket (int sockfd, struct socket **sock)
{
    struct file *filep;
    int          ret;

    ret = kernel_fs_getfilep (sockfd, &filep);
    if (ret < 0)
    {
        *sock = NULL;
        return -EBADF;
    }

    return file_socket (filep, sock);
}