#include <errno.h>
#include <fcntl.h>
#include <limits.h>
#include <page.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <system/macros.h>
#include <time.h>
#include <ttosProcess.h>
#include <unistd.h>

#include <fs/statfs.h>
#include <sys/statfs.h>
#include <ttos_init.h>

#include "fs_ramfs.h"

#include <klog.h>
#undef KLOG_TAG
#define KLOG_TAG "RAMFS"

#define RAMFS_MAX_RECURSIVE_DEPTH 8

#define ramfs_lock(fs)                                                                             \
    do                                                                                             \
    {                                                                                              \
        int __ret = TTOS_ObtainMutex(fs->lock, TTOS_WAIT_FOREVER);                                 \
        if (__ret != TTOS_OK)                                                                      \
        {                                                                                          \
            KLOG_E("%s %d, ramfs lock obtain error, ret %d", __func__, __LINE__, __ret);           \
            return __ret;                                                                          \
        }                                                                                          \
    } while (0)

#define ramfs_unlock(fs)                                                                           \
    do                                                                                             \
    {                                                                                              \
        TTOS_ReleaseMutex(fs->lock);                                                               \
    } while (0)

int vfs_ramfs_bind(struct inode *mountpt, const void *data, void **handle)
{
    if (!handle)
    {
        return -EINVAL;
    }

    return ramfs_bind(data, handle);
}

int vfs_ramfs_unbind(void *handle, struct inode **blkdriver, unsigned int flags)
{
    int ret = 0;
    ramfs_t *fs = (ramfs_t *)handle;

    if (!fs)
    {
        return -EINVAL;
    }

    /* 销毁ramfs */
    ramfs_lock(fs);
    ramfs_unbind(fs);
    ramfs_unlock(fs);

    TTOS_DeleteMutex(fs->lock);

    free(fs);
    fs = NULL;

    return 0;
}

int vfs_ramfs_open(struct file *filep, const char *path, int flags, mode_t mode)
{
    ramfs_t *fs;
    struct inode *inode;
    ramfs_file_t *file = NULL;

    int ret = 0;

    if (!filep)
    {
        KLOG_E("%s %d, filep is NULL", __func__, __LINE__);
        return -EINVAL;
    }

    inode = filep->f_inode;

    if (inode == NULL || inode->i_private == NULL)
    {
        KLOG_E("%s %d, inode or i_private is NULL", __func__, __LINE__);
        return -EINVAL;
    }

    fs = inode->i_private;

    ramfs_lock(fs);

    ret = ramfs_open(fs, path, flags, mode, &file);
    if (ret || file == NULL)
    {
        KLOG_E("%s %d, open file %s failed, ret %d", __func__, __LINE__, path, ret);
        ramfs_unlock(fs);
        return ret;
    }

    filep->f_priv = file;
    filep->f_pos = file->offset;
    file->offset = 0;

    ramfs_unlock(fs);
    return 0;
}

int vfs_ramfs_close(struct file *filep)
{
    if (!filep || !filep->f_priv)
    {
        return -EINVAL;
    }

    ramfs_file_t *file;
    int ret = 0;
    ramfs_t *fs;

    file = (ramfs_file_t *)filep->f_priv;
    if (!file)
    {
        KLOG_E("%s %d, file is NULL", __func__, __LINE__);
        return -EINVAL;
    }

    fs = filep->f_inode->i_private;
    if (!fs)
    {
        KLOG_E("%s %d, fs is NULL", __func__, __LINE__);
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_close(file);
    if (ret)
    {
        KLOG_E("%s %d, close file failed, ret %d", __func__, __LINE__, ret);
        ramfs_unlock(fs);
        return ret;
    }

    ramfs_unlock(fs);

    filep->f_inode = NULL;
    // filep->f_priv = NULL;
    filep->f_pos = 0;

    return 0;
}

ssize_t vfs_ramfs_read(struct file *filep, char *buffer, size_t size)
{
    ramfs_file_t *file;
    ssize_t ret = 0;

    if (!filep || !buffer)
    {
        return -EINVAL;
    }

    file = (ramfs_file_t *)filep->f_priv;

    if (!file)
    {
        return -EINVAL;
    }

    ret = ramfs_read_pages(file->node, filep->f_pos, buffer, size);

    if (ret > 0)
    {
        // 直接更新内核管理的偏移
        filep->f_pos += ret;
    }
    else
    {
        KLOG_E("%s %d, read file failed, ret %d", __func__, __LINE__, ret);
    }

    return ret;
}

ssize_t vfs_ramfs_write(struct file *filep, const char *buffer, size_t size)
{
    ramfs_file_t *file;
    ssize_t ret = 0;
    ramfs_t *fs;

    if (!filep || !buffer)
    {
        return -EINVAL;
    }

    file = (ramfs_file_t *)filep->f_priv;
    if (!file)
    {
        return -EBADF;
    }

    fs = filep->f_inode->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    file->offset = filep->f_pos;

    ret = ramfs_write(file, buffer, size);
    if (ret > 0)
    {
        filep->f_pos += ret;
    }
    else
    {
        KLOG_E("%s %d, write file failed, ret %d", __func__, __LINE__, ret);
    }

    ramfs_unlock(fs);

    return ret;
}

off_t vfs_ramfs_seek(struct file *filep, off_t offset, int whence)
{
    ramfs_file_t *file;
    off_t new_offset;
    ramfs_t *fs;

    if (!filep)
    {
        return -EINVAL;
    }

    file = (ramfs_file_t *)filep->f_priv;
    if (!file)
    {
        return -EBADF;
    }

    fs = filep->f_inode->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    file->offset = filep->f_pos;

    new_offset = ramfs_seek(file, offset, whence);

    if (new_offset < 0)
    {
        KLOG_E("%s %d, seek failed, ret %d", __func__, __LINE__, new_offset);
        ramfs_unlock(fs);
        return -EINVAL;
    }

    filep->f_pos = new_offset;

    /* new_offset小于0时，不设置偏移，并返回错误码 */

    ramfs_unlock(fs);

    return new_offset;
}

int vfs_ramfs_truncate(struct file *filep, off_t length)
{
    ramfs_file_t *file;
    int ret = 0;
    ramfs_t *fs;

    if (!filep)
    {
        return -EINVAL;
    }

    file = (ramfs_file_t *)filep->f_priv;
    if (!file)
    {
        return -EBADF;
    }

    fs = filep->f_inode->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_truncate(file, length);
    if (ret)
    {
        KLOG_E("%s %d, truncate file failed, ret %d", __func__, __LINE__, ret);
    }

    ramfs_unlock(fs);

    return ret;
}
int vfs_ramfs_dup(const struct file *oldp, struct file *newp)
{
    ramfs_file_t *old_file, *new_file;
    int ret = 0;
    ramfs_t *fs;

    if (!oldp || !newp)
    {
        return -EINVAL;
    }

    old_file = (ramfs_file_t *)oldp->f_priv;
    if (!old_file)
    {
        KLOG_EMERG("%s %d, old_file is NULL", __func__, __LINE__);
        return -EINVAL;
    }

    fs = oldp->f_inode->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_dup(old_file, &new_file);
    if (ret)
    {
        return ret;
    }

    newp->f_priv = new_file;

    ramfs_unlock(fs);
    return 0;
}

/*************************************
 * 目录操作函数
 **************************************/

int vfs_ramfs_opendir(struct inode *mountpt, const char *path, struct fs_dirent_s **dir_out)
{
    ramfs_t *fs;
    ramfs_node_t *node;
    ramfs_dir_t *dir = NULL;
    int ret = 0;

    if (!mountpt || !path || !dir_out)
    {
        return -EINVAL;
    }

    fs = mountpt->i_private;

    ramfs_lock(fs);

    ret = ramfs_opendir(fs, path, &dir);
    if (ret)
    {
        ramfs_unlock(fs);
        return ret;
    }

    /* 将ramfs_dir_t 当作fs_dirent_s 保存，后续可以强转得到ramfs_dir_t */
    *dir_out = &dir->tf_base;

    ramfs_unlock(fs);

    return 0;
}

int vfs_ramfs_closedir(struct inode *mountpt, struct fs_dirent_s *dir)
{
    int ret = 0;
    ramfs_t *fs;

    if (!mountpt || !dir)
    {
        return -EINVAL;
    }

    fs = mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_closedir((ramfs_dir_t *)dir);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_readdir(struct inode *mountpt, struct fs_dirent_s *dir, struct dirent *entry)
{
    ramfs_dir_t *ramfs_dir;
    int ret = 0;
    ramfs_t *fs;

    if (!mountpt || !dir || !entry)
    {
        return -EINVAL;
    }

    fs = mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_dir = ((ramfs_dir_t *)dir);
    if (!ramfs_dir)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_readdir(fs, ramfs_dir, entry);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_mkdir(struct inode *mountpt, const char *path, mode_t mode)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !path)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_mkdir(fs, path, mode);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_rmdir(struct inode *mountpt, const char *path)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !path)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_rmdir(fs, path);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_stat(struct inode *mountpt, const char *path, struct stat *buf)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !path || !buf)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    /* 默认使用不跟踪符号链接的stat */
    ret = ramfs_lstat(fs, path, buf);

    ramfs_unlock(fs);

    return ret;
}

static int vfs_ramfs_chstat(struct inode *mountpt, const char *path, const struct stat *buf,
                            int flags)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !path || !buf)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_chstat(fs, path, buf, flags);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_unlink(struct inode *mountpt, const char *path)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !path)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_unlink(fs, path);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_rename(struct inode *mountpt, const char *oldpath, const char *newpath)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !oldpath || !newpath)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_rename(fs, oldpath, newpath);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_symlink(struct inode *mountpt, const char *target, const char *link_relpath)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !target || !link_relpath)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_symlink(fs, target, link_relpath);

    ramfs_unlock(fs);

    return ret;
}

ssize_t vfs_ramfs_readlink(struct inode *mountpt, const char *path, char *buf, size_t size)
{
    ramfs_t *fs;
    ssize_t ret = 0;

    if (!mountpt || !path || !buf || size == 0)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_readlink(fs, path, buf, size);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_statfs(struct inode *mountpt, struct statfs *buf)
{
    ramfs_t *fs;
    int ret = 0;

    if (!mountpt || !buf)
    {
        return -EINVAL;
    }

    fs = (ramfs_t *)mountpt->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_statfs(fs, buf);

    ramfs_unlock(fs);

    return ret;
}

int vfs_ramfs_fstat(const struct file *filep, struct stat *buf)
{
    ramfs_t *fs;
    int ret = 0;
    ramfs_file_t *file;

    if (!filep || !buf)
    {
        return -EINVAL;
    }

    file = (ramfs_file_t *)filep->f_priv;
    if (!file)
    {
        return -EBADF;
    }

    fs = filep->f_inode->i_private;
    if (!fs)
    {
        return -EINVAL;
    }

    ramfs_lock(fs);

    ret = ramfs_fstat(file, buf);

    ramfs_unlock(fs);

    return ret;
}

const struct mountpt_operations ramfs_operations = {
    vfs_ramfs_open,  /* open */
    vfs_ramfs_close, /* close */
    vfs_ramfs_read,  /* read */
    vfs_ramfs_write, /* write */
    vfs_ramfs_seek,  /* seek */
    NULL,            /* ioctl */
    NULL,            /* mmap */
    NULL,            /* poll */

    NULL,            /* sync */
    vfs_ramfs_dup,   /* dup */
    vfs_ramfs_fstat, /* fstat */
    NULL,            /* fchstat */

    vfs_ramfs_truncate, /* truncate */
    vfs_ramfs_opendir,  /* opendir */
    vfs_ramfs_closedir, /* closedir */
    vfs_ramfs_readdir,  /* readdir */
    NULL,               /* rewinddir */
    vfs_ramfs_bind,     /* bind */
    vfs_ramfs_unbind,   /* unbind */
    vfs_ramfs_statfs,   /* statfs */

    vfs_ramfs_unlink,   /* unlink */
    vfs_ramfs_mkdir,    /* mkdir */
    vfs_ramfs_rmdir,    /* rmdir */
    vfs_ramfs_rename,   /* rename */
    vfs_ramfs_stat,     /* stat */
    vfs_ramfs_chstat,   /* chstat */
    vfs_ramfs_readlink, /* readlink */
    vfs_ramfs_symlink,  /* symlink */
    NULL                /* link */
};
