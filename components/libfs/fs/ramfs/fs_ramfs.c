#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <limits.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <system/macros.h>
#include <time.h>
#include <ttosProcess.h>
#include <unistd.h>

#include <fs/statfs.h>
#include <sys/statfs.h>

#include "fs_ramfs.h"

#include <ttos_init.h>

#include <klog.h>
#undef KLOG_TAG
#define KLOG_TAG "RAMFS"

// #define ramfs_lock(fs) TTOS_ObtainMutex(fs->ramfs_lock, TTOS_WAIT_FOREVER)
// #define ramfs_unlock(fs) TTOS_ReleaseMutex(fs->ramfs_lock)

// #define ramfs_lock(fs) 0
// #define ramfs_unlock(fs) 0

/* 创建节点 */
// static ramfs_node_t *ramfs_create_node(ramfs_node_type_t type, mode_t mode)
ramfs_node_t *ramfs_create_node(ramfs_node_type_t type, mode_t mode)
{
    ramfs_node_t *node = (ramfs_node_t *)malloc(sizeof(ramfs_node_t));
    if (!node)
    {
        return NULL;
    }

    memset(node, 0, sizeof(ramfs_node_t));

    node->type = type;
    node->mode = mode;
    node->uid = 0; /* 默认所有者为root */
    node->gid = 0; /* 默认组为root */
    node->size = 0;

    time_t now = time(NULL);
    node->atime = now;
    node->mtime = now;
    node->ctime = now;

    atomic_set(&node->ref_count, 1);

    /* 根据节点类型进行特定初始化 */
    switch (type)
    {
    case RAMFS_TYPE_FILE:
        node->file.first_page = NULL;
        node->file.num_pages = 0;
        break;

    case RAMFS_TYPE_DIR:
        node->dir.first_entry = NULL;
        break;

    case RAMFS_TYPE_SYMLINK:
        node->symlink.target = NULL;
        break;
    }

    return node;
}

// static int ramfs_add_dirent(ramfs_node_t *dir_node, const char *name, ramfs_node_t *node)
int ramfs_add_dirent(ramfs_node_t *dir_node, const char *name, ramfs_node_t *node)
{
    int name_len;
    ramfs_dirent_t *tmp_entry = NULL, *new_entry;

    if (!dir_node || dir_node->type != RAMFS_TYPE_DIR || !name || !node)
    {
        return -EINVAL;
    }

    /* 检查目录中是否已存在同名条目 */
    tmp_entry = dir_node->dir.first_entry;
    while (tmp_entry)
    {
        if (strcmp(tmp_entry->name, name) == 0)
        {
            return -EEXIST;
        }
        tmp_entry = tmp_entry->next;
    }

    name_len = strlen(name);
    new_entry = (ramfs_dirent_t *)malloc(sizeof(ramfs_dirent_t));
    if (!new_entry)
    {
        return -ENOMEM;
    }

    new_entry->name = (char *)malloc(name_len + 1);
    if (!new_entry->name)
    {
        free(new_entry);
        return -ENOMEM;
    }

    memcpy(new_entry->name, name, name_len);
    new_entry->name[name_len] = '\0';

    /* 增加引用计数 */
    atomic_inc(&node->ref_count);

    new_entry->node = node;
    new_entry->next = NULL;

    /* 找到最后一个节点，并插入new_entry */
    ramfs_dirent_t **p_entry = &dir_node->dir.first_entry;
    while (*p_entry)
    {
        p_entry = &(*p_entry)->next;
    }
    *p_entry = new_entry;

    dir_node->mtime = time(NULL);

    return 0;
}

static void ramfs_destroy_node(ramfs_node_t *node)
{
    if (!node)
    {
        return;
    }

    /* 根据节点类型释放资源 */
    switch (node->type)
    {
    case RAMFS_TYPE_FILE:
        ramfs_free_all_pages(node);
        break;

    case RAMFS_TYPE_DIR:
        /* 递归释放所有目录条目 */
        while (node->dir.first_entry)
        {
            ramfs_dirent_t *entry = node->dir.first_entry;
            node->dir.first_entry = entry->next;

            /* 如果是"."条目，跳过减少引用计数 */
            if (strcmp(entry->name, ".") == 0)
            {
            }
            else if (strcmp(entry->name, "..") == 0)
            {
            }
            else
            {
                /* 减少节点引用计数，如果引用计数为0，可以释放节点 */
                if (atomic_dec_return(&entry->node->ref_count) == 0)
                {
                    ramfs_destroy_node(entry->node);
                }
            }

            free(entry->name);
            free(entry);
        }
        break;

    case RAMFS_TYPE_SYMLINK:
        if (node->symlink.target)
        {
            free(node->symlink.target);
        }
        break;
    }

    free(node);
    return;
}

/* 删除目录条目 */
// static int ramfs_remove_dirent(ramfs_node_t *dir_node, const char *name)
int ramfs_remove_dirent(ramfs_node_t *dir_node, const char *name)
{
    if (!dir_node || dir_node->type != RAMFS_TYPE_DIR || !name)
    {
        return -EINVAL;
    }

    ramfs_dirent_t **p_entry = &dir_node->dir.first_entry;
    ramfs_dirent_t *entry;

    while (*p_entry)
    {
        entry = *p_entry;
        if (strcmp(entry->name, name) == 0)
        {
            /* 解除链表 */
            *p_entry = entry->next;

            /* 减少节点引用计数，如果引用计数为0，可以释放节点 */
            if (atomic_dec_return(&entry->node->ref_count) == 0)
            {
                ramfs_destroy_node(entry->node);
                free(entry->name);
                free(entry);
            }

            /* 更新目录时间 */
            dir_node->mtime = time(NULL);

            return 0;
        }
        p_entry = &entry->next;
    }

    return -ENOENT;
}

/* 查找目录条目 */
// static ramfs_dirent_t *ramfs_find_dirent(ramfs_node_t *dir_node, const char *name)
ramfs_dirent_t *ramfs_find_dirent(ramfs_node_t *dir_node, const char *name)
{
    if (!dir_node || dir_node->type != RAMFS_TYPE_DIR || !name)
    {
        return NULL;
    }

    ramfs_dirent_t *entry = dir_node->dir.first_entry;
    while (entry)
    {
        if (strcmp(entry->name, name) == 0)
        {
            return entry;
        }
        entry = entry->next;
    }

    return NULL;
}

/***********************************
 * 路径处理函数
 ***********************************/

/**
 * 删除路径中多余的 /
 * 例如
 * "//dir1////dir2///" --> "/dir1/dir2",
 * "///" --> "/".
 * */
static void ramfs_normalize_path(char *path)
{
    int i, j = 0;

    for (i = 0; path[i] != '\0'; i++)
    {
        if (path[i] != '/' || j < 1 || path[j - 1] != '/')
            path[j++] = path[i];
    }

    if (j > 1 && path[j - 1] == '/')
        j--;
    path[j] = '\0';
}

/**
 * 在不访问文件系统的情况下，将任意绝对或相对路径规范化到 resolved 中
 * @param path     用户输入路径（绝对或相对）
 * @param resolved 调用者分配缓冲区，大小 >= PATH_MAX
 * @return         resolved 或 NULL（出错时 errno 已设置）
 */
int ramfs_realpath(const char *path, char *resolved_path)
{
    if (!path || !resolved_path)
        return -1;

    // 构建绝对路径（cwd = "/"）
    char abs_path[PATH_MAX];
    if (path[0] != '/')
    {
        if (snprintf(abs_path, sizeof(abs_path), "/%s", path) >= PATH_MAX)
            return -1;
    }
    else
    {
        strncpy(abs_path, path, sizeof(abs_path) - 1);
        abs_path[sizeof(abs_path) - 1] = '\0';
    }

    // 开始规范化路径
    char *stack[PATH_MAX / 2]; // 最多 PATH_MAX/2 层
    int depth = 0;

    char *saveptr;
    char *token;
    token = strtok_r(abs_path, "/", &saveptr);
    while (token)
    {
        if (strcmp(token, ".") == 0)
        {
            // 忽略
        }
        else if (strcmp(token, "..") == 0)
        {
            /* 处理 ".."，回退一层 */
            if (depth > 0)
                depth--;
        }
        else
        {
            stack[depth++] = token;
        }
        token = strtok_r(NULL, "/", &saveptr);
    }

    // 重新组装规范路径
    resolved_path[0] = '\0';
    if (depth == 0)
    {
        strcpy(resolved_path, "/");
        return 0;
    }

    for (int i = 0; i < depth; i++)
    {
        strcat(resolved_path, "/");
        strcat(resolved_path, stack[i]);
    }

    return 0;
}

/**
 * 分离最后一级目录和父目录路径
 */
int split_path(const char *path_in, char *parent_out, char *base_out)
{
    const char *last_slash;

    if (!path_in || !parent_out || !base_out)
        return -EINVAL;

    last_slash = strrchr(path_in, '/');

    if (!last_slash)
    {
        // 当前目录下的文件
        strcpy(parent_out, ".");
        strncpy(base_out, path_in, NAME_MAX);
    }
    else if (last_slash == path_in)
    {
        // 根目录
        strcpy(parent_out, "/");
        strncpy(base_out, path_in + 1, NAME_MAX);
    }
    else
    {
        // 一般情况
        size_t parent_len = last_slash - path_in;
        strncpy(parent_out, path_in, parent_len);
        parent_out[parent_len] = '\0';

        strncpy(base_out, last_slash + 1, NAME_MAX);
    }

    parent_out[PATH_MAX - 1] = '\0';
    base_out[NAME_MAX - 1] = '\0';

    return 0;
}

static ramfs_node_t *ramfs_lookup_internal(ramfs_t *fs, char *path, int depth);

static ramfs_node_t *ramfs_lookup(ramfs_t *fs, const char *path)
{
    char *path_copy;
    ramfs_node_t *res = NULL;

    path_copy = strdup(path);
    if (!path_copy)
    {
        return NULL;
    }
    res = ramfs_lookup_internal(fs, path_copy, 0);
    free(path_copy);

    return res;
}

static ramfs_dirent_t *ramfs_get_entry_by_path(ramfs_t *fs, const char *path)
{
    ramfs_dirent_t *entry;

    ramfs_node_t *cur_node = fs->root;
    char *remaining = NULL;
    char *next_part = NULL;

    char tmp[PATH_MAX] = {0};
    strcpy(tmp, path);

    ramfs_normalize_path(tmp);

    char abspath[PATH_MAX] = {0};
    ramfs_realpath(tmp, abspath);

    char *cur_part = strtok_r(abspath, "/", &remaining);
    while (cur_part)
    {
        next_part = strtok_r(NULL, "/", &remaining);

        entry = ramfs_find_dirent(cur_node, cur_part);
        if (!entry)
        {
            /* 找不到目录条目 */
            return NULL;
        }

        cur_node = entry->node;

        /* 如果不是最后一个组件，当前节点必须是目录 */
        if (cur_node->type != RAMFS_TYPE_DIR)
        {
            return NULL;
        }

        cur_part = next_part;
    }

    return entry;
}

static int ramfs_prepare_path(const char *path, char parent_out[PATH_MAX], char name_out[NAME_MAX])
{
    char normalized[PATH_MAX];
    char full_path[PATH_MAX];
    int ret = 0;

    if (!path || !parent_out || !name_out)
        return -EINVAL;

    strncpy(normalized, path, PATH_MAX);

    ramfs_normalize_path(normalized);

    if (strcmp(normalized, "/") == 0)
    {
        strcpy(parent_out, "/");
    }

    /* 转为绝对路径 */
    if (ramfs_realpath(normalized, full_path))
    {
        KLOG_E("%s:%d ramfs_realpath(%s) failed", __func__, __LINE__, normalized);
        return -ENOENT;
    }

    /**
     * 拆分父目录 + 自身文件名
     * todo 仅适用于a/b形式的路径，多级路径拆分不正确。后期改为ramfs_get_entry_by_path接口
     * */
    ret = split_path(full_path, parent_out, name_out);
    if (ret)
    {
        KLOG_E("%s:%d split_path(%s) failed", __func__, __LINE__, full_path);
        return ret;
    }

    return 0;
}
static ramfs_node_t *ramfs_lookup_path_parent(ramfs_t *fs, const char *path)
{
    int ret = 0;

    if (!fs || !path)
    {
        return NULL;
    }

    /* 处理根目录 */
    if (strcmp(path, "/") == 0)
    {
        return fs->root;
    }

    char parent_path[PATH_MAX] = "";
    char file_name[NAME_MAX] = "";
    ret = ramfs_prepare_path(path, parent_path, file_name);
    if (ret)
    {
        KLOG_E("%s:%d prepare path %s failed, ret %d", __func__, __LINE__, path, ret);
        return NULL;
    }

    return ramfs_lookup(fs, parent_path);
}

static ramfs_node_t *ramfs_lookup_path_self(ramfs_t *fs, const char *path)
{
    int ret = 0;
    ramfs_node_t *dir_node;
    ramfs_dirent_t *entry;

    if (!fs || !path)
    {
        return NULL;
    }

    /* 处理根目录 */
    if (strcmp(path, "/") == 0)
    {
        return fs->root;
    }

    char parent_path[PATH_MAX] = "";
    char file_name[NAME_MAX] = "";
    ret = ramfs_prepare_path(path, parent_path, file_name);
    if (ret)
    {
        KLOG_E("%s:%d prepare path %s failed, ret %d", __func__, __LINE__, path, ret);
        return NULL;
    }

    dir_node = ramfs_lookup(fs, parent_path);
    if (!dir_node || dir_node->type != RAMFS_TYPE_DIR)
    {
        /* 父目录不存在 */
        KLOG_E("%s:%d no dir entry %s", __func__, __LINE__, parent_path);
        ret = -ENOENT;
        return NULL;
    }

    entry = ramfs_find_dirent(dir_node, file_name);
    if (!entry)
    {
        return NULL;
    }

    return entry->node;
}

static ramfs_node_t *ramfs_lookup_internal(ramfs_t *fs, char *path, int depth)
{
    int ret = 0;

    if (!fs || !path)
    {
        return NULL;
    }

    /* 防止无限递归 */
    if (depth >= RAMFS_MAX_RECURSIVE_DEPTH)
    {
        return NULL;
    }

    /* 删除路径中多余的斜杠 */
    ramfs_normalize_path(path);

    /* 处理根目录 */
    if (strcmp(path, "/") == 0)
    {
        return fs->root;
    }

    /* 如果不是根目录，则跳过开头的斜杠 */
    char path_copy[PATH_MAX];
    strcpy(path_copy, path);

    char *pathptr = path_copy;
    if (*pathptr == '/')
    {
        pathptr++;
    }

    ramfs_node_t *cur_node = fs->root;
    char *remaining = NULL;
    char *next_part = NULL;

    char *cur_part = strtok_r(pathptr, "/", &remaining);
    while (cur_part)
    {
        next_part = strtok_r(NULL, "/", &remaining);

        ramfs_dirent_t *entry = ramfs_find_dirent(cur_node, cur_part);
        if (!entry)
        {
            /* 找不到目录条目 */
            return NULL;
        }

        cur_node = entry->node;

        if (cur_node->type == RAMFS_TYPE_SYMLINK)
        {
            /* 控制是否解析真实的链接文件 */
            char resolved_path[PATH_MAX] = {0};

            /* 解析符号链接：前半部分由target替换，替换为 [target/next_part] */
            sprintf(resolved_path, "%s/%s", cur_node->symlink.target, next_part);

            /* 递归解析新的符号链接路径 */
            ramfs_node_t *res = ramfs_lookup_internal(fs, resolved_path, depth + 1);
            return res;
        }

        /* 如果不是最后一个组件，当前节点必须是目录 */
        if (cur_node->type != RAMFS_TYPE_DIR)
        {
            return NULL;
        }

        cur_part = next_part;
    }

    return cur_node;
}

int ramfs_open(ramfs_t *fs, const char *path, int flags, mode_t mode, ramfs_file_t **file_out)
{
    int ret = 0;

    if (!fs || !path || !file_out)
    {
        return -EINVAL;
    }

    if (strlen(path) >= PATH_MAX)
    {
        return -ENAMETOOLONG;
    }

    /* 统一转换为绝对路径 */
    char full_path[PATH_MAX];
    if (ramfs_realpath(path, full_path))
    {
        KLOG_E("%s:%d convert path %s failed", __func__, __LINE__, path);
        ret = -ENOENT;
        return ret;
    }

    /* 分离父目录和最后一级目录 */
    char parent_path[PATH_MAX] = "";
    char file_name[NAME_MAX] = "";
    ret = split_path(full_path, parent_path, file_name);
    if (ret)
    {
        return ret;
    }

    ramfs_node_t *dir_node, *this_node = NULL;
    ramfs_dirent_t *entry;

    dir_node = ramfs_lookup(fs, parent_path);
    if (!dir_node || dir_node->type != RAMFS_TYPE_DIR)
    {
        /* 父目录不存在 */
        KLOG_E("%s:%d no dir entry %s", __func__, __LINE__, parent_path);
        ret = -ENOENT;
        return ret;
    }

    entry = ramfs_find_dirent(dir_node, file_name);

    if (entry == NULL)
    {
        /* 文件不存在 */
        if (!(flags & O_CREAT))
        {
            /* 不存在且不需要创建 */
            KLOG_E("%s:%d file %s doesn't exist", __func__, __LINE__, file_name);
            ret = -ENOENT;
            return ret;
        }

        this_node = ramfs_create_node(RAMFS_TYPE_FILE, mode);
        if (!this_node)
        {
            KLOG_E("%s %d, ramfs_create node [%s] failed", __func__, __LINE__, file_name);
            ret = -ENOMEM;
            return ret;
        }

        ret = ramfs_add_dirent(dir_node, file_name, this_node);
        if (ret)
        {
            KLOG_E("%s %d, ramfs add node [%s] to its parent failed", __func__, __LINE__,
                   file_name);
            ramfs_destroy_node(this_node);
            return ret;
        }
    }
    else
    {
        /* 文件存在 */
        this_node = entry->node;

        /* 文件存在，但是为目录 */
        if (this_node->type == RAMFS_TYPE_DIR)
        {
            KLOG_E("%s %d, [%s] is dir", __func__, __LINE__, file_name);
            ret = -EISDIR;
            return ret;
        }

        /* 文件存在但指定了O_EXCL */
        if ((flags & O_CREAT) && (flags & O_EXCL))
        {
            ret = -EEXIST;
            return ret;
        }

        /* 处理截断 */
        if (flags & O_TRUNC)
        {
            ramfs_truncate_pages(this_node, 0);
        }
    }

    /* 使用 this_node 处理文件 */
    if (this_node->type == RAMFS_TYPE_SYMLINK)
    {
        /* 不跟踪链接文件，只打开自身 */
        if (flags & O_NOFOLLOW)
        {
            /* 正常执行后续代码 */
        }
        else
        {
            /* 打开链接指向的真实文件 */
            ret = ramfs_open(fs, this_node->symlink.target, flags & ~O_CREAT & ~O_EXCL, mode,
                             file_out);
            return ret;
        }
    }

    ramfs_file_t *res = (ramfs_file_t *)malloc(sizeof(ramfs_file_t));
    if (!res)
    {
        /* 分配不出内存，则移除先前创建的文件 */
        KLOG_E("%s %d, malloc file node(%d byte) failed", __func__, __LINE__, sizeof(ramfs_file_t));
        // ramfs_remove_dirent(dir_node, file_name);
        if (!entry)
        {
            ramfs_remove_dirent(dir_node, file_name);
        }
        ret = -ENOMEM;
        return ret;
    }

    res->node = this_node;
    res->offset = 0;

    atomic_inc(&this_node->ref_count);

    this_node->atime = time(NULL);

    *file_out = res;

    return ret;
}

int ramfs_close(ramfs_file_t *file)
{
    if (!file)
    {
        return -EINVAL;
    }

    if (file->node)
    {
        /* 减少引用计数, 如果引用计数为0，可以释放节点 */
        if (atomic_dec_return(&file->node->ref_count) == 0)
        {
            ramfs_destroy_node(file->node);
            free(file);
        }
    }

    return 0;
}

ssize_t ramfs_read(ramfs_file_t *file, char *buffer, size_t size)
{
    if (!file || !buffer)
    {
        return -EINVAL;
    }
    int ret = 0;

    ret = ramfs_read_pages(file->node, file->offset, buffer, size);
    if (ret > 0)
    {
        file->offset += ret;
    }

    return ret;
}

ssize_t ramfs_write(ramfs_file_t *file, const char *buffer, size_t size)
{
    if (!file || !buffer)
    {
        return -EINVAL;
    }

    int ret = 0;
    ret = ramfs_write_pages(file->node, file->offset, buffer, size);
    if (ret > 0)
    {
        file->offset += ret;
    }

    return ret;
}

off_t ramfs_seek(ramfs_file_t *file, off_t offset, int whence)
{
    if (!file || offset < 0)
    {
        return -EINVAL;
    }

    off_t new_offset;

    switch (whence)
    {
    case SEEK_SET:
        new_offset = offset;
        break;

    case SEEK_CUR:
        new_offset = file->offset + offset;
        break;

    case SEEK_END:
        new_offset = file->node->size + offset;
        break;

    default:
        return -EINVAL;
    }

    file->offset = new_offset;
    return new_offset;
}

int ramfs_truncate(ramfs_file_t *file, off_t length)
{
    if (!file || length < 0)
    {
        return -EINVAL;
    }

    return ramfs_truncate_pages(file->node, length);
}

int ramfs_dup(ramfs_file_t *oldp, ramfs_file_t **newp)
{
    if (!oldp || !oldp->node || !newp)
    {
        return -EINVAL;
    }

    atomic_inc(&oldp->node->ref_count);

    *newp = oldp;
    return 0;
}

/*************************************
 * 目录操作函数
 ************************************/
int ramfs_opendir(ramfs_t *fs, const char *path, ramfs_dir_t **dir_out)
{
    if (!fs || !path || !dir_out)
    {
        return -EINVAL;
    }

    if (strlen(path) >= PATH_MAX)
    {
        return -ENAMETOOLONG;
    }

    ramfs_node_t *node = NULL;
    ramfs_dir_t *dir = NULL;

    node = ramfs_lookup(fs, path);
    if (!node)
    {
        return -ENOENT;
    }
    if (node->type != RAMFS_TYPE_DIR)
    {
        return -ENOTDIR;
    }

    dir = (ramfs_dir_t *)malloc(sizeof(ramfs_dir_t));
    if (!dir)
    {
        return -ENOMEM;
    }

    /* 保存到目录节点 */
    dir->node = node;
    dir->cur_entry = node->dir.first_entry;

    /* 更新节点信息 */
    atomic_inc(&node->ref_count);
    node->atime = time(NULL);

    *dir_out = dir;
    return 0;
}

int ramfs_closedir(ramfs_dir_t *dir)
{
    if (!dir)
    {
        return -EINVAL;
    }

    if (dir->node)
    {
        /* 减少引用计数，如果引用计数为0，可以释放节点 */
        if (atomic_dec_return(&dir->node->ref_count) == 0)
        {
            ramfs_destroy_node(dir->node);
        }
    }

    free(dir);
    return 0;
}

int ramfs_readdir(ramfs_t *fs, ramfs_dir_t *dir, struct dirent *entry)
{
    char *name;
    size_t size;

    if (!dir || !entry)
    {
        return -EINVAL;
    }

    name = entry->d_name;
    size = sizeof(entry->d_name);

    /* 检查是否到达目录末尾 */
    if (!dir->cur_entry)
    {
        return -ENOENT;
    }

    strncpy(name, dir->cur_entry->name, size - 1);
    name[size - 1] = '\0';

    /* 移动到下一个条目 */
    dir->cur_entry = dir->cur_entry->next;

    /* 设置文件类型 */
    ramfs_node_t *node = ramfs_lookup(fs, name);

    if (node)
    {
        switch (node->type)
        {
        case RAMFS_TYPE_FILE:
            entry->d_type = DT_REG;
            break;

        case RAMFS_TYPE_DIR:
            entry->d_type = DT_DIR;
            break;

        case RAMFS_TYPE_SYMLINK:
            entry->d_type = DT_LNK;
            break;

        default:
            entry->d_type = DT_UNKNOWN;
            break;
        }
    }
    else
    {
        entry->d_type = DT_UNKNOWN;
    }

    return 0;
}

int ramfs_mkdir(ramfs_t *fs, const char *path, mode_t mode)
{
    if (!path)
    {
        return -EINVAL;
    }

    if (strlen(path) >= PATH_MAX)
    {
        return -ENAMETOOLONG;
    }

    int ret = 0;

    /* 处理相对路径，转为绝对路径 */
    char full_path[PATH_MAX];
    if (ramfs_realpath(path, full_path))
    {
        return -ENOENT;
    }

    /* 分离最后一级目录和父目录路径 */
    char parent_path[PATH_MAX] = "";
    char dir_name[NAME_MAX];
    ret = split_path(full_path, parent_path, dir_name);
    if (ret)
    {
        return ret;
    }

    ramfs_node_t *parent = NULL, *dir_new;
    ramfs_dirent_t *entry = NULL;

    parent = ramfs_lookup(fs, parent_path);
    if (!parent || parent->type != RAMFS_TYPE_DIR)
    {
        return -ENOENT; /* 父目录不存在 */
    }

    entry = ramfs_find_dirent(parent, dir_name);
    if (entry)
    {
        return -EEXIST; /* 已存在同名条目 */
    }

    dir_new = ramfs_create_node(RAMFS_TYPE_DIR, mode);
    if (!dir_new)
    {
        return -ENOMEM;
    }

    ret = ramfs_add_dirent(parent, dir_name, dir_new);
    if (ret < 0)
    {
        ramfs_destroy_node(dir_new);
        return ret;
    }

    /* 添加"."和".."条目 */
    ret = ramfs_add_dirent(dir_new, ".", dir_new);
    ret = ramfs_add_dirent(dir_new, "..", parent);

    return 0;
}

static bool is_dir_empty(ramfs_dirent_t *dir_entry)
{
    bool empty = true;

    while (dir_entry)
    {
        if (strcmp(dir_entry->name, ".") != 0 && strcmp(dir_entry->name, "..") != 0)
        {
            empty = false;
            break;
        }
        dir_entry = dir_entry->next;
    }
    return empty;
}

int ramfs_rmdir(ramfs_t *fs, const char *path)
{
    if (!fs || !path)
    {
        return -EINVAL;
    }

    /* 不能删除根目录 */
    if (strcmp(path, "/") == 0)
    {
        return -EBUSY;
    }

    int ret = 0;

    /* 处理相对路径，转为绝对路径 */
    char full_path[PATH_MAX];
    if (ramfs_realpath(path, full_path))
    {
        return -ENOENT;
    }

    /* 分离最后一级目录和父目录路径 */
    char parent_path[PATH_MAX] = "";
    char dir_name[NAME_MAX];

    ret = split_path(full_path, parent_path, dir_name);
    if (ret)
    {
        return ret;
    }

    ramfs_node_t *parent = NULL;
    ramfs_dirent_t *entry_del = NULL, *entry_tmp = NULL;

    parent = ramfs_lookup(fs, parent_path);
    if (!parent || parent->type != RAMFS_TYPE_DIR)
    {
        /* 父目录不存在 */
        return -ENOENT;
    }

    entry_del = ramfs_find_dirent(parent, dir_name);
    if (!entry_del || entry_del->node->type != RAMFS_TYPE_DIR)
    {
        /* 要删除的目录不存在 */
        return -ENOENT;
    }

    /* 检查目录是否为空（除了"."和".."） */
    entry_tmp = entry_del->node->dir.first_entry;
    if (!is_dir_empty(entry_tmp))
    {
        /* 目录不为空，不能直接删除 */
        return -ENOTEMPTY;
    }

    return ramfs_remove_dirent(parent, dir_name);
}

/**
 * 基于当前路径，获取符号链接target的绝对路径
 * 例如：cur_path = "a/b/c", link = "d/e" --> out_buf = "a/b/d/e"
 */
static int ramfs_resolve_symlink_path(const char *cur_path, const char *link, char *out_buf,
                                      size_t size)
{
    /* 链接的目标是绝对路径，直接拷贝 */
    if (link[0] == '/')
    {
        strncpy(out_buf, link, size);
        return 0;
    }

    char *path = strdup(cur_path);
    if (!path)
    {
        return -ENOMEM;
    }

    char *last_slash = strrchr(path, '/');
    if (last_slash)
    {
        if (last_slash == path)
        {
            /* 根目录 */
            snprintf(out_buf, size, "/%s", link);
        }
        else
        {
            /* 一般目录情况 */
            *(last_slash + 1) = '\0';
            snprintf(out_buf, size, "%s%s", path, link);
        }
    }
    else
    {
        /* 没有斜杠，直接使用目标 */
        strncpy(out_buf, link, size - 1);
        out_buf[size - 1] = '\0';
    }

    printk("----> symlink abs path: %s\n", out_buf);

    free(path);
    return 0;
}

static void ramfs_save_stat(ramfs_node_t *node, struct stat *buf)
{
    if (!node || !buf)
    {
        return;
    }

    buf->st_mode = node->mode;
    buf->st_size = node->size;
    buf->st_uid = node->uid;
    buf->st_gid = node->gid;
    buf->st_atime = node->atime;
    buf->st_mtime = node->mtime;
    buf->st_ctime = node->ctime;

    /* 设置文件类型 */
    switch (node->type)
    {
    case RAMFS_TYPE_FILE:
        buf->st_mode |= S_IFREG;
        buf->st_nlink = 1;

        /* 普通文件同时计算块数量 */
        buf->st_blocks = (node->size + 511) / 512; /* 标准的512字节块计数 */
        buf->st_blksize = RAMFS_PAGE_SIZE;
        break;

    case RAMFS_TYPE_DIR:
        buf->st_mode |= S_IFDIR;
        /* 计算硬链接数,初始为2, 包含 "." 和目录本身 */
        buf->st_nlink = 2;

        ramfs_dirent_t *entry;
        for (entry = node->dir.first_entry; entry; entry = entry->next)
        {
            if (entry->node->type == RAMFS_TYPE_DIR && strcmp(entry->name, ".") != 0 &&
                strcmp(entry->name, "..") != 0)
            {
                buf->st_nlink++;
            }
        }
        break;

    case RAMFS_TYPE_SYMLINK:
        /* 通常不会执行到这里，因为符号链接会被跟踪 */
        buf->st_mode |= S_IFLNK;
        buf->st_nlink = 1;
        break;
    }

    return;
}

static int ramfs_stat_with_depth(ramfs_t *fs, char *path, struct stat *buf, int depth,
                                 bool follow_symlink)
{
    /* 防止循环符号链接导致无限递归 */
    if (depth > RAMFS_MAX_RECURSIVE_DEPTH)
    {
        KLOG_E("%s %d, recursive max depth %d\n", __func__, __LINE__, RAMFS_MAX_RECURSIVE_DEPTH);
        return -ELOOP;
    }

    int ret = 0;
    ramfs_node_t *node = NULL, *parent = NULL;
    ramfs_dirent_t *entry = NULL;

    /* 删除多余的 / */
    ramfs_normalize_path(path);

    char parent_path[PATH_MAX];
    char basename[NAME_MAX];
    ret = split_path(path, parent_path, basename);
    if (ret)
    {
        return ret;
    }

    parent = ramfs_lookup(fs, parent_path);
    if (!parent || parent->type != RAMFS_TYPE_DIR)
    {
        return -ENOENT;
    }

    entry = ramfs_find_dirent(parent, basename);
    if (!entry)
    {
        return -ENOENT;
    }

    node = entry->node;

    if (!node)
    {
        return -ENOENT;
    }

    /* 处理符号链接 */
    if (node->type == RAMFS_TYPE_SYMLINK && follow_symlink)
    {
        char resolved[PATH_MAX] = "";

        /* 获取符号链接的绝对路径 */
        ret = ramfs_resolve_symlink_path(path, node->symlink.target, resolved, sizeof(resolved));
        if (ret)
        {
            return ret;
        }

        /* 递归解析新的符号链接路径 */
        return ramfs_stat_with_depth(fs, resolved, buf, depth + 1, follow_symlink);
    }

    /* 填充 stat */
    memset(buf, 0, sizeof(struct stat));

    ramfs_save_stat(node, buf);

    node->atime = time(NULL);

    return 0;
}

/* 跟踪符号链接的stat */
int ramfs_stat(ramfs_t *fs, const char *path, struct stat *buf)
{
    if (!fs || !path || !buf)
    {
        return -EINVAL;
    }
    int ret = 0;
    char *path_copy;

    path_copy = strdup(path);
    ret = ramfs_stat_with_depth(fs, path_copy, buf, 0, true);
    free(path_copy);

    return ret;
}

/* 不跟随符号链接的stat */
int ramfs_lstat(ramfs_t *fs, const char *path, struct stat *buf)
{
    if (!fs || !path || !buf)
    {
        return -EINVAL;
    }
    int ret = 0;
    char *path_copy;

    path_copy = strdup(path);
    ret = ramfs_stat_with_depth(fs, path_copy, buf, 0, false);
    free(path_copy);

    return ret;
}

int ramfs_fstat(ramfs_file_t *file, struct stat *buf)
{
    if (!file || !file->node || !buf)
    {
        return -EINVAL;
    }

    memset(buf, 0, sizeof(struct stat));

    ramfs_save_stat(file->node, buf);

    return 0;
}

int ramfs_chstat(ramfs_t *fs, const char *path, const struct stat *buf, int flags)
{
    ramfs_node_t *node, *parent = NULL;
    ramfs_dirent_t *entry = NULL;
    int ret = 0;

    char parent_path[PATH_MAX];
    char basename[NAME_MAX];

    if (strcmp(path, "/") == 0)
    {
        return -EINVAL;
    }

    ret = split_path(path, parent_path, basename);
    if (ret)
    {
        return ret;
    }

    parent = ramfs_lookup(fs, parent_path);
    if (!parent || parent->type != RAMFS_TYPE_DIR)
    {
        return -ENOENT;
    }

    entry = ramfs_find_dirent(parent, basename);
    if (!entry)
    {
        return -ENOENT;
    }

    node = entry->node;

    node->mode = (node->mode & ~0777) | (buf->st_mode & 0777);

    /* 更新修改时间 */
    node->mtime = time(NULL);
    node->ctime = time(NULL);

    return 0;
}
#if 0
int ramfs_chmod(ramfs_t *fs, char *path, mode_t mode)
{
    if (!fs || !path)
    {
        return -EINVAL;
    }

    ramfs_node_t *node;

    /* 查找节点 */
    node = ramfs_lookup(fs, path);
    if (!node)
    {
        return -ENOENT;
    }

    /* 只修改权限位，保留文件类型位 */
    node->mode = (node->mode & ~0777) | (mode & 0777);

    /* 更新修改时间 */
    node->mtime = time(NULL);
    node->ctime = time(NULL);

    return 0;
}
#endif

int ramfs_unlink(ramfs_t *fs, const char *path)
{
    int ret = 0;

    if (!fs || !path)
    {
        return -EINVAL;
    }

    if (strcmp(path, "/") == 0)
    {
        return -EISDIR;
    }

    char parent_path[PATH_MAX];
    char basename[NAME_MAX];
    ret = split_path(path, parent_path, basename);
    if (ret)
    {
        return ret;
    }

    ramfs_node_t *parent;
    ramfs_dirent_t *entry;

    /* 查找父目录 */
    parent = ramfs_lookup(fs, parent_path);
    if (!parent)
    {
        ret = -ENOENT;
        return ret;
    }

    /* 查找要删除的节点 */
    entry = ramfs_find_dirent(parent, basename);
    if (!entry)
    {
        ret = -ENOENT; /* 文件不存在 */
        return ret;
    }

    if (entry->node->type == RAMFS_TYPE_DIR)
    {
        ret = -EISDIR; /* 是目录，不能用unlink删除 */
        return ret;
    }

    ret = ramfs_remove_dirent(parent, basename);

    return ret;
}

int ramfs_rename(ramfs_t *fs, const char *oldpath, const char *newpath)
{
    if (!fs || !oldpath || !newpath)
    {
        return -EINVAL;
    }

    if (strcmp(oldpath, "/") == 0)
    {
        return -EBUSY;
    }

    int ret = 0;
    ramfs_node_t *old_parent_node = NULL, *new_parent_node = NULL;
    ramfs_dirent_t *old_entry, *new_entry = NULL;

    char oldpath_resolved[PATH_MAX] = {0};
    if (ramfs_realpath(oldpath, oldpath_resolved))
    {
        KLOG_E("%s %d, realpath %s failed", __func__, __LINE__, oldpath);
        return -ENOENT;
    }

    /* 获取旧文件的目录名和文件名 */
    char old_dirname[PATH_MAX];
    char old_basename[NAME_MAX];
    ret = split_path(oldpath_resolved, old_dirname, old_basename);
    if (ret)
    {
        return ret;
    }

    /* 查找源父目录节点 */
    old_parent_node = ramfs_lookup(fs, old_dirname);
    if (!old_parent_node)
    {
        ret = -ENOENT;
        return ret;
    }

    /* 查找源文件节点 */
    old_entry = ramfs_find_dirent(old_parent_node, old_basename);
    if (!old_entry)
    {
        ret = -ENOENT;
        return ret;
    }

    /* 获取新文件的目录名和文件名 */
    char new_dirname[PATH_MAX];
    char new_basename[NAME_MAX];

    ret = split_path(newpath, new_dirname, new_basename);
    if (ret)
    {
        return ret;
    }

    new_parent_node = ramfs_lookup(fs, new_dirname);
    if (!new_parent_node)
    {
        ret = -ENOENT;
        return ret;
    }

    new_entry = ramfs_find_dirent(new_parent_node, new_basename);

    /* 如果新条目存在，则删除新条目 */
    if (new_entry)
    {
        /* 如果是同一个文件，不需要操作 */
        if (new_entry->node == old_entry->node)
        {
            return 0;
        }

        /* 如果新目标是目录，则必须为空 */
        if (new_entry->node->type == RAMFS_TYPE_DIR)
        {
            ramfs_dirent_t *dir_entry = new_entry->node->dir.first_entry;
            if (!is_dir_empty(dir_entry))
            {
                KLOG_E("%s %d, try to remove dir [%s] which is not empty\n", __func__, __LINE__,
                       dir_entry->name);
                ret = -ENOTEMPTY; /* 目录不为空 */
                return ret;
            }
        }

        ramfs_remove_dirent(new_parent_node, new_basename);
    }

    ramfs_remove_dirent(old_parent_node, old_basename);

    /* 添加新节点 */
    ramfs_add_dirent(new_parent_node, new_basename, old_entry->node);

    /* 如果是目录，更新".."条目，指向新的父目录 */
    if (old_entry->node->type == RAMFS_TYPE_DIR)
    {
        ramfs_dirent_t *entry = ramfs_find_dirent(old_entry->node, "..");
        if (entry)
        {
            /* 减少旧父目录的引用计数 */
            atomic_dec(&entry->node->ref_count);

            /* 指向新父目录 */
            entry->node = new_parent_node;

            /* 增加新父目录的引用计数 */
            atomic_inc(&new_parent_node->ref_count);
        }
    }

    return 0;
}

int ramfs_symlink(ramfs_t *fs, const char *target, const char *linkpath)
{
    if (!fs || !target || !linkpath)
    {
        return -EINVAL;
    }

    if (strlen(linkpath) >= PATH_MAX || strlen(target) >= PATH_MAX)
        return -ENAMETOOLONG;

    int ret = 0;
    size_t target_len;

    ramfs_node_t *parent = NULL, *sym_node = NULL;

    /* 分离最后一级目录和父目录路径 */
    char parent_path[PATH_MAX] = "";
    char link_name[NAME_MAX + 1] = "";

    ret = split_path(linkpath, parent_path, link_name);
    if (ret)
    {
        return ret;
    }

    /* 获取父目录的绝对路径 */
    char resolved_parent[PATH_MAX] = "";
    if (ramfs_realpath(parent_path, resolved_parent))
    {
        return -ENOENT;
    }

    /**
     * 解析target，得到绝对路径 resolved_target
     * 考虑target存储的两种情况：
     * 1. 如果是绝对路径，则可以直接调用ramfs_realpath解析
     * 2. 如果是相对路径，则基于linkpath的父目录解析，获得target的绝对路径
     */

    char resolved_target[PATH_MAX] = "";
    if (*target == '/')
    {
        if (ramfs_realpath(target, resolved_target))
        {
            return -ENOENT;
        }
    }
    else
    {
        char tmp[PATH_MAX] = "";
        snprintf(tmp, sizeof(tmp), "%s/%s", resolved_parent, target);
        if (ramfs_realpath(tmp, resolved_target))
        {
            return -ENOENT;
        }
    }

    /* 构造新建链接文件的绝对路径 */
    char full_path[PATH_MAX] = "";
    snprintf(full_path, sizeof(full_path), "%s/%s", resolved_parent, link_name);

    /* 检查循环链接指向自身，直接返回 */
    if (strcmp(resolved_target, full_path) == 0)
    {
        KLOG_E("%s %d, symlink %s point to self", __func__, __LINE__, full_path);
        return -ELOOP;
    }

    /* 查找父目录 */
    parent = ramfs_lookup(fs, parent_path);
    if (!parent)
    {
        return -ENOENT;
    }

    /* 不是目录 */
    if (parent->type != RAMFS_TYPE_DIR)
    {
        return -ENOTDIR;
    }

    /* 已存在同名条目 */
    if (ramfs_find_dirent(parent, link_name))
    {
        return -EEXIST;
    }

    /* 创建符号链接节点 */
    unsigned long symlink_mode = S_IFLNK | 0777;
    sym_node = ramfs_create_node(RAMFS_TYPE_SYMLINK, symlink_mode);
    if (!sym_node)
    {
        KLOG_E("%s create new symlink node failed\n", __func__);
        return -ENOMEM;
    }

#if 0
    /* 创建链接节点的同时，创建自身文件 */
    ramfs_file_t *self = NULL;
    ret = ramfs_open(fs, full_path, O_RDWR | O_CREAT, S_IFLNK, &self);
    if(ret)
    {
        KLOG_E("%s %d, open file [%s] failed, ret %d\n", __func__, __LINE__, full_path, ret);
        return ret;
    }
#endif
    /* 设置符号链接的目标路径 */
    target_len = strlen(resolved_target);
    sym_node->symlink.target = malloc(target_len + 1);

    if (!sym_node->symlink.target)
    {
        ret = -ENOMEM;
        KLOG_E("%s %d, malloc failed: %d byte\n", __func__, __LINE__, target_len + 1);
        goto ret_free_node;
    }

    memcpy(sym_node->symlink.target, resolved_target, target_len);
    sym_node->symlink.target[target_len] = '\0';

    /* 设置文件大小为目标路径长度 */
    sym_node->size = target_len;

    /* 将链接节点添加到父目录 */
    ret = ramfs_add_dirent(parent, link_name, sym_node);
    if (ret)
    {
        KLOG_E("%s %d, err ret %d\n", __func__, __LINE__, ret);
        goto ret_free_node;
    }

    return 0;

ret_free_node:
    ramfs_destroy_node(sym_node);
    return ret;
}

int ramfs_readlink(ramfs_t *fs, const char *path, char *buf, size_t size)
{
    if (!fs || !path || !buf || size == 0)
    {
        return -EINVAL;
    }

    if (strlen(path) >= PATH_MAX)
    {
        return -ENAMETOOLONG;
    }

    /* 查找符号链接节点 */
    ramfs_node_t *node;
    size_t target_len = 0;
    int ret = 0;

    char parent_path[PATH_MAX] = "";
    char basename[NAME_MAX] = "";

    ret = ramfs_prepare_path(path, parent_path, basename);
    if (ret)
    {
        return ret;
    }

    /* 查找父目录 */
    node = ramfs_lookup(fs, parent_path);
    if (!node || node->type != RAMFS_TYPE_DIR)
    {
        /* 父目录不存在 */

        /* todo dropbear解析是路径不存在的处理 */
        // ramfs_dirent_t *entry111 = ramfs_get_entry_by_path(fs, path);
        KLOG_E("%s:%d no dir entry %s", __func__, __LINE__, parent_path);
        return -ENOTDIR;
    }

    /* 查找自身目录 */
    ramfs_dirent_t *entry;
    entry = ramfs_find_dirent(node, basename);
    if (!entry || entry->node == NULL)
    {
        /**
         * 符号链接不存在
         * 这里是正常情况，因为vfs在打开文件时执行realpath, 尝试为每个文件都当作符号链接处理
         * */
        KLOG_D("%s:%d no symlink entry %s", __func__, __LINE__, basename);
        return -EINVAL;
    }

    node = entry->node;
    if (node->type != RAMFS_TYPE_SYMLINK)
    {
        /**
         * 不是符号链接
         * 同上，realpath会尝试将每个文件都当作符号链接处理，这里设置
         * */
        KLOG_D("%s:%d entry %s is not a symlink", __func__, __LINE__, basename);
        return -EINVAL;
    }

    /* 复制目标路径 */
    target_len = strlen(node->symlink.target);
    if (target_len >= size)
    {
        /* 如果目标太长，截断并确保有足够空间放置'\0' */
        target_len = size - 1;
    }

    memcpy(buf, node->symlink.target, target_len);
    buf[target_len] = '\0';

    /* 更新访问时间 */
    node->atime = time(NULL);

    /* 返回实际长度（不包括终止符） */
    return target_len;
}

int ramfs_statfs(ramfs_t *fs, struct statfs *buf)
{
    if (!fs || !buf)
    {
        return -EINVAL;
    }

    memset(buf, 0, sizeof(struct statfs));

    buf->f_type = RAMFS_MAGIC;
    buf->f_namelen = NAME_MAX;
    buf->f_bsize = RAMFS_PAGE_SIZE;
    buf->f_blocks = 0;
    buf->f_bfree = 0;
    buf->f_bavail = 0;
    buf->f_files = 0;
    buf->f_ffree = 0;

    return 0;
}

/* 销毁ramfs */
static void ramfs_destroy(ramfs_t *fs)
{
    if (fs)
    {
        if (fs->root)
        {
            /* 根节点可能有多个引用，强制销毁 */
            atomic_set(&fs->root->ref_count, 1);
            ramfs_destroy_node(fs->root);
        }
    }
}

int ramfs_bind(const void *data, void **handle)
{
    ramfs_t *fs;
    int ret = 0;

    fs = (ramfs_t *)malloc(sizeof(ramfs_t));
    if (!fs)
    {
        return -ENOMEM;
    }

    /* 创建新目录节点 */
    fs->root = ramfs_create_node(RAMFS_TYPE_DIR, 0755);
    if (!fs->root)
    {
        return -ENOMEM;
    }

    /* 将新目录节点原有的根目录下 */
    ret = ramfs_add_dirent(fs->root, "", fs->root);
    if (ret)
        return ret;

    ret = ramfs_add_dirent(fs->root, ".", fs->root);
    if (ret)
        return ret;

    ret = ramfs_add_dirent(fs->root, "..", fs->root);
    if (ret)
        return ret;

    /* 创建并初始化互斥量 */
    TTOS_CreateMutex(1, 0, &fs->lock);

    /* todo bind 和 unbind 传入的handle 应该是mountpt，用于挂载tmpfs和shmfs */

    *handle = (void *)fs;

    return 0;
}

int ramfs_unbind(ramfs_t *fs)
{
    /* 销毁ramfs */
    ramfs_destroy(fs);

    return 0;
}

int ramfs_init(void)
{
    int ret = 0;
    const char *ramfs_path = "/ram";

    /* 如果ram路径不存在则创建 */
    if(vfs_access(ramfs_path, F_OK) != 0)
    {
        vfs_mkdir(ramfs_path, 0666);
    }
    ret = vfs_mount(NULL, ramfs_path, "ramfs", 0, NULL);
    if (ret < 0)
    {
        KLOG_E("ERROR: Failed to mount ramfs: %d\n", ret);
    }
    return ret;
}

INIT_EXPORT_SERVE_FS (ramfs_init, "ramfs init");