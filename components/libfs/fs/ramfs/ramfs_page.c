#include <errno.h>
#include <page.h>
#include <stdlib.h>
#include <system/macros.h>
#include <string.h>
#include <ttosProcess.h>


#include "fs_ramfs.h"

#undef KLOG_TAG
#define KLOG_TAG "RAMFS"
#include <klog.h>


extern int printk(const char *fmt, ...);

/* 分配一个新的内存页 */
ramfs_page_t *ramfs_alloc_page(void)
{
    ramfs_page_t *page = (ramfs_page_t *)malloc(sizeof(ramfs_page_t));
    if (!page)
    {
        return NULL;
    }

    page->data = (void *)page_address(pages_alloc(0, ZONE_NORMAL));
    if (!page->data)
    {
        free(page);
        return NULL;
    }

    /* 初始化为0 */
    memset(page->data, 0, RAMFS_PAGE_SIZE);
    page->next = NULL;

    return page;
}

/* 释放一个内存页 */
void ramfs_free_page(ramfs_page_t *page)
{
    if (page)
    {
        if (page->data)
        {
            free(page->data);
        }
        free(page);
    } 
}

/* 释放文件的所有内存页 */
void ramfs_free_all_pages(ramfs_node_t *node)
{
    if (!node)
    {
        return;
    }

    ramfs_page_t *page = node->file.first_page;
    while (page)
    {
        ramfs_page_t *next = page->next;
        ramfs_free_page(page);
        page = next;
    }

    node->file.first_page = NULL;
    node->file.num_pages = 0;
    node->size = 0;
}

/* 获取指定偏移的页和页内偏移 */
static int ramfs_get_page(ramfs_node_t *node, off_t offset, int allocate, ramfs_page_t **page_out,
                          size_t *page_offset_out)
{
    if (!node)
    {
        return -EINVAL;
    }

    size_t page_index = offset / RAMFS_PAGE_SIZE;
    size_t page_offset = offset % RAMFS_PAGE_SIZE;

    /* 找到对应页 */
    ramfs_page_t *page = node->file.first_page;
    ramfs_page_t *prev = NULL;
    size_t current_index = 0;

    /* 遍历链表查找指定页 */
    while (page && current_index < page_index)
    {
        prev = page;
        page = page->next;
        current_index++;
    }

    /* 如果页不存在且需要分配 */
    if (!page && allocate)
    {
        while (current_index <= page_index)
        {
            ramfs_page_t *new_page = ramfs_alloc_page();
            if (!new_page)
            {
                return -ENOMEM;
            }

            if (prev)
            {
                prev->next = new_page;
            }
            else
            {
                node->file.first_page = new_page;
            }

            prev = new_page;

            if (current_index == page_index)
            {
                page = new_page;
            }

            current_index++;
            node->file.num_pages++;
        }
    }

    if (!page && !allocate)
    {
        return -ENOENT;
    }

    if (page_out)
    {
        *page_out = page;
    }

    if (page_offset_out)
    {
        *page_offset_out = page_offset;
    }

    return 0;
}

/* 读取文件内容 */
ssize_t ramfs_read_pages(ramfs_node_t *node, off_t offset, char *buffer, size_t size)
{
    int ret = 0;
    if (!node || !buffer || offset < 0)
    {
        return -EINVAL;
    }

    size_t remaining = node->size - offset;
    if (remaining <= 0)
    {
        KLOG_D("%s EOF reach, remaining %d\n", __func__, remaining);
        return 0;  // EOF
    }

    size_t read_size = min(size, remaining);
    size_t bytes_read = 0;
    while (bytes_read < read_size)
    {
        ramfs_page_t *page;
        size_t page_offset;
        ret = ramfs_get_page(node, offset + bytes_read, 0, &page, &page_offset);
        if (ret < 0)
        {
            KLOG_E("%s %d, get page failed, ret=%d\n", __func__, __LINE__, ret);
            return ret;
        }

        /* 计算需要读取的大小 */
        size_t chunk_size = min((size_t)(RAMFS_PAGE_SIZE - page_offset), read_size - bytes_read);
        if(page && page->data)
        {
            memcpy(buffer + bytes_read, (char *)page->data + page_offset, chunk_size);
        }else{
            // 若页面未分配，返回已读字节（不继续填充0）
            break;
        }

        bytes_read += chunk_size;
    }

    /* 更新访问时间 */
    node->atime = time(NULL);
    // printk("READ DONE: offset=%ld, read_size=%zu, actually_read=%zu\n", offset, read_size, bytes_read);

    ret = bytes_read ? bytes_read : (read_size > 0 ? -EIO : 0);
    return ret;
}

/* 写入文件内容 */
ssize_t ramfs_write_pages(ramfs_node_t *node, off_t offset, const char *buffer, size_t size)
{
    if (!node || !buffer || offset < 0)
    {
        return -EINVAL;
    }

    /* 检查长度是否有效 */
    if (size == 0)
    {
        return 0;
    }

    /* 检查是否超过最大文件大小 (对于ramfs，通常是内存大小的限制) */
    if (offset > MAX_FILE_SIZE || size > MAX_FILE_SIZE - offset)
        return -EFBIG;

    size_t bytes_written = 0;
    while (bytes_written < size)
    {
        ramfs_page_t *page;
        size_t page_offset, chunk_size;
        int ret = 0;

        ret = ramfs_get_page(node, offset + bytes_written, 1, &page, &page_offset);
        if (ret < 0)
        {
            KLOG_E("%s %d, get page failed, ret=%d\n", __func__, __LINE__, ret);
            return ret;
        }

        if (!page || !page->data)
        {
            return bytes_written > 0 ? bytes_written : -EIO;
        }

        /* 计算本次写入大小 */
        chunk_size = min((size_t)(RAMFS_PAGE_SIZE - page_offset), size - bytes_written);

        /* 复制数据 */
        memcpy((char *)page->data + page_offset, buffer + bytes_written, chunk_size);
        bytes_written += chunk_size;
    }

    /* 更新文件大小 */
    node->size = max(node->size, (size_t)offset + bytes_written);

    /* 更新修改时间 */
    node->mtime = time(NULL);

    return bytes_written;
}

/* 截断文件 */
int ramfs_truncate_pages(ramfs_node_t *node, off_t length)
{
    if (!node)
    {
        return -EINVAL;
    }

    /* 如果新长度大于当前长度，分配新页并填充0 */
    if (length > node->size)
    {
        char *zeros = calloc(1, length - node->size);
        if (!zeros)
        {
            return -ENOMEM;
        }

        /* 在当前文件尾写入零填充 */
        int ret = ramfs_write_pages(node, node->size, zeros, length - node->size);
        free(zeros);

        if (ret < 0)
        {
            return ret;
        }
    }
    else if (length < node->size)
    {
        /* 释放多余的页 */
        size_t last_page_index = length / RAMFS_PAGE_SIZE;

        ramfs_page_t *page = node->file.first_page;
        ramfs_page_t *prev = NULL;
        size_t current_index = 0;

        /* 找到截断位置的页 */
        while (page && current_index < last_page_index)
        {
            prev = page;
            page = page->next;
            current_index++;
        }

        /* 如果不是页边界，保留最后一页 */
        if (length % RAMFS_PAGE_SIZE != 0)
        {
            if (page)
            {
                ramfs_page_t *next = page->next;
                page->next = NULL;
                page = next;
                current_index++;
            }
        }
        else if (prev)
        {
            prev->next = NULL;
        }
        else
        {
            node->file.first_page = NULL;
        }

        /* 释放后续所有页 */
        while (page)
        {
            ramfs_page_t *next = page->next;
            ramfs_free_page(page);
            page = next;
            node->file.num_pages--;
        }
    }

    /* 更新文件大小 */
    node->size = length;
    node->mtime = time(NULL);

    return 0;
}