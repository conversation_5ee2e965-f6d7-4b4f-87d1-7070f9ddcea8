#ifndef _RAMFS_VFS_H
#define _RAMFS_VFS_H

#include "fs_ramfs.h"

/* 初始化 ramfs */
ramfs_t *ramfs_init(void);

/* 销毁 ramfs */
void ramfs_destroy(ramfs_t *fs);

/* ramfs 节点操作函数 */
// ramfs_node_t *ramfs_create_node(ramfs_node_type_t type, mode_t mode);
// void ramfs_destroy_node(ramfs_node_t *node);

/* ramfs 文件操作函数 */
int ramfs_open(ramfs_t *fs, const char *path, int flags, mode_t mode, ramfs_file_t **file);
int ramfs_close(ramfs_file_t *file);
ssize_t ramfs_read(ramfs_file_t *file, char *buffer, size_t size);
ssize_t ramfs_write(ramfs_file_t *file, const char *buffer, size_t size);
off_t ramfs_seek(ramfs_file_t *file, off_t offset, int whence);
int ramfs_truncate(ramfs_file_t *file, off_t length);

/* ramfs 目录操作函数 */
int ramfs_opendir(ramfs_t *fs, const char *path, ramfs_dir_t **dir);
int ramfs_closedir(ramfs_dir_t *dir);
int ramfs_readdir(ramfs_dir_t *dir, char *name, size_t size);
int ramfs_mkdir(ramfs_t *fs, const char *path, mode_t mode);
int ramfs_rmdir(ramfs_t *fs, const char *path);

/* ramfs 文件系统操作函数 */
int ramfs_stat(ramfs_t *fs, const char *path, struct stat *buf);
int ramfs_lstat(ramfs_t *fs, const char *path, struct stat *buf);
int ramfs_unlink(ramfs_t *fs, const char *path);
int ramfs_rename(ramfs_t *fs, const char *oldpath, const char *newpath);
int ramfs_symlink(ramfs_t *fs, const char *target, const char *linkpath);
int ramfs_readlink(ramfs_t *fs, const char *path, char *buf, size_t size);
int ramfs_chmod(ramfs_t *fs, const char *path, mode_t mode);

/* ramfs 路径解析函数 */
ramfs_node_t *ramfs_lookup(ramfs_t *fs, const char *path, char **basename);

/* ramfs 页操作函数 */
ramfs_page_t *ramfs_alloc_page(void);
void ramfs_free_page(ramfs_page_t *page);
ssize_t ramfs_read_pages(ramfs_node_t *node, off_t offset, char *buffer, size_t size);
ssize_t ramfs_write_pages(ramfs_node_t *node, off_t offset, const char *buffer, size_t size);
int ramfs_truncate_pages(ramfs_node_t *node, off_t length);


#endif