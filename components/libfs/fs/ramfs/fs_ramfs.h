#ifndef __RAMFS_H__
#define __RAMFS_H__

#include <stddef.h>
#include <stdint.h>
#include <sys/stat.h>
#include <sys/types.h>

#include <atomic.h>
#include <fs/fs.h>
#include <mmu.h>

/* 页大小，可根据实际需要调整 */
#define RAMFS_PAGE_SIZE PAGE_SIZE

/* 查找的最大递归深度 */
#define RAMFS_MAX_RECURSIVE_DEPTH 8

// 自定义最大文件大小
#define MAX_FILE_SIZE ((1ULL << 40) - 1)

/* ramfs 文件系统节点类型 */
typedef enum
{
    RAMFS_TYPE_FILE = 0,
    RAMFS_TYPE_DIR,
    RAMFS_TYPE_SYMLINK
} ramfs_node_type_t;

/* 内存页结构 */
typedef struct ramfs_page
{
    void *data;              /* 页数据指针 */
    struct ramfs_page *next; /* 链表下一页 */
} ramfs_page_t;

/* ramfs 目录条目 */
typedef struct ramfs_dirent
{
    char *name;                /* 条目名称 */
    struct ramfs_node *node;   /* 指向的节点 */
    struct ramfs_dirent *next; /* 链表下一条目 */
} ramfs_dirent_t;

/* ramfs 节点结构 */
typedef struct ramfs_node
{
    ramfs_node_type_t type; /* 节点类型 */
    mode_t mode;            /* 文件权限 */
    uid_t uid;              /* 所有者ID */
    gid_t gid;              /* 组ID */
    size_t size;            /* 文件大小 */
    time_t atime;           /* 访问时间 */
    time_t mtime;           /* 修改时间 */
    time_t ctime;           /* 创建时间 */

    atomic_t ref_count; /* 引用计数 */

    union {
        /* 文件特有数据 */
        struct
        {
            ramfs_page_t *first_page; /* 第一个页指针 */
            size_t num_pages;         /* 总页数 */
        } file;

        /* 目录特有数据 */
        struct
        {
            ramfs_dirent_t *first_entry; /* 第一个目录条目 */
        } dir;

        /* 符号链接特有数据 */
        struct
        {
            char *target; /* 链接目标 */
        } symlink;
    };
} ramfs_node_t;

/* ramfs 文件句柄 */
typedef struct ramfs_file
{
    ramfs_node_t *node; /* 节点指针 */
    off_t offset;       /* 当前文件偏移 */
} ramfs_file_t;

/* ramfs 目录句柄 */
typedef struct ramfs_dir
{
    struct fs_dirent_s tf_base; /* VFS 目录结构体 */
    ramfs_node_t *node;         /* 目录节点 */
    ramfs_dirent_t *cur_entry;    /* 当前目录条目 */
} ramfs_dir_t;

/* ramfs 文件系统 */
typedef struct ramfs
{
    ramfs_node_t *root; /* 根目录节点 */
    MUTEX_ID lock;      /* 文件锁 */
} ramfs_t;

/* ramfs 文件操作函数 */
int ramfs_open(ramfs_t *fs, const char *path, int flags, mode_t mode, ramfs_file_t **file);
int ramfs_close(ramfs_file_t *file);
ssize_t ramfs_read(ramfs_file_t *file, char *buffer, size_t size);
ssize_t ramfs_write(ramfs_file_t *file, const char *buffer, size_t size);
off_t ramfs_seek(ramfs_file_t *file, off_t offset, int whence);
int ramfs_truncate(ramfs_file_t *file, off_t length);
int ramfs_dup(ramfs_file_t *oldp, ramfs_file_t **newp);

/* ramfs 目录操作函数 */
int ramfs_opendir(ramfs_t *fs, const char *path, ramfs_dir_t **dir);
int ramfs_closedir(ramfs_dir_t *dir);
int ramfs_readdir(ramfs_t *fs, ramfs_dir_t *dir, struct dirent *entry);
int ramfs_mkdir(ramfs_t *fs, const char *path, mode_t mode);
int ramfs_rmdir(ramfs_t *fs, const char *path);

/* ramfs 文件系统操作函数 */
int ramfs_stat(ramfs_t *fs, const char *path, struct stat *buf);
int ramfs_lstat(ramfs_t *fs, const char *path, struct stat *buf);
int ramfs_fstat(ramfs_file_t *file, struct stat *buf);
int ramfs_statfs(ramfs_t *fs, struct statfs *buf);
int ramfs_chstat(ramfs_t *fs, const char *path, const struct stat *buf, int flags);
int ramfs_unlink(ramfs_t *fs, const char *path);
int ramfs_rename(ramfs_t *fs, const char *oldpath, const char *newpath);
int ramfs_symlink(ramfs_t *fs, const char *target, const char *linkpath);
int ramfs_readlink(ramfs_t *fs, const char *path, char *buf, size_t size);
// int ramfs_chmod(ramfs_t *fs, char *path, mode_t mode);

int ramfs_bind(const void *data, void **handle);
int ramfs_unbind(ramfs_t *fs);

/* ramfs 页操作函数 */
ramfs_page_t *ramfs_alloc_page(void);
void ramfs_free_page(ramfs_page_t *page);
void ramfs_free_all_pages(ramfs_node_t *node);
ssize_t ramfs_read_pages(ramfs_node_t *node, off_t offset, char *buffer, size_t size);
ssize_t ramfs_write_pages(ramfs_node_t *node, off_t offset, const char *buffer, size_t size);
int ramfs_truncate_pages(ramfs_node_t *node, off_t length);

#endif /* __RAMFS_H__ */
