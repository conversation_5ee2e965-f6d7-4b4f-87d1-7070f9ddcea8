#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <mqueue.h>
#include <stdlib.h>
#include <ttos.h>

#include "../inode/inode.h"
#include "mqueue.h"

int _mq_setattr (mqd_t mqdes, struct mq_attr *new, struct mq_attr *old)
{
    struct file           *filep;
    int                    ret;
    struct mqueue_inode_s *msgq;

    ret = fs_getfilep (mqdes, &filep);
    if (ret < 0)
    {
        return ret;
    }

    if(!INODE_IS_MQUEUE(filep->f_inode))
    {
        return -EBADFD;
    }

    msgq = filep->f_inode->i_private;

    /* 获取旧的属性 */
    if (old != NULL)
    {
        memcpy (old, &(msgq->ttos_msgq->attr), sizeof (struct mq_attr));
    }

    /* 设置新属性 */
    if ((new != NULL) && (new->mq_flags & O_NONBLOCK))
    {
        msgq->ttos_msgq->attr.mq_flags |= O_NONBLOCK;
    }

    return 0;
}
