/****************************************************************************
 * fs/mqueue/mq_unlink.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <assert.h>
#include <errno.h>
#include <mqueue.h>
#include <stdbool.h>
#include <stdio.h>
#include <ttos.h>
#include <ttosInterHal.h>

#include "../inode/inode.h"
#include "mqueue.h"

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: mq_inode_release
 *
 * Description:
 *   Release a reference count on a message queue inode.
 *
 * Input Parameters:
 *   inode - The message queue inode
 *
 * Returned Value:
 *   None
 *
 ****************************************************************************/

static void mq_inode_release (struct inode *inode)
{
    if (inode->i_crefs <= 1)
    {
        struct mqueue_inode_s *msgq = inode->i_private;

        if (msgq)
        {
            TTOS_DeleteMsgq (msgq->ttos_msgq);
            free (msgq);
            inode->i_private = NULL;
        }

        inode_release (inode);
    }
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: file_mq_unlink
 *
 * Description:
 *   This is an internal OS interface.  It is functionally equivalent to
 *   mq_unlink() except that:
 *
 *   - It is not a cancellation point, and
 *   - It does not modify the errno value.
 *
 *  See comments with mq_unlink() for a more complete description of the
 *  behavior of this function
 *
 * Input Parameters:
 *   mq_name - Name of the message queue
 *
 * Returned Value:
 *   This is an internal OS interface and should not be used by applications.
 *   It follows the NuttX internal error return policy:  Zero (0) is
 *   returned on success. A negated errno value is returned on failure.
 *
 ****************************************************************************/

int file_mq_unlink (const char *mq_name)
{
    struct inode         *inode;
    struct inode_search_s desc;
    char                  fullpath[MAX_MQUEUE_PATH];
    int                   ret;

    /* Get the full path to the message queue */

    snprintf (fullpath, MAX_MQUEUE_PATH, CONFIG_FS_MQUEUE_VFS_PATH "/%s",
              mq_name);

    /* Get the inode for this message queue. */

    TTOS_KERNEL_LOCK ();

    SETUP_SEARCH (&desc, fullpath, false);

    ret = inode_find (&desc);
    if (ret < 0)
    {
        /* There is no inode that includes in this path */

        goto errout_with_search;
    }

    /* Get the search results */

    inode = desc.node;
    assert (inode != NULL);

    /* Verify that what we found is, indeed, a message queue */

    if (!INODE_IS_MQUEUE (inode))
    {
        ret = -ENXIO;
        goto errout_with_inode;
    }

    /* Refuse to unlink the inode if it has children.  I.e., if it is
     * functioning as a directory and the directory is not empty.
     */

    ret = inode_lock ();
    if (ret < 0)
    {
        goto errout_with_inode;
    }

    if (inode->i_child != NULL)
    {
        ret = -ENOTEMPTY;
        goto errout_with_lock;
    }

    /* Remove the old inode from the tree.  Because we hold a reference count
     * on the inode, it will not be deleted now.  This will set the
     * FSNODEFLAG_DELETED bit in the inode flags.
     */

    ret = inode_remove (fullpath);

    /* inode_remove() should always fail with -EBUSY because we hae a reference
     * on the inode.  -EBUSY means that the inode was, indeed, unlinked but
     * thatis could not be freed because there are references.
     */

    assert (ret >= 0 || ret == -EBUSY);

    /* Now we do not release the reference count in the normal way (by calling
     * inode release.  Rather, we call mq_inode_release().  mq_inode_release
     * will decrement the reference count on the inode.  But it will also free
     * the message queue if that reference count decrements to zero.  Since we
     * hold one reference, that can only occur if the message queue is not
     * in-use.
     */

    inode_unlock ();
    mq_inode_release (inode);
    RELEASE_SEARCH (&desc);
    TTOS_KERNEL_UNLOCK ();
    return 0;

errout_with_lock:
    inode_unlock ();

errout_with_inode:
    inode_release (inode);

errout_with_search:
    RELEASE_SEARCH (&desc);
    TTOS_KERNEL_UNLOCK ();

    return ret;
}

/****************************************************************************
 * Name: vfs_mq_unlink
 *
 * Description:
 *   This is an internal OS interface.  It is functionally equivalent to
 *   mq_unlink() except that:
 *
 *   - It is not a cancellation point, and
 *   - It does not modify the errno value.
 *
 *  See comments with mq_unlink() for a more complete description of the
 *  behavior of this function
 *
 * Input Parameters:
 *   mq_name - Name of the message queue
 *
 * Returned Value:
 *   This is an internal OS interface and should not be used by applications.
 *   It follows the NuttX internal error return policy:  Zero (0) is
 *   returned on success. A negated errno value is returned on failure.
 *
 ****************************************************************************/

int vfs_mq_unlink (const char *mq_name)
{
    return file_mq_unlink (mq_name);
}
