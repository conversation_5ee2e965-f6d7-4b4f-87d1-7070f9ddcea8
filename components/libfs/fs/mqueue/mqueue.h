/****************************************************************************
 * fs/mqueue/mqueue.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __FS_MQUEUE_MQUEUE_H
#define __FS_MQUEUE_MQUEUE_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
typedef struct T_TTOS_MsgqControlBlock_Struct *MSGQ_ID;
struct mqueue_inode_s
{
    MSGQ_ID         ttos_msgq;
    struct inode   *inode;
    struct kpollfd *fds[CONFIG_FS_MQUEUE_NPOLLWAITERS];
};

/* Sizes of things */

#define MAX_MQUEUE_PATH 64

/* 消息队列最大优先级(0-31) */
#define TTOS_MQ_PRIO_MAX 31

/* 消息优先级 */
#define TTOS_MQ_PRIO_CONVERT(prio) (TTOS_MQ_PRIO_MAX - prio)

int file_mq_unlink (const char *mq_name);
int file_mq_close (struct file *mq);

#endif /* __FS_MQUEUE_MQUEUE_H */
