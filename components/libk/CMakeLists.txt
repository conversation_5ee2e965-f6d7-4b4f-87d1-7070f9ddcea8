include(ExternalProject)

set(PKG_NAME libk)
set(URL http://192.168.11.8/Hongdao/intewell-rtos/components/libk.git)
set(VERSION master)


set(C_FLAGS -O${CONFIG_BUILD_OPTIMIZE})

if(CONFIG_BUILD_DEBUG_INFO)
set(C_FLAGS  ${C_FLAGS}\ -g)
endif(CONFIG_BUILD_DEBUG_INFO)

if(CONFIG_TOOLCHAIN_CLANG)
set(C_FLAGS  ${C_FLAGS}\ -target\ ${CC_TARGET})
endif(CONFIG_TOOLCHAIN_CLANG)

if(CONFIG_ARCH_X86_64)
set(C_FLAGS  ${C_FLAGS}\ -march=x86-64\ -m64\ -mcmodel=large\ -mno-red-zone)
endif(CONFIG_ARCH_X86_64)

add_compile_options()
ExternalProject_Add(${PKG_NAME}
    GIT_REPOSITORY  ${URL}
    GIT_TAG         ${VERSION}
    GIT_PROGRESS    true
    EXCLUDE_FROM_ALL 1
    PREFIX          ${CMAKE_BINARY_DIR}
    # BUILD_IN_SOURCE true
    BINARY_DIR      ${CMAKE_CURRENT_BINARY_DIR}/build/build
    TMP_DIR         ${CMAKE_CURRENT_BINARY_DIR}/build/tmp
    STAMP_DIR       ${CMAKE_CURRENT_BINARY_DIR}/build/stamp
    LOG_DIR         ${CMAKE_CURRENT_BINARY_DIR}/build/log
    DOWNLOAD_DIR    ${CMAKE_CURRENT_BINARY_DIR}/build/download
    SOURCE_DIR      ${CMAKE_CURRENT_BINARY_DIR}/build/src
    INSTALL_DIR     ${CMAKE_BINARY_DIR}
    # LOG_BUILD       true
    CMAKE_ARGS -DCONFIG_ARCH=${CONFIG_ARCH} 
                -DCMAKE_INSTALL_PREFIX=<INSTALL_DIR>
                -DCMAKE_C_COMPILER=${CMAKE_C_COMPILER}
                -DCMAKE_ASM_COMPILER=${CMAKE_ASM_COMPILER}
                -DCMAKE_ASM_FLAGS=${C_FLAGS}
                -DCMAKE_C_COMPILER_FORCED=ON
                -DCMAKE_CXX_COMPILER_FORCED=ON
                -DCMAKE_C_FLAGS=${C_FLAGS}
)

set_property(TARGET ${PKG_NAME} APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_CURRENT_BINARY_DIR}/build/build)
