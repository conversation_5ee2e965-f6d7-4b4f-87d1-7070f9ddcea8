menuconfig NETWORK
	bool "Network"
	default y

config TCPIP
	bool "TCP/IP Stack"
	depends on NETWORK
	default y

choice
	prompt ""
	default LWIP_2_2_0
	depends on TCPIP

	menuconfig LWIP_2_2_0
		bool "LwIP 2.2.0"
		select <PERSON>WI<PERSON>_EXTERNAL_LIBC
		select LWIP_IPV4
		select LWIP_TCP
		select LWIP_UDP
		select LWIP_RAW
		select LWIP_ARP
		select LWIP_IGMP
		rsource "lwip-2.2.0/Kconfig"
endchoice

config MAX_NETIF_IPS
	int "Maximum IP number for one device"
	depends on NETWORK
	default 32
	---help---
		Config how many IPs can be set on one device.

config NETPKT_RESERVED_LEN
	int "netpkt reserved data length"
	depends on NETWORK
	default 32
	---help---
		config extra data length before payload buffer for device-specific usage, currently for virtio-net driver only.

config ETH_RX_TASK_PRIORITY
	int "Ethernet device RX task priority"
	depends on NETWORK
	default 20
	---help---
		Ethernet device RX task priority.

config ETH_RX_TASK_STACK_SIZE
	int "Ethernet device RX task stack size"
	depends on NETWORK
	default 65536
	---help---
		Ethernet device RX task stack size.

config ETH_RX_TASK_AFFINITY
	int "ethernet device RX task affinity"
	depends on NETWORK
	default 0
	---help---
		ethernet device RX task affinity.

config PHY_MONITOR_TASK_PRIORITY
	int "PHY monitor task priority"
	depends on NETWORK
	default 253
	---help---
		PHY monitor task priority.

config PHY_MONITOR_CHECK_PERIOD
	int "The interval at which the PHY monitor task checks the PHY status, by seconds"
	depends on NETWORK
	default 2
	---help---
		The interval at which the PHY monitor task checks the PHY status, by seconds.

config SUPPORT_AF_PACKET_RECV_ARP
	bool "Support AF_PACKET Socket receive ARP packet"
	depends on NETWORK
	default y

config SUPPORT_PCAP_TOOL
	bool "Support use pacap tool to capture ethernet network packet"
	depends on NETWORK
	default n

config USER_SHELL_NET_INIT
	bool "Support network initialization in user shell"
	depends on NETWORK
	default y

config UNIX_SOCKET
	bool "local socket"
	depends on NETWORK
	default y

config NET_LOCAL_VFS_PATH
	string "Path to local sock"
	depends on NETWORK
	default "/dev/"
	---help---
		The path to where localsock will exist in the VFS namespace.
