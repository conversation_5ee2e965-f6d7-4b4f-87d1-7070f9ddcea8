#include <system/kconfig.h>
#include <errno.h>
#include <netinet/if_ether.h>

#include <net/netdev.h>
#include <net/ethernet_dev.h>
#include <net/if_arp.h>
#include <net/packet.h>

#if IS_ENABLED(CONFIG_LWIP_2_2_0)
#include <lwip/netif.h>
#include <lwip/pbuf.h>
#include <net/lwip_ethernet.h>
#endif

/* 分配独立的ETH_NETPKT，不带有网络协议栈结构 */
static ETH_NETPKT *eth_netpkt_alloc_alone (unsigned int len)
{
    ETH_NETPKT *netpkt = NULL;

    netpkt = calloc (1, sizeof(ETH_NETPKT) + len + CONFIG_NETPKT_RESERVED_LEN);
    if (netpkt == NULL)
    {
        return NULL;
    }

    netpkt->base = (void *)netpkt;

    netpkt->flags = ETH_NETPKT_STANDALONE;

    /* 根据是否分配数据空间设置标志位 */
    if (len == 0)
    {
        netpkt->flags |= ETH_NETPKT_DATA_SEPARATED;
    }
    netpkt->len = len;

    if (len == 0)
    {
        netpkt->buf = NULL;
    }
    else
    {
        netpkt->buf = (unsigned char *)netpkt + sizeof(ETH_NETPKT) + CONFIG_NETPKT_RESERVED_LEN;
        netpkt->reserved = (unsigned char *)netpkt + sizeof(ETH_NETPKT);
    }

    return netpkt;
}

#if IS_ENABLED(CONFIG_TCPIP)
/* 分配带有网络协议栈结构netpkt */
static ETH_NETPKT *eth_netpkt_alloc_with_netstack (unsigned int len)
{
    ETH_NETPKT *netpkt = NULL;

#if CONFIG_LWIP_2_2_0
    netpkt = eth_lwip_alloc_pbuf (len);
#endif

    return netpkt;
}
#endif

/*
  根据参数type分配不同类型的数据包结构ETH_NETPKT
  注意，使用LwIP协议栈时，以太网设备驱动中使用本函数分配的，带有协议栈结构，用于数据接收的ETH_NETPKT，
  在通过ETH_DATA_TO_STACK()向网络协议栈提交后，无需调用eth_netpkt_free()，数据包将由LwIP进行释放
*/
ETH_NETPKT *eth_netpkt_alloc (ether_header_t *hdr, unsigned int len)
{
    ETH_NETPKT *netpkt = NULL;
    uint16_t pptp_type;

#if IS_ENABLED(CONFIG_TCPIP)
    if (hdr == NETPKT_WITH_NETSTACK)
    {
        netpkt = eth_netpkt_alloc_with_netstack (len);
        return netpkt;
    }
    else if (hdr == NETPKT_STANDALONE)
    {
        netpkt = eth_netpkt_alloc_alone (len);
        return netpkt;
    }

    pptp_type = eth_network_protocol_parse (hdr);

    switch (pptp_type)
    {
        case ETH_P_IP:
            netpkt = eth_netpkt_alloc_with_netstack (len);
            break;
        case ETH_P_ARP:
            netpkt = eth_netpkt_alloc_with_netstack (len);
            netpkt->flags |= ETH_NETPKT_ARP;
            break;
        default:
            netpkt = eth_netpkt_alloc_alone (len);
            break;
    }
#else
    netpkt = eth_netpkt_alloc_alone (len);
#endif

    return netpkt;
}

/*
    目前该函数仅针对ETH_NETPKT_STANDALONE类型的netpkt（包括ETH_NETPKT_ETHERNETCAT类型）
    例如使用LwIP协议栈时，传递给以太网卡驱动发送函数的netpkt为ETH_NETPKT_STANDALONE类型，
    驱动使用完成后需要调用本函数对netpkt的空间进行释放
*/
int eth_netpkt_free (ETH_NETPKT *netpkt)
{
    if (netpkt->flags & ETH_NETPKT_STANDALONE)
    {
        if (netpkt->flags & ETH_NETPKT_DATA_SEPARATED)
        {
            free (netpkt->buf);
            free (netpkt);
            return OK;
        }
        else
        {
            free (netpkt);
            return OK;
        }
    }
#if IS_ENABLED(CONFIG_TCPIP)
    else
    {
#if IS_ENABLED(CONFIG_LWIP_2_2_0)
        pbuf_free(netpkt->base);
        return OK;
#else
        return -ERROR;
#endif
    }
#endif
}

/*
    克隆一个与原netpkt数据内容相同的新netpkt，type用于选择新netpkt的类型
    不可用于ETH_NETPKT_DATA_SEPARATED类型的netpkt
*/
ETH_NETPKT *eth_netpkt_clone (const ETH_NETPKT *netpkt, ETH_NETPKT_TYPE type)
{
    ETH_NETPKT *new_netpkt = NULL;

    if (netpkt->flags & ETH_NETPKT_DATA_SEPARATED)
    {
        return NULL;
    }

    if (type == standalone)
    {
        new_netpkt = eth_netpkt_alloc (NETPKT_STANDALONE, netpkt->len);

        if (new_netpkt != NULL)
        {
            memcpy (new_netpkt->buf, netpkt->buf, netpkt->len);
        }

        /* 复制原netpkt类型标志位除0位外的其他位 */
        new_netpkt->flags |= (netpkt->flags & 0xFFFFFFFE);

        return new_netpkt;
    }
#if IS_ENABLED(CONFIG_TCPIP)
    else
    {
#if IS_ENABLED(CONFIG_LWIP_2_2_0)
        new_netpkt = eth_lwip_clone_netpkt (netpkt);
        return new_netpkt;
#else
        return NULL;
#endif
    }
#endif
}

