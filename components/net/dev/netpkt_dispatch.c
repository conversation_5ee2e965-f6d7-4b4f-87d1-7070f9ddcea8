#include <errno.h>
#include <system/kconfig.h>

#include <net/ethernet_dev.h>
#include <net/if_arp.h>
#include <net/netdev.h>
#include <net/packet.h>

#include <symtab.h>

#if IS_ENABLED(CONFIG_LWIP_2_2_0)
#include <lwip/netif.h>
#include <lwip/pbuf.h>
#include <net/lwip_ethernet.h>
#endif

/* 数据传递至协议栈 */
KSYM_EXPORT(ETH_DATA_TO_STACK);
void ETH_DATA_TO_STACK(ETH_DEV *ethdev, ETH_NETPKT *netpkt)
{
#if (IS_ENABLED(CONFIG_TCPIP))

#ifdef CONFIG_SUPPORT_AF_PACKET_RECV_ARP
    ETH_NETPKT *newpkt = NULL;

    /* ARP协议数据包复制一份投递给AF_PACKET套接字 */
    if (netpkt->flags & ETH_NETPKT_ARP)
    {
        newpkt = eth_netpkt_clone(netpkt, standalone);
        packet_input(ethdev, newpkt);

#if IS_ENABLED(CONFIG_LWIP_2_2_0)
        eth_lwip_data_to_stack(ethdev, netpkt);
#endif
    }
    else
#endif /* CONFIG_SUPPORT_AF_PACKET_RECV_ARP */
    if (netpkt->flags & ETH_NETPKT_STANDALONE)
    {
        packet_input(ethdev, netpkt);
    }
    else
    {
#if IS_ENABLED(CONFIG_LWIP_2_2_0)
        eth_lwip_data_to_stack(ethdev, netpkt);
#else
        eth_netpkt_free(netpkt);
#endif /* IS_ENABLED(CONFIG_LWIP_2_2_0) */
    }
#else
    packet_input(ethdev, netpkt);
#endif /* IS_ENABLED(CONFIG_TCPIP) */
}
