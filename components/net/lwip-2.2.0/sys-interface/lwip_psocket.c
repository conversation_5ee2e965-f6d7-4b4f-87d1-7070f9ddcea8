#include <assert.h>
#include <errno.h>
#include <fs/kpoll.h>
#include <net/net.h>
#include <netinet/in.h>
#include <net/if.h>
#include <poll.h>
#include <fcntl.h>
#include <lwip/netif.h>
#include <lwip/api.h>
#include <lwip/priv/sockets_priv.h>
#include <lwip/etharp.h>
#include <net/netdev.h>
#include <net/netdev_ioctl.h>
#include <net/ethernet_dev.h>
#include <net/lwip_ethernet.h>
#include <net/if_arp.h>


#undef KLOG_TAG
#define KLOG_TAG "LWIP_PSOCK"
#include <klog.h>

/* PUBLIC */

typedef struct lwip_sock *(*LWIP_GET_SOCK_FN) (int);

extern int lwip_accept (int s, struct sockaddr *addr, socklen_t *addrlen);
extern int lwip_bind (int s, const struct sockaddr *name, socklen_t namelen);
extern int lwip_shutdown (int s, int how);
extern int lwip_getpeername (int s, struct sockaddr *name, socklen_t *namelen);
extern int lwip_getsockname (int s, struct sockaddr *name, socklen_t *namelen);
extern int lwip_getsockopt (int s, int level, int optname, void *optval, socklen_t *optlen);
extern int lwip_setsockopt (int s, int level, int optname, const void *optval, socklen_t optlen);
extern int lwip_close (int s);
extern int lwip_connect (int s, const struct sockaddr *name, socklen_t namelen);
extern int lwip_listen (int s, int backlog);
extern ssize_t lwip_recvmsg (int s, struct msghdr *message, int flags);
extern ssize_t lwip_sendmsg (int s, const struct msghdr *message, int flags);
extern int lwip_socket (int domain, int type, int protocol);
extern int lwip_poll (struct pollfd *fds, nfds_t nfds, int timeout);
extern int lwip_ioctl (int s, long cmd, void *argp);
extern LWIP_GET_SOCK_FN lwip_get_sock;

static int if_setup (struct netdev_sock *nsock)
{
    assert (nsock);

    int sockfd;
    struct lwip_sock *lsock;
    unsigned long non_block_opt = 1;

    sockfd = lwip_socket (nsock->satype.family, nsock->satype.type, nsock->satype.protocol);

    if (sockfd < 0)
    {
        return -errno;
    }

    nsock->priv = (void *)(uintptr_t)sockfd;

    lsock = lwip_get_sock (sockfd);

    lsock->aio.aio_enable = false;
    lsock->aio.pid = -1;

    /*
      有些函数会在创建套接字时指定SOCK_NONBLOCK属性，LwIP并不会检查和处理该属性
      此处进行检查和设置
    */
    if (nsock->satype.type & SOCK_NONBLOCK)
    {
        lwip_ioctl (sockfd, (long)FIONBIO, (void *)non_block_opt);
    }

    return 0;
}

static int if_bind (struct netdev_sock *nsock, const struct sockaddr *addr, socklen_t addrlen)
{
    assert (nsock);

    int ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_bind (sockfd, addr, addrlen);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_getsockname (struct netdev_sock *nsock, struct sockaddr *addr, socklen_t *addrlen)
{
    assert (nsock);

    int ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_getsockname (sockfd, addr, addrlen);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_getpeername (struct netdev_sock *nsock, struct sockaddr *addr, socklen_t *addrlen)
{
    assert (nsock);

    int ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_getpeername (sockfd, addr, addrlen);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_listen (struct netdev_sock *nsock, int backlog)
{
    assert (nsock);

    int ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_listen (sockfd, backlog);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_connect (struct netdev_sock *nsock, const struct sockaddr *addr, socklen_t addrlen)
{
    assert (nsock);

    int ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_connect (sockfd, addr, addrlen);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_accept (struct netdev_sock *nsock, struct sockaddr *addr, socklen_t *addrlen, struct netdev_sock *new_nsock, int flags)
{
    assert (nsock);

    int sockfd = (uintptr_t)nsock->priv;
    int newfd;

    newfd = lwip_accept (sockfd, addr, addrlen);

    if (newfd < 0)
    {
        return -errno;
    }

    new_nsock->sockif = nsock->sockif;
    new_nsock->priv   = (void *)(uintptr_t)newfd;

    return 0;
}

static int if_poll (struct netdev_sock *nsock, struct kpollfd *kfds, bool setup)
{
    assert (nsock);

    int sockfd = (uintptr_t)nsock->priv;

    short eventset = kfds != NULL ? kfds->pollfd.revents : 0;

    struct lwip_sock *lsock = lwip_get_sock (sockfd);
    if (lsock == NULL)
    {
        return -ENODEV;
    }

    SYS_ARCH_PROTECT (lev);

    if (setup)
    {
        lsock->kfd = kfds;
        kfds->priv = &lsock->kfd;

        if (lsock->conn->events.rcvevent > 0)
        {
            eventset |= POLLIN;
        }
        if (lsock->conn->events.sendevent > 0)
        {
            eventset |= POLLOUT;
        }
        if (lsock->conn->events.errevent > 0)
        {
            eventset |= POLLERR;
        }

        SYS_ARCH_UNPROTECT (lev);

        kpoll_notify (&kfds, 1, eventset, &lsock->aio);
    }
    else
    {
        *(struct kpollfd **)kfds->priv = NULL;
        kfds->priv                     = NULL;

        SYS_ARCH_UNPROTECT (lev);
    }

    return 0;
}

static ssize_t if_sendmsg (struct netdev_sock *nsock, struct msghdr *msg, int flags)
{
    assert (nsock);

    ssize_t ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    /* LwIP未支持MSG_NOSIGNAL标志位，因此在此处检查并清除，以支持musl库的getaddrinfo()函数 */
    if (flags & MSG_NOSIGNAL)
    {
        flags &= ~MSG_NOSIGNAL;
    }

    ret = lwip_sendmsg (sockfd, msg, flags);

    if (ret < 0)
    {
        ret = (ssize_t)(-errno);
    }

    return ret;
}

static ssize_t if_recvmsg (struct netdev_sock *nsock, struct msghdr *msg, int flags)
{
    assert (nsock);

    ssize_t ret = 0;
    int i;
    struct sockaddr_in *msg_name = NULL;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_recvmsg (sockfd, msg, flags);

    /*
      LwIP在向msg_name拷贝数据时未将成员变量sin_zero数组清零，会导致musl库的getaddrinfo接口检查地址匹配时不通过
      此处将其做清零处理
    */
    if ((ret > 0) && (msg->msg_namelen == sizeof(struct sockaddr_in)))
    {
        msg_name = (struct sockaddr_in *)msg->msg_name;
        for (i = 0; i < sizeof(msg_name->sin_zero); i++)
        {
            msg_name->sin_zero[i] = 0;
        }
    }

    /* socket接收接口只用到一个msg */
    if (ret > 0 && (size_t)ret > msg->msg_iov[0].iov_len)
    {
        ret = (ssize_t)msg->msg_iov[0].iov_len;
    }

    if (ret < 0)
    {
        ret = (ssize_t)(-errno);
    }

    return ret;
}

static void if_addref (struct netdev_sock *nsock)
{
    /**
     * DO NOTHING
     * PSOCK引用依靠底层vfs inode的引用计数
     * LWIP无需对PSOCK引用变化进行操作
     * PSOCK需要判断si_addref函数指针，因此留空函数兼容
     */
}

static int if_close (struct netdev_sock *nsock)
{
    assert (nsock);

    int ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_close (sockfd);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_ioctl (struct netdev_sock *nsock, int cmd, unsigned long arg)
{
    assert (nsock);

    int sockfd = (uintptr_t)nsock->priv;
    int ret = 0;
    struct lwip_sock *lsock = lwip_get_sock (sockfd);
    struct arpreq *arpr;
    struct sockaddr_in *addr_in;
    ip4_addr_t ip_addr;
    struct eth_addr hw_addr;
    int i;

    switch (cmd)
    {
        case FIOASYNC:
            {
                lsock->aio.aio_enable = !!arg;
                ret                   = 0;
            }
            return ret;
        case FIOGETOWN:
            {
                if (arg)
                {
                    *(int *)arg = lsock->aio.pid;
                    ret         = 0;
                }
            }
            return ret;
        case FIOSETOWN:
            {
                if (arg)
                {
                    lsock->aio.pid = (int)arg;
                    ret            = 0;
                }
                else
                {
                    ret = -EINVAL;
                }
            }
            return ret;
        case FIOCLEX:
        case FIONCLEX:
            return -ENOTTY;

        case SIOCSARP:
            arpr = (struct arpreq *)arg;
            addr_in = (struct sockaddr_in *)&arpr->arp_pa;

            ip_addr.addr = (u32_t)addr_in->sin_addr.s_addr;

            for (i = 0; i < 6; i++)
            {
                hw_addr.addr[i] = arpr->arp_ha.sa_data[i];
            }

            ret = etharp_add_static_entry((const ip4_addr_t *)&ip_addr, &hw_addr);

            return ret;

        case SIOCDARP:
            arpr = (struct arpreq *)arg;
            addr_in = (struct sockaddr_in *)&arpr->arp_pa;

            ip_addr.addr = (u32_t)addr_in->sin_addr.s_addr;

            ret = etharp_remove_static_entry ((const ip4_addr_t *)&ip_addr);

            /* 设置返回值以适配busybox的arp工具 */
            if (ret < 0)
            {
                if (ret == ERR_MEM)
                {
                    ret = -ENXIO;
                }

                if (ret == ERR_ARG)
                {
                    ret = -EPERM;
                }
            }

            return ret;
        default:
            break;
    }

    ret = lwip_ioctl (sockfd, (long)cmd, (void *)arg);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_shutdown (struct netdev_sock *nsock, int how)
{
    assert (nsock);

    int ret = 0;

    int sockfd = (uintptr_t)nsock->priv;

    ret = lwip_shutdown (sockfd, how);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_getsockopt (struct netdev_sock *nsock, int level, int option, void *value, socklen_t *value_len)
{
    assert (nsock);

    int sockfd = (uintptr_t)nsock->priv;

    int ret = lwip_getsockopt (sockfd, level, option, value, value_len);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static int if_setsockopt (struct netdev_sock *nsock, int level, int option, const void *value, socklen_t value_len)
{
    assert (nsock);

    const void *real_val = value;
    int sockfd = (uintptr_t)nsock->priv;

    NET_DEV *ndev = NULL;
    struct ifreq pifr = {0};

    if (level == SOL_SOCKET && option == SO_BINDTODEVICE)
    {
        ndev = netdev_get_by_name(((const struct ifreq *)value)->ifr_name);
        if (NULL == ndev)
        {
            return -ENODEV;
        }

        eth_lwip_to_internal_name((ETH_DEV *)ndev->link_data, pifr.ifr_name);
        real_val = &pifr;
    }

    int ret = lwip_setsockopt (sockfd, level, option, real_val, value_len);

    if (ret < 0)
    {
        ret = -errno;
    }

    return ret;
}

static const struct sa_type lwip_supp_sa_types[] =
{
    {.family = AF_INET, .type = SOCK_DGRAM, .protocol = IPPROTO_IP},
    {.family = AF_INET, .type = SOCK_DGRAM, .protocol = IPPROTO_UDP},
    {.family = AF_INET, .type = SOCK_STREAM, .protocol = IPPROTO_IP},
    {.family = AF_INET, .type = SOCK_STREAM, .protocol = IPPROTO_TCP},
    {.family = AF_INET, .type = SOCK_RAW, .protocol = IPPROTO_RAW},
    {.family = AF_INET, .type = SOCK_RAW, .protocol = IPPROTO_IP},
    {.family = AF_INET, .type = SOCK_RAW, .protocol = IPPROTO_UDP},
    {.family = AF_INET, .type = SOCK_RAW, .protocol = IPPROTO_TCP},
    {.family = AF_INET, .type = SOCK_RAW, .protocol = IPPROTO_ICMP},
#if LWIP_IPV6
    {.family = AF_INET6, .type = SOCK_DGRAM, .protocol = IPPROTO_IP},
    {.family = AF_INET6, .type = SOCK_DGRAM, .protocol = IPPROTO_UDP},
    {.family = AF_INET6, .type = SOCK_STREAM, .protocol = IPPROTO_IP},
    {.family = AF_INET6, .type = SOCK_STREAM, .protocol = IPPROTO_TCP},
    {.family = AF_INET6, .type = SOCK_RAW, .protocol = IPPROTO_RAW},
    {.family = AF_INET6, .type = SOCK_RAW, .protocol = IPPROTO_IP},
    {.family = AF_INET6, .type = SOCK_RAW, .protocol = IPPROTO_UDP},
    {.family = AF_INET6, .type = SOCK_RAW, .protocol = IPPROTO_TCP},
    {.family = AF_INET6, .type = SOCK_RAW, .protocol = IPPROTO_ICMP},
#endif
};

static struct netdev_sock_ops lwip_ndev_ops =
{
    .ns_setup       = if_setup,
    .ns_sockcaps    = NULL,
    .ns_addref      = if_addref,
    .ns_bind        = if_bind,
    .ns_getsockname = if_getsockname,
    .ns_getpeername = if_getpeername,
    .ns_listen      = if_listen,
    .ns_connect     = if_connect,
    .ns_accept      = if_accept,
    .ns_poll        = if_poll,
    .ns_sendmsg     = if_sendmsg,
    .ns_recvmsg     = if_recvmsg,
    .ns_close       = if_close,
    .ns_ioctl       = if_ioctl,
    .ns_socketpair  = NULL,
    .ns_shutdown    = if_shutdown,
    .ns_getsockopt  = if_getsockopt,
    .ns_setsockopt  = if_setsockopt,
    .ns_sendfile    = NULL,
};

/**
 *
 * Pcap capabilities
 *
 *
 *
 */

static const struct sa_type lwip_pcap_supp_sa_types[] =
{
    {.family = AF_PACKET, .type = SOCK_RAW, .protocol = IPPROTO_IP},
};

static int try_delete_msgq_and_node(MSGQ_ID msgq, struct list_node *node)
{
    int ret = OK;

    if (msgq != NULL)
    {
        ret = TTOS_DeleteMsgq((MSGQ_ID)msgq);

        if (ret == TTOS_OK)
        {
            list_delete(node);
        }
    }

    return ret;
}

int if_packet_setup (struct netdev_sock *nsock)
{
    struct pcap_nsock *pnsock = calloc(1, sizeof(struct pcap_nsock));

    if (pnsock == NULL)
    {
        return -ENOMEM;
    }

    pnsock->nsock = nsock;
    pnsock->msgq = NULL;
    pnsock->eth = NULL;
    nsock->priv = (void *)pnsock;

    return OK;
}

ssize_t if_packet_recvmsg (struct netdev_sock *nsock, struct msghdr *msg, int flags)
{
    T_UWORD msglen = 0;

    struct pcap_nsock *pnsock = (struct pcap_nsock *)nsock->priv;

    if (pnsock->msgq == NULL)
    {
        return (ssize_t)-EINVAL;
    }

    int ret = TTOS_ReceiveMsgq(pnsock->msgq,
        msg->msg_iov->iov_base, &msglen,
        0, TTOS_MSGQ_WAIT_FOREVER);

    msg->msg_iov->iov_len = msglen;
    msg->msg_iovlen = 1;

    return ret == 0 ? (ssize_t)msglen : (ssize_t)ret;
}

int if_packet_close (struct netdev_sock *nsock)
{
    int ret = OK;
    irq_flags_t flags;

    struct pcap_nsock *pnsock = (struct pcap_nsock *)nsock->priv;

    TTOS_ObtainMutex(PCAP_SOCKLIST_MUTEX, TTOS_MUTEX_WAIT_FOREVER);
    ret = try_delete_msgq_and_node(pnsock->msgq, &pnsock->node);
    if (ret == OK)
    {
        free(pnsock);
    }
    TTOS_ReleaseMutex(PCAP_SOCKLIST_MUTEX);

    return ret;
}

int if_pcaket_ioctl (struct netdev_sock *nsock, int cmd, unsigned long arg)
{
    struct ifreq *ifr = (struct ifreq *)arg;
    pcap_ioctl_t *pcap = (pcap_ioctl_t *)ifr->ifr_data;
    NET_DEV *ndev;
    struct pcap_nsock *pnsock;
    MSGQ_ID msgq;
    irq_flags_t flags;
    T_TTOS_ReturnCode mutex_ret;

    if (cmd != SIOCXPCAP)
    {
        return -ENOTSUP;
    }

    if (pcap == NULL || nsock->priv == NULL)
        return -EINVAL;

    pnsock = (struct pcap_nsock *)nsock->priv;

   ndev = netdev_get_by_name (ifr->ifr_name);
    if (NULL == ndev)
    {
        return -ENODEV;
    }

    switch (pcap->op)
    {
    case PCAP_OP_START:
        if (pnsock->msgq != NULL)
        {
            return -EINVAL;
        }

        if (pnsock == NULL)
        {
            return -ENOMEM;
        }

        int ret = TTOS_CreateMsgqEx(1600, 128, &msgq);

        if (ret < 0)
        {
            return ret;
        }

        pnsock->msgq = msgq;
        pnsock->eth = (ETH_DEV *)ndev->link_data;
        mutex_ret = TTOS_ObtainMutex(PCAP_SOCKLIST_MUTEX, TTOS_MUTEX_WAIT_FOREVER);
        list_add(&pnsock->node, &PCAP_NSOCK_LIST);
        TTOS_ReleaseMutex(PCAP_SOCKLIST_MUTEX);
        break;

    case PCAP_OP_STOP:
        if (pnsock->msgq != NULL)
        {
            TTOS_ObtainMutex(PCAP_SOCKLIST_MUTEX, TTOS_MUTEX_WAIT_FOREVER);
            ret = try_delete_msgq_and_node(pnsock->msgq, &pnsock->node);
            pnsock->msgq = NULL;
            TTOS_ReleaseMutex(PCAP_SOCKLIST_MUTEX);
            if(ret < 0)
            {
                return ret;
            }
        }
        else
        {
            return -EINVAL;
        }
        break;

    default:
        return -ENOTSUP;
    }

    return OK;
}

static struct netdev_sock_ops lwip_pcap_ndev_ops =
{
    .ns_setup       = if_packet_setup,
    .ns_sockcaps    = NULL,
    .ns_addref      = NULL,
    .ns_bind        = NULL,
    .ns_getsockname = NULL,
    .ns_getpeername = NULL,
    .ns_listen      = NULL,
    .ns_connect     = NULL,
    .ns_accept      = NULL,
    .ns_poll        = NULL,
    .ns_sendmsg     = NULL,
    .ns_recvmsg     = if_packet_recvmsg,
    .ns_close       = if_packet_close,
    .ns_ioctl       = if_pcaket_ioctl,
    .ns_socketpair  = NULL,
    .ns_shutdown    = NULL,
    .ns_getsockopt  = NULL,
    .ns_setsockopt  = NULL,
    .ns_sendfile    = NULL,
};

void lwip_register_sockif ()
{
    if (netdev_register_net_stack_sockif(lwip_supp_sa_types, array_size(lwip_supp_sa_types), &lwip_ndev_ops) != 0)
    {
        KLOG_E("Add normal lwip socket interfaces failed");
    }

    if (netdev_register_net_stack_sockif(lwip_pcap_supp_sa_types, array_size(lwip_pcap_supp_sa_types), &lwip_pcap_ndev_ops) != 0)
    {
        KLOG_E("Add pcap lwip socket interfaces failed");
    }
}
