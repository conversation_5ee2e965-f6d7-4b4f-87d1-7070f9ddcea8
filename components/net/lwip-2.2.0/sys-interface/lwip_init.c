#include <arch/sys_arch.h>
#include <lwip/netif.h>
#include <lwip/tcpip.h>
#include <net/ethernet_dev.h>
#include <net/netdev.h>
#include <ttos_init.h>

#define KLOG_TAG "LWIP_INIT"
#include <klog.h>

typedef struct T_TTOS_TaskControlBlock_Struct *TASK_ID;

extern const struct netstack_ops lwip_netdev_ops;
extern struct netstack_ops *eth_stack_netdev_ops;
extern netdev_install_cb eth_netdev_install_cb;
extern void lwip_register_sockif();
extern int eth_lwip_netdev_install_callback();
extern void phy_subsystem_init();
void eth_device_loopback_init ();


static void tcpip_init_done_callback()
{
    KLOG_D("LWIP init done");
    T_TTOS_ReturnCode ret;

    ret = TTOS_CreateMutex(1, 0, &PCAP_SOCKLIST_MUTEX);
    if (TTOS_OK != ret)
    {
        KLOG_E("Can't create pcap mutex");
        PCAP_SOCKLIST_MUTEX = NULL;
    }

    /* lwip tcpip启动后挂载psock */
    lwip_register_sockif();

    eth_device_loopback_init ();
}

static int ttos_tcpip_init()
{
    KLOG_D("LWIP init start");

    eth_netdev_install_cb = eth_lwip_netdev_install_callback;

    eth_stack_netdev_ops = (struct netstack_ops *)&lwip_netdev_ops;

    tcpip_init(tcpip_init_done_callback, NULL);

    phy_subsystem_init();

    return 0;
}

INIT_EXPORT_COMPONENTS(ttos_tcpip_init, "init lwip protocol stack");
