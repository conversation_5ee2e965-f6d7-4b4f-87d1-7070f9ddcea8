#include <errno.h>
#include <lwip/dhcp.h>
#include <lwip/dns.h>
#include <lwip/igmp.h>
#include <lwip/netif.h>
#include <net/ethernet_dev.h>
#include <net/lwip_ethernet.h>
#include <net/netdev.h>
#include <net/netdev_ioctl.h>
#include <shell.h>
#include <sys/ioctl.h>
#include <sys/socket.h>

#define KLOG_TAG "NETDEV"
#include <klog.h>

void impl_ethernet_get_all_ips(ETH_DEV *eth, netif_ips_t *ips)
{
    int i;
    long ip_flags;

    ips->ip_count = eth->lwip_netif_count;

    spin_lock_irqsave(&eth->ip_lock, ip_flags);
    for (i = 0; i < eth->lwip_netif_count && i < ips->ip_count; i += 1)
    {
        ips->addrs[i].addr.s_addr = eth->lwip_netif[i]->ip_addr.addr;
        ips->addrs[i].netmask.s_addr = eth->lwip_netif[i]->netmask.addr;
        ips->addrs[i].flags = (uint32_t)eth->lwip_netif[i]->flags;
    }
    spin_unlock_irqrestore(&eth->ip_lock, ip_flags);
    ips->ip_count = i;
}

static int __lwip_set_etharp(NET_DEV *netdev, uint32_t newstat)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        if (newstat == NETDEV_FLAG_SET)
        {
            ethdev->lwip_netif[0]->flags |= NETIF_FLAG_ETHARP;
        }
        else
        {
            ethdev->lwip_netif[0]->flags &= (~NETIF_FLAG_ETHARP);
        }
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

static int __lwip_set_broadcast(NET_DEV *netdev, uint32_t newstat)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        if (newstat == NETDEV_FLAG_SET)
        {
            ethdev->lwip_netif[i]->flags |= NETIF_FLAG_BROADCAST;
        }
        else
        {
            ethdev->lwip_netif[i]->flags &= (~NETIF_FLAG_BROADCAST);
        }
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

static int __lwip_set_multicast(NET_DEV *netdev, uint32_t newstat)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    int ret = ERR_OK;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        if (newstat == NETDEV_FLAG_SET)
        {
            if ((ret = igmp_start(ethdev->lwip_netif[i])) == ERR_OK)
            {
                ethdev->lwip_netif[i]->flags |= NETIF_FLAG_IGMP;
            }
        }
        else
        {
            if ((ret = igmp_stop(ethdev->lwip_netif[i])) == ERR_OK)
            {
                ethdev->lwip_netif[i]->flags &= (~NETIF_FLAG_IGMP);
            }
        }
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ret;
}

static int lwip_netdev_set_flag(NET_DEV *netdev, uint32_t flag, uint32_t newstat)
{
    int ret = ERR_OK;
    uint16_t oldflags = netdev->flags;

    if ((!!(netdev->flags & flag)) == newstat)
    {
        return ERR_OK;
    }

    if (newstat == NETDEV_FLAG_SET)
    {
        netdev->flags |= flag;
    }
    else
    {
        netdev->flags &= (~flag);
    }

    switch (flag)
    {
    case NETDEV_FLAG_ETHARP:
        ret = __lwip_set_etharp(netdev, newstat);
        break;
    case NETDEV_FLAG_BROADCAST:
        ret = __lwip_set_broadcast(netdev, newstat);
        break;
    case NETDEV_FLAG_IGMP:
        ret = __lwip_set_multicast(netdev, newstat);
        break;

    default:
        ret = -ENOTSUP;
        break;
    }

    if (ret != ERR_OK)
    {
        netdev->flags = oldflags;
    }

    return ret;
}

static int lwip_netdev_set_up(NET_DEV *netdev)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        netif_set_up(ethdev->lwip_netif[i]);
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    eth_release_rx_sem(ethdev);

    return ERR_OK;
}

static int lwip_netdev_set_down(NET_DEV *netdev)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        netif_set_down(ethdev->lwip_netif[i]);
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

static int lwip_netdev_set_link_up(NET_DEV *netdev)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        netif_set_link_up(ethdev->lwip_netif[i]);
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

static int lwip_netdev_set_link_down(NET_DEV *netdev)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        netif_set_link_down(ethdev->lwip_netif[i]);
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

static int lwip_netdev_set_mac(NET_DEV *netdev, unsigned long arg)
{
    ETH_DEV *ethdev = netdev->link_data;
    const char *new_mac = NULL;
    int i;
    int ret;
    long ip_flags;

    if (ethdev->drv_info->eth_func->ioctl == NULL)
    {
        return -ENOTSUP;
    }

    ret = ethdev->drv_info->eth_func->ioctl(ethdev, SIOCSIFHWADDR, arg);
    if (ret < 0)
    {
        return ret;
    }

    new_mac = (const char *)((struct ifreq *)arg)->ifr_hwaddr.sa_data;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i++)
    {
        memcpy(ethdev->lwip_netif[i]->hwaddr, new_mac, ethdev->lwip_netif[0]->hwaddr_len);
    }
    /* 最后设置netdev的mac */
    memcpy(netdev->hwaddr, ethdev->lwip_netif[0]->hwaddr, ethdev->lwip_netif[0]->hwaddr_len);
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

static int lwip_netdev_set_addr_info(NET_DEV *netdev, struct in_addr *ip_addr,
                                     struct in_addr *netmask, struct in_addr *gw)
{
    ETH_DEV *ethdev = netdev->link_data;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    if (ip_addr && netmask && gw)
    {
        netif_set_addr(ethdev->lwip_netif[0], (const ip4_addr_t *)ip_addr,
                       (const ip4_addr_t *)netmask, (const ip4_addr_t *)gw);
    }
    else
    {
        if (ip_addr)
        {
            netif_set_ipaddr(ethdev->lwip_netif[0], (const ip4_addr_t *)ip_addr);
        }

        if (netmask)
        {
            netif_set_netmask(ethdev->lwip_netif[0], (const ip4_addr_t *)netmask);
        }

        if (gw)
        {
            netif_set_gw(ethdev->lwip_netif[0], (const ip4_addr_t *)gw);
        }
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

static int lwip_netdev_set_mtu(NET_DEV *netdev, int mtu)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        ethdev->lwip_netif[i]->mtu = mtu;
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

/* 将网络协议栈结构状态与NET_DEV状态同步 */
int lwip_netdev_sync_flags(NET_DEV *netdev, void *netstack_data)
{
    struct netif *lwip_netif = NULL;

    if (netdev == NULL || netstack_data == NULL)
    {
        return -ERROR;
    }

    lwip_netif = (struct netif *)netstack_data;

    /* 同步设备数据 */
    netdev->cfg.ethernet.mtu = lwip_netif->mtu;
    netdev->hwaddr_len = lwip_netif->hwaddr_len;
    memcpy(netdev->hwaddr, lwip_netif->hwaddr, lwip_netif->hwaddr_len);
    netdev->cfg.ethernet.ip_addr = *(struct in_addr *)&lwip_netif->ip_addr;
    netdev->cfg.ethernet.gw = *(struct in_addr *)&lwip_netif->gw;
    netdev->cfg.ethernet.netmask = *(struct in_addr *)&lwip_netif->netmask;

    /* 低8位直接同步netif的flags */
    netdev->flags = (uint32_t)lwip_netif->flags;

    /* DHCP标志同步 */
#if LWIP_DHCP
    /* 对环回网卡不设置DHCP标志位，后续应对DHCP的状态设置方法进行优化 */
    if (strcmp(netdev->name, "lo") != 0)
    {
        netdev_sync_dhcp_status(netdev, 1);
    }
#else
    netdev_sync_dhcp_status(netdev, 0);
#endif /* LWIP_DHCP */

    return ERR_OK;
}

#if LWIP_DHCP
static int lwip_netdev_set_dhcp(NET_DEV *netdev, unsigned int is_enabled)
{
    ETH_DEV *ethdev = netdev->link_data;
    int i;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    if (1 == is_enabled)
    {
        for (i = 0; i < ethdev->lwip_netif_count; i += 1)
        {
            dhcp_start(ethdev->lwip_netif[i]);
        }
    }
    else
    {
        for (i = 0; i < ethdev->lwip_netif_count; i += 1)
        {
            dhcp_stop(ethdev->lwip_netif[i]);
        }
    }
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}
#endif /* LWIP_DHCP */

static int lwip_netdev_set_default(NET_DEV *netdev)
{
    ETH_DEV *ethdev = netdev->link_data;
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    netif_set_default(ethdev->lwip_netif[0]);
    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    return ERR_OK;
}

int impl_lwip_netdev_add_addr(NET_DEV *netdev, struct in_addr *ip_addr, struct in_addr *netmask)
{
    ETH_DEV *ethdev = netdev->link_data;
    ETH_CFG_INFO new_info;
    long spin_flags;
    int i;

    spin_lock_irqsave(&ethdev->ip_lock, spin_flags);

    if (NULL == ip_addr || NULL == netmask)
    {
        return -EINVAL;
    }

    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        if (ip_addr->s_addr == ethdev->lwip_netif[i]->ip_addr.addr &&
            netmask->s_addr == ethdev->lwip_netif[i]->netmask.addr)
        {
            break;
        }
    }

    if (i != ethdev->lwip_netif_count)
    {
        spin_unlock_irqrestore(&ethdev->ip_lock, spin_flags);

        return ERR_OK;
    }
    else
    {
        spin_unlock_irqrestore(&ethdev->ip_lock, spin_flags);

        new_info.ip.s_addr = ip_addr->s_addr;
        new_info.netmask.s_addr = netmask->s_addr;

        return eth_lwip_add_ip(ethdev, &new_info);
    }
}

int impl_lwip_netdev_del_addr(NET_DEV *netdev, struct in_addr *ip_addr, struct in_addr *netmask)
{
    ETH_DEV *ethdev = netdev->link_data;
    ETH_CFG_INFO new_info;
    long spin_flags;
    int i;

    if (NULL == ip_addr || NULL == netmask)
    {
        return -EINVAL;
    }

    new_info.ip.s_addr = ip_addr->s_addr;
    new_info.netmask.s_addr = netmask->s_addr;

    spin_lock_irqsave(&ethdev->ip_lock, spin_flags);

    for (i = 0; i < ethdev->lwip_netif_count; i += 1)
    {
        if (new_info.ip.s_addr == ethdev->lwip_netif[i]->ip_addr.addr &&
            new_info.netmask.s_addr == ethdev->lwip_netif[i]->netmask.addr)
        {
            break;
        }
    }

    if (i != ethdev->lwip_netif_count)
    {
        spin_unlock_irqrestore(&ethdev->ip_lock, spin_flags);

        return eth_lwip_del_ip_by_netif(ethdev, ethdev->lwip_netif[i]);
    }
    else
    {
        spin_unlock_irqrestore(&ethdev->ip_lock, spin_flags);
        return -ESRCH;
    }
}

#if 0
#ifdef CONFIG_SHELL
#if LWIP_TCP
#include <lwip/priv/tcp_priv.h>
#include <lwip/tcp.h>

void list_tcps (void)
{
    unsigned int num = 0;
    struct tcp_pcb *pcb;
    char local_ip_str[16];
    char remote_ip_str[16];

    extern struct tcp_pcb *tcp_active_pcbs;
    extern union tcp_listen_pcbs_t tcp_listen_pcbs;
    extern struct tcp_pcb *tcp_tw_pcbs;

    KLOG_I ("Active PCB states:\n");
    for (pcb = tcp_active_pcbs; pcb != NULL; pcb = pcb->next)
    {
        strcpy (local_ip_str, ipaddr_ntoa (&(pcb->local_ip)));
        strcpy (remote_ip_str, ipaddr_ntoa (&(pcb->remote_ip)));

        KLOG_I ("#%d %s:%d <==> %s:%d snd_nxt 0x%08X rcv_nxt 0x%08X ", num++,
                local_ip_str, pcb->local_port, remote_ip_str, pcb->remote_port,
                pcb->snd_nxt, pcb->rcv_nxt);
        KLOG_I ("state: %s\n", tcp_debug_state_str (pcb->state));
    }

    KLOG_I ("Listen PCB states:\n");
    num = 0;
    for (pcb = (struct tcp_pcb *)tcp_listen_pcbs.pcbs; pcb != NULL;
         pcb = pcb->next)
    {
        KLOG_I ("#%d local port %d ", num++, pcb->local_port);
        KLOG_I ("state: %s\n", tcp_debug_state_str (pcb->state));
    }

    KLOG_I ("TIME-WAIT PCB states:\n");
    num = 0;
    for (pcb = tcp_tw_pcbs; pcb != NULL; pcb = pcb->next)
    {
        strcpy (local_ip_str, ipaddr_ntoa (&(pcb->local_ip)));
        strcpy (remote_ip_str, ipaddr_ntoa (&(pcb->remote_ip)));

        KLOG_I ("#%d %s:%d <==> %s:%d snd_nxt 0x%08X rcv_nxt 0x%08X ", num++,
                local_ip_str, pcb->local_port, remote_ip_str, pcb->remote_port,
                pcb->snd_nxt, pcb->rcv_nxt);
        KLOG_I ("state: %s\n", tcp_debug_state_str (pcb->state));
    }
    UNUSED_ARG(num);
}
SHELL_EXPORT_CMD (SHELL_CMD_PERMISSION (0) | SHELL_CMD_TYPE (SHELL_TYPE_CMD_FUNC) | SHELL_CMD_DISABLE_RETURN, list_tcps, list_tcps, list all of tcp connections);

#endif /* LWIP_TCP */

#if LWIP_UDP
#include "lwip/udp.h"
void list_udps (void)
{
    struct udp_pcb *pcb;
    unsigned int num = 0;
    char local_ip_str[16];
    char remote_ip_str[16];

    KLOG_I ("Active UDP PCB states:\n");
    for (pcb = udp_pcbs; pcb != NULL; pcb = pcb->next)
    {
        strcpy (local_ip_str, ipaddr_ntoa (&(pcb->local_ip)));
        strcpy (remote_ip_str, ipaddr_ntoa (&(pcb->remote_ip)));

        KLOG_I ("#%d %d %s:%d <==> %s:%d \n", num, (int)pcb->flags, local_ip_str, pcb->local_port, remote_ip_str, pcb->remote_port);

        num++;
    }
}
SHELL_EXPORT_CMD (SHELL_CMD_PERMISSION (0) | SHELL_CMD_TYPE (SHELL_TYPE_CMD_FUNC) | SHELL_CMD_DISABLE_RETURN, list_udps, list_udps, list all of udp connections);
#endif /* LWIP_UDP */

#if defined(LWIP_TCP) || defined(LWIP_UDP)
void lwip_netdev_netstat ()
{
#if LWIP_TCP
    list_tcps ();
#endif
#if LWIP_UDP
    list_udps ();
#endif
}
#endif /* LWIP_TCP || LWIP_UDP */
#endif /* CONFIG_SHELL */

#if LWIP_DNS
#include <lwip/dns.h>
void set_dns (uint8_t dns_num, char *dns_server)
{
    struct in_addr addr;

    if ((dns_server != NULL) && inet_aton (dns_server, &addr))
    {
        // IPv4 Only current
        dns_setserver (dns_num, (const ip_addr_t *)&addr);
    }
}
SHELL_EXPORT_CMD (SHELL_CMD_PERMISSION (0) | SHELL_CMD_TYPE (SHELL_TYPE_CMD_FUNC) | SHELL_CMD_DISABLE_RETURN, set_dns, set_dns, set DNS server address);
#endif
#endif

/* LwIP向NetDev提供的操作 */
const struct netstack_ops lwip_netdev_ops = {
    .set_up = lwip_netdev_set_up,
    .set_down = lwip_netdev_set_down,
    .set_link_up = lwip_netdev_set_link_up,
    .set_link_down = lwip_netdev_set_link_down,
    .set_mac = lwip_netdev_set_mac,
    .set_flag = lwip_netdev_set_flag,
    .set_addr_info = lwip_netdev_set_addr_info,
    .set_mtu = lwip_netdev_set_mtu,
#if LWIP_DHCP
    .set_dhcp = lwip_netdev_set_dhcp,
#else
    .set_dhcp = NULL,
#endif /* LWIP_DHCP */
#ifdef CONFIG_SHELL
#if LWIP_USING_PING
    NULL, // lwip_netdev_ping,
#else
    NULL,
#endif /* LWIP_USING_PING */
#if defined(LWIP_TCP) || defined(LWIP_UDP)
    .netstat = NULL, /* 屏蔽shell命令后目前置空 */
#endif               /* LWIP_TCP || LWIP_UDP */
#endif               /* CONFIG_SHELL */
    .set_default = lwip_netdev_set_default,
};
