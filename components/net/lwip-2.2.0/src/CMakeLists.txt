# 制作 Patch
# 使用 `git format-patch 0a0452b2c39bdd91e252aef045c115f88f6ca773..HEAD --stdout > sys-patch_lwip-2.2.0.patch`
# - 0a0452b2c39bdd91e252aef045c115f88f6ca773 是 STABLE-2_2_0_RELEASE 所在提交
# - 打包从 STABLE-2_2_0_RELEASE（不包含）之后的提交并输出到单个 patch 中
# - 编译时使用 `git am` 打补丁，可打入提交记录，方便协作开发

include(ExternalProject)

set(LWIP_PKG_NAME lwip)
set(LWIP_LIB_NAME "lib${LWIP_PKG_NAME}")

set(URL http://************/Hongdao/intewell-rtos/Third/lwip.git)
set(VERSION STABLE-2_2_0_RELEASE)

set(LWIP_UPDATE_CMD bash ${CMAKE_CURRENT_SOURCE_DIR}/update.sh)
set(LWIP_PATCH_CMD bash ${CMAKE_CURRENT_SOURCE_DIR}/patch.sh)

set(C_FLAGS -O${CONFIG_BUILD_OPTIMIZE})

if(CONFIG_BUILD_DEBUG_INFO)
set(C_FLAGS  ${C_FLAGS}\ -g)
endif(CONFIG_BUILD_DEBUG_INFO)

if(CONFIG_TOOLCHAIN_CLANG)
set(C_FLAGS  ${C_FLAGS}\ -target\ ${CC_TARGET})
endif(CONFIG_TOOLCHAIN_CLANG)

if(CONFIG_ARCH_X86_64)
set(C_FLAGS  ${C_FLAGS}\ -march=x86-64\ -m64\ -mcmodel=large\ -mno-red-zone)
endif(CONFIG_ARCH_X86_64)

if(CONFIG_ARCH_AARCH64 OR CONFIG_ARCH_AARCH32 OR CONFIG_ARCH_ARMv7)
file(WRITE include.cmake "set(INC_PATHS \"${LIBC_INC_PATH}\" \"${KERNEL_INC_PATH}\" \"${KERNEL_INC_PATH}/arch/${CONFIG_ARCH}\" \"${KERNEL_INC_PATH}/arch/arm_common\")\nset(PATCH_INC_PATH \"${CMAKE_CURRENT_SOURCE_DIR}/include\")\nset(AUTOCONF_PATH \"${CMAKE_CURRENT_SOURCE_DIR}/include/autoconfig.h\")")
else()
file(WRITE include.cmake "set(INC_PATHS \"${LIBC_INC_PATH}\" \"${KERNEL_INC_PATH}\" \"${KERNEL_INC_PATH}/arch/${CONFIG_ARCH}\")\nset(PATCH_INC_PATH \"${CMAKE_CURRENT_SOURCE_DIR}/include\")\nset(AUTOCONF_PATH \"${CMAKE_CURRENT_SOURCE_DIR}/include/autoconfig.h\")")
endif()


file(WRITE include/autoconfig.h "#include <${GEN_C_HEADER_DIR}/autoconfig.h>")
file(WRITE update.sh "if [[ -z $(git status --porcelain) ]]; then git checkout 0a0452b2c39bdd91e252aef045c115f88f6ca773; fi")
file(WRITE patch.sh "if [[ -z $(git status --porcelain) ]]; then git -c user.name=\"temp\" -c user.email=\"\" am ${CMAKE_CURRENT_SOURCE_DIR}/sys-patch_lwip-2.2.0.patch; fi")

ExternalProject_Add(${LWIP_LIB_NAME}
    GIT_REPOSITORY  ${URL}
    GIT_TAG         ${VERSION}
    GIT_PROGRESS    true
    UPDATE_COMMAND  ${LWIP_UPDATE_CMD}
    PATCH_COMMAND   ${LWIP_PATCH_CMD}
    EXCLUDE_FROM_ALL 1
    PREFIX          ${CMAKE_BINARY_DIR}
    # BUILD_IN_SOURCE true
    BINARY_DIR      ${CMAKE_CURRENT_BINARY_DIR}/build/build
    TMP_DIR         ${CMAKE_CURRENT_BINARY_DIR}/build/tmp
    STAMP_DIR       ${CMAKE_CURRENT_BINARY_DIR}/build/stamp
    LOG_DIR         ${CMAKE_CURRENT_BINARY_DIR}/build/log
    DOWNLOAD_DIR    ${CMAKE_CURRENT_BINARY_DIR}/build/download
    SOURCE_DIR      ${CMAKE_CURRENT_BINARY_DIR}/build/src
    INSTALL_DIR     ${CMAKE_BINARY_DIR}
    # LOG_BUILD       true
    CMAKE_ARGS -DCMAKE_INSTALL_PREFIX=<INSTALL_DIR>
                -DCMAKE_C_COMPILER=${CMAKE_C_COMPILER}
                -DCMAKE_C_FLAGS=${C_FLAGS}
                -DCONFIG_ARCH=${CONFIG_ARCH}
                -DCMAKE_C_COMPILER_FORCED=ON
                -DCMAKE_PROJECT_lwip_INCLUDE=${CMAKE_CURRENT_SOURCE_DIR}/include.cmake
)

set_property(TARGET ${LWIP_LIB_NAME} APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_CURRENT_BINARY_DIR}/build/build)

if(CONFIG_LWIP_2_2_0)

add_library(${LWIP_PKG_NAME} STATIC IMPORTED GLOBAL)
set_target_properties(${LWIP_PKG_NAME} PROPERTIES IMPORTED_LOCATION ${CMAKE_BINARY_DIR}/${LWIP_LIB_NAME}/libs/${LWIP_LIB_NAME}.a)

target_link_libraries(${PROJECT_NAME} PRIVATE ${LWIP_PKG_NAME})

endif()
