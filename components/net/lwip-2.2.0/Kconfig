if LWIP_2_2_0

config LWIP_EXTERNAL_LIBC
	bool "Use external libc"
	default y

config LWIP_IPV4
	bool "Use IPv4"
	default y

config LWIP_TCP
	bool "Use TCP"
	default y

config LWIP_UDP
	bool "Use UDP"
	default y

config LWIP_RAW
	bool "Use IP Raw"
	default y

config LWIP_ARP
	bool "Use ARP"
	default y

config LWIP_IGMP
	bool "Use IGMP"
	default y

config LWIP_DHCP
	bool "Use DHCP"
	default y

config LWIP_AUTOIP
	bool "Use AutoIP"
	depends on LWIP_DHCP
	default y

config LWIP_DNS
	bool "Use DNS"
	depends on LWIP_UDP
	default y

config LWIP_BROADCAST_LOOPBACK
	bool "LwIP broadcast packets loopback input"
	default n

config LWIP_CORE_LOCKING
	bool "Global mutex for TCPIP thread"
	default y

config LWIP_CORE_LOCKING_INPUT
	bool "Global mutex for input"
	depends on LWIP_CORE_LOCKING
	default y

config LWIP_TCPIP_THREAD_PRIO
	int "TCPIP thread priority"
	default 21

config LWIP_TCPIP_THREAD_STACK
	int "TCPIP thread stack size"
	default 32768

config LWIP_TCPIP_MBOX_SIZE
	int "TCPIP thread message box maximum size"
	default 512

config LWIP_NETCONN_NUM
	int "Parallel netconn structs"
	default 128

config LWIP_NETCONN_ACCEPT_MBOX_SIZE
	int "Netconn accept message box maximum size"
	default 128

config LWIP_NETCONN_TCP_MBOX_SIZE
	int "Netconn TCP receive message box maximum size"
	default 512

config LWIP_NETCONN_UDP_MBOX_SIZE
	int "Netconn UDP receive message box maximum size"
	default 512

config LWIP_NETCONN_RAW_MBOX_SIZE
	int "Netconn RAW receive message box maximum size"
	default 512

config LWIP_TCP_MSS
	int "TCP message segment maximum size"
	default 1460

config LWIP_CHECKSUMS
	bool "LwIP checksums"
	default n

config LWIP_CHECKSUM_COPY
	depends on LWIP_CHECKSUMS
	bool "Enable checksum on copy"
	default n

config LWIP_CHECKSUM_IP
	depends on LWIP_CHECKSUMS
	bool "Enable checksum on IP protocol"
	default n

config LWIP_CHECKSUM_TCP
	depends on LWIP_CHECKSUMS
	bool "Enable checksum on TCP protocol"
	default n

config LWIP_CHECKSUM_UDP
	depends on LWIP_CHECKSUMS
	bool "Enable checksum on UDP protocol"
	default n

config LWIP_CHECKSUM_ICMP
	depends on LWIP_CHECKSUMS
	bool "Enable checksum on ICMP protocol"
	default n

config LWIP_INTERNAL_STATS
	bool "LwIP internal status summary"
	default n

endif
