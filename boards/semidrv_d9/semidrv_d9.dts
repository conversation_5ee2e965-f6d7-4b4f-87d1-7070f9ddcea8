/dts-v1/;

/ {
	#address-cells = <0x02>;
	model = "ZHIYUAN MD9340 Ev Board";
	serial-number = "SDRV-D9-D9340";
	#size-cells = <0x02>;
	interrupt-parent = <0x01>;
	compatible = "zlg,md9340\0semidrive,kunlun";
	
    memory@50000000 
    {
        device_type = "memory";
        reg =   <0x00 0x57200000 0x00 0x20000000>;
    };

    aliases 
    {
        serial0 = "/soc/uart15@30520000";//uart15
    };

    psci
    {
        compatible   = "arm,psci-1.0";
        method       = "smc";
        cpu_suspend  = <0xc4000001>;
        cpu_off      = <0x84000002>;
        cpu_on       = <0xc4000003>;
        sys_poweroff = <0x84000008>;
        sys_reset    = <0x84000009>;
    };
    

	
    // CPU nodes, namely cores. 64 for default, if need more, than need to add manually
	cpus {
		#address-cells = <0x02>;
		#size-cells = <0x00>;

		cpu@0 {
			d-cache-line-size = <0x40>;
			clock-names = "cpu0\0sel0\0ckgen\0pll";
			clocks = <0xd8 0x08 0x06 0x07 0x00>;
			i-cache-line-size = <0x40>;
			device_type = "cpu";
			compatible = "arm,cortex-a55\0arm,armv8";
			d-cache-size = <0x8000>;
			next-level-cache = <0xd9>;
			i-cache-size = <0x8000>;
			reg = <0x00 0x00>;
			enable-method = "psci";
			phandle = <0xc9>;
			d-cache-sets = <0x80>;
			i-cache-sets = <0x80>;
			linux,phandle = <0xc9>;
		};
		
		cpu@1 {
			d-cache-line-size = <0x40>;
			clock-names = "cpu1";
			clocks = <0xda>;
			i-cache-line-size = <0x40>;
			device_type = "cpu";
			compatible = "arm,cortex-a55\0arm,armv8";
			d-cache-size = <0x8000>;
			next-level-cache = <0xdb>;
			i-cache-size = <0x8000>;
			reg = <0x00 0x100>;
			enable-method = "psci";
			phandle = <0xca>;
			d-cache-sets = <0x80>;
			i-cache-sets = <0x80>;
			linux,phandle = <0xca>;
		};
		cpu@2 {
			d-cache-line-size = <0x40>;
			clock-names = "cpu2";
			clocks = <0xdc>;
			i-cache-line-size = <0x40>;
			device_type = "cpu";
			compatible = "arm,cortex-a55\0arm,armv8";
			d-cache-size = <0x8000>;
			next-level-cache = <0xdd>;
			i-cache-size = <0x8000>;
			reg = <0x00 0x200>;
			enable-method = "psci";
			phandle = <0xcb>;
			d-cache-sets = <0x80>;
			i-cache-sets = <0x80>;
			linux,phandle = <0xcb>;
		};
		cpu@3 {
			d-cache-line-size = <0x40>;
			clock-names = "cpu3";
			clocks = <0xde>;
			i-cache-line-size = <0x40>;
			device_type = "cpu";
			compatible = "arm,cortex-a55\0arm,armv8";
			d-cache-size = <0x8000>;
			next-level-cache = <0xdf>;
			i-cache-size = <0x8000>;
			reg = <0x00 0x300>;
			enable-method = "psci";
			phandle = <0xcc>;
			d-cache-sets = <0x80>;
			i-cache-sets = <0x80>;
			linux,phandle = <0xcc>;
		};
		
		l2-cache0 {
			cache-size = <0x20000>;
			cache-sets = <0x200>;
			compatible = "cache";
			cache-line-size = <0x40>;
			next-level-cache = <0xe0>;
			phandle = <0xd9>;
			linux,phandle = <0xd9>;
		};
		l2-cache1 {
			cache-size = <0x20000>;
			cache-sets = <0x200>;
			compatible = "cache";
			cache-line-size = <0x40>;
			next-level-cache = <0xe0>;
			phandle = <0xdb>;
			linux,phandle = <0xdb>;
		};

		l2-cache2 {
			cache-size = <0x20000>;
			cache-sets = <0x200>;
			compatible = "cache";
			cache-line-size = <0x40>;
			next-level-cache = <0xe0>;
			phandle = <0xdd>;
			linux,phandle = <0xdd>;
		};
		
		l2-cache3 {
			cache-size = <0x20000>;
			cache-sets = <0x200>;
			compatible = "cache";
			cache-line-size = <0x40>;
			next-level-cache = <0xe0>;
			phandle = <0xdf>;
			linux,phandle = <0xdf>;
		};

		l3-cache {
			cache-size = <0x100000>;
			cache-sets = <0x400>;
			compatible = "cache";
			cache-line-size = <0x40>;
			phandle = <0xe0>;
			linux,phandle = <0xe0>;
		};
	};

	interrupt-controller@35431000 {

		compatible = "arm,gic-400";
		#interrupt-cells = <0x03>;
		#address-cells = <0x02>;
		reg = <0x0 0x35431000 0x0 0x1000>,	/* GICD */
			<0x0 0x35432000 0x0 0x2000>;	/* GICC */
		phandle = <0x01>;
		interrupts = <0x01 0x09 0x4>;
		interrupt-parent = <0x01>;
		linux,phandle = <0x01>;
		interrupt-controller;
	};

	
	UART_SEC0: UART_SEC0 {
		compatible = "semidrive,clkgen-composite";
		reg =  <0 0x38007000 0 0x1000>;
		#clock-cells = <0>;
		sdrv,type =<0x02>;
		clocks=<0x02 0x35 0x03 0x36 0x41 0x03 0x41 0x04 0x3f 0x04 0x41 0x02>;
		sdrv,prediv = <1>;/*TODO*/
		sdrv,postdiv = <1>;/*TODO*/
		sdrv,clk-readonly = <1>;
		status = "disabled";
	};
	

	UART_SEC0_15: UART_SEC0_15 {
		compatible = "semidrive,clkgen-composite";
		reg =  <0 0x38134000 0 0x1000>;
		#clock-cells = <0>;
		sdrv,type =<0x07>;
		clocks=<&UART_SEC0>;
		status = "disabled";
	};
   soc {
	compatible = "simple-bus";
	#address-cells = <2>;
	#size-cells = <2>;
	ranges;
		
	uart15: uart15@30520000 {
		compatible = "snps,dw-apb-uart";
		reg = <0 0x30520000 0 0x100>;
		reg-shift=<2>;
		reg-io-width=<4>;
		interrupts = <0 30 4>;
		clock-frequency=<60000000>;
		clocks = <0x02 0x35 0x03 0x36 0x41 0x03 0x41 0x04 0x3f 0x04 0x41 0x02>;//<0x3a 0x0b 0x3a 0x2c>;
		clock-names = "baudclk,apb_pclk";
		status = "okay";
	};

	eth@30170000 {
		phy-mode = "rgmii";
		semidrive,multicast_filter_bins = <0x04>;
		semidrive,mcast_bits_log2 = <0x07>;
		local-mac-address = [00 14 97 a3 b6 dc];
		interrupts = <0x00 0x66 0x04>;
		snps,max_dma_cap_quirk = <0x20>;
		compatible = "semidrive,dwc-qos-ethernet";
		status = "okay";
		tx-fifo-depth = <0x2000>;
		rx-fifo-depth = <0x2000>;
		reg = <0x00 0x30170000 0x00 0x10000>;
		snps,force_sf_dma_mode;
		phy-handle = <0xb4>;

		mdio@1 {
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			compatible = "snps,dwmac-mdio";
			status = "okay";

			ethernet-phy@1 {
				rx-delay = [04];
				tx-delay = [07];
				compatible = "ethernet-phy-id4f51.e91a";
				status = "okay";
				reg = <0x05>;
				phandle = <0xb4>;
				linux,phandle = <0xb4>;
				delay-rxclk-2ns;
			};
		};
	};
	eth@306a0000 {
		phy-mode = "rgmii";
		semidrive,multicast_filter_bins = <0x04>;
		semidrive,mcast_bits_log2 = <0x07>;
		local-mac-address = [00 14 97 a3 b6 dd];
		interrupts = <0x00 0x69 0x04>;
		snps,max_dma_cap_quirk = <0x20>;
		compatible = "semidrive,dwc-qos-ethernet";
		status = "okay";
		tx-fifo-depth = <0x2000>;
		rx-fifo-depth = <0x2000>;
		reg = <0x00 0x306a0000 0x00 0x10000>;
		snps,force_sf_dma_mode;
		phy-handle = <0xb7>;

		mdio@2 {
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			compatible = "snps,dwmac-mdio";
			status = "okay";

			ethernet-phy@2 {
				rx-delay = [04];
				tx-delay = [07];
				compatible = "ethernet-phy-id4f51.e91a";
				status = "okay";
				reg = <0x07>;
				phandle = <0xb7>;
				linux,phandle = <0xb7>;
				delay-rxclk-2ns;
			};
		};
	};

	sdhci@34180000 {
		compatible = "snps,dwcmshc-sdhci";
		reg = <0x00000000 0x34180000 0x00000000 0x00010000>;
		interrupts = <0x00000000 0x0000006e 0x00000004 0x00000000 0x0000006f 0x00000004>;
		sdrv,scr_signals_ddr = <0x000000bf>;
		#clock-cells = <0x00000001>;
		clocks = <0x00000093>;
		clock-names = "core";
		vmmc-supply = <0x0000006f>;
		vqmmc-supply = <0x00000094>;
		cap-mmc-hw-reset;
		status = "okay";
		bus-width = <0x00000008>;
		non-removable;
		no-sdio;
		no-sd;
		card-is-emmc;
		disable-wp;
		cap-mmc-highspeed;
		keep-power-in-suspend;
		mmc-ddr-1_8v;
		mmc-hs200-1_8v;
		mmc-hs400-1_8v;
		mmc-hs400-enhanced-strobe;
	};


	OSPI1 {
		#clock-cells = <0x00>;
		clock-frequency = <0xfdad680>;
		compatible = "fixed-clock";
		status = "disabled";
		phandle = <0xa3>;
		linux,phandle = <0xa3>;
	};

	spi@30020000 {
		interrupts = <0x00 0x38 0x04>;
		clocks = <0xa3>;
		cdns,fifo-width = <0x04>;
		cdns,trigger-address = <0x3fffe00>;
		compatible = "sdrv,ospi-nor";
		status = "okay";
		reg = <0x00 0x30020000 0x00 0x10000 0x00 0x4000000 0x00 0x4000000>;
		cdns,fifo-depth = <0x100>;
		flash@0 {
			reg = <0x00000002 0x01000000>;
			spi-rx-bus-width = <0x00000001>;
			spi-max-frequency = <0x02faf080>;
			status = "okay";
		};		
	};

	can1@30030000 {
		compatible = "nxp,flexcan-fd";
		reg = <0x00000000 0x30030000 0x00000000 0x00010000>;
		interrupts = <0x00 118 0x04>;
		status = "okay";
    };

	can2@30040000 {
        compatible = "nxp,flexcan-fd";
		reg = <0x00000000 0x30040000 0x00000000 0x00010000>;
		interrupts = <0x00 119 0x04>;
		status = "okay";
    };

	can3@30050000 {
        compatible = "nxp,flexcan-fd";
		reg = <0x00000000 0x30050000 0x00000000 0x00010000>;
		interrupts = <0x00 120 0x04>;
		status = "okay";
    };

	can4@30060000 {
        compatible = "nxp,flexcan-fd";
		reg = <0x00000000 0x30060000 0x00000000 0x00010000>;
		interrupts = <0x00 121 0x04>;
		status = "okay";
    };

	gpio@30420000 {
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		compatible = "semidrive,sdrv-gpio";
		reg = <0x00 0x30420000 0x00 0x10000>;
		status = "okay";

		gpio-controller@0 {
			compatible = "semidrive,sdrv-gpio-port";
			gpio-controller;
			gpio-ranges = <0xb8 0x00 0x00 0x20>;
			#gpio-cells = <0x02>;
			nr-gpios = <0x20>;
			reg = <0x00>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			interrupts = <0x00 0xec 0x04>;
		};

		gpio-controller@1 {
			compatible = "semidrive,sdrv-gpio-port";
			gpio-controller;
			gpio-ranges = <0xb8 0x00 0x20 0x20>;
			#gpio-cells = <0x02>;
			nr-gpios = <0x20>;
			reg = <0x01>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			interrupts = <0x00 0xed 0x04>;
			linux,phandle = <0x96>;
			phandle = <0x96>;
		};

		gpio-controller@2 {
			compatible = "semidrive,sdrv-gpio-port";
			gpio-controller;
			gpio-ranges = <0xb8 0x00 0x40 0x20>;
			#gpio-cells = <0x02>;
			nr-gpios = <0x20>;
			reg = <0x02>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			interrupts = <0x00 0xee 0x04>;
			linux,phandle = <0xe1>;
			phandle = <0xe1>;
		};

		gpio-controller@3 {
			compatible = "semidrive,sdrv-gpio-port";
			gpio-controller;
			gpio-ranges = <0xb8 0x00 0x60 0x20>;
			#gpio-cells = <0x02>;
			nr-gpios = <0x20>;
			reg = <0x03>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			interrupts = <0x00 0xef 0x04>;
			linux,phandle = <0x6c>;
			phandle = <0x6c>;
		};

		gpio-controller@4 {
			compatible = "semidrive,sdrv-gpio-port";
			gpio-controller;
			gpio-ranges = <0xb8 0x00 0x80 0x20>;
			#gpio-cells = <0x02>;
			nr-gpios = <0x20>;
			reg = <0x04>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			interrupts = <0x00 0xf0 0x04>;
			linux,phandle = <0x75>;
			phandle = <0x75>;
		};
	};
	
	i2c@30ad0000 {
		compatible = "snps,designware-i2c";
		reg = <0x0 0x30ad0000 0x0 0x1000>;
		interrupts = <0x0 0x25 0x4>;
		status = "okay";
		ins5699s@32 {
			compatible = "ins5699s,rtc";
				reg = <0x32>;
		};
	};
	
	i2c@30ae0000 {
		compatible = "snps,designware-i2c";
		reg = <0x0 0x30ae0000 0x0 0x1000>;
		interrupts = <0x0 0x38 0x4>;
		status = "okay";
		lm157@48 {
			compatible = "lm157,temp";
				reg = <0x48>;
		};
	};

	i2c@30ac0000 {
		compatible = "snps,designware-i2c";
		reg = <0x0 0x30ac0000 0x0 0x1000>;
		interrupts = <0x0 0x36 0x4>;
		status = "okay";
		sf2507@5c {
			compatible = "sf2507,switch";
				reg = <0x5c>;
		};
	};
	};
	
    chosen 
    {
        stdout-path = "serial0", "115200n8";
    };
};
