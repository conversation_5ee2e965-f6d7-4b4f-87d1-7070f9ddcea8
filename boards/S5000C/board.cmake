set(BOA<PERSON>_RUNCMD )
set(BOARD_DBGCMD )

add_custom_target(mainfast cp -f ${CMAKE_SOURCE_DIR}/boards/${CONFIG_BOARD_PATH}/mainfast.json.template ${CMAKE_BINARY_DIR}/mainfast.c && ${CMAKE_C_COMPILER} -include ${CONFIG_C_HEADER} -include ${CMAKE_SOURCE_DIR}/include/version.h -E ${CMAKE_BINARY_DIR}/mainfast.c -o ${CMAKE_BINARY_DIR}/mainfast.json && sed -i "/^#/d" ${CMAKE_BINARY_DIR}/mainfast.json)
add_custom_target(downloadrootfs COMMAND wget http://*************/repository/download/Intewell-N/rootfs/${CONFIG_ARCH}/rootfs.bin -O ${CMAKE_BINARY_DIR}/rootfs.bin)
add_custom_target(idepack COMMAND zip -j ${CONFIG_BOARD_PATH}.zip ${CMAKE_BINARY_DIR}/rtos.bin ${CMAKE_BINARY_DIR}/rtos.elf ${CMAKE_BINARY_DIR}/rootfs.bin ${CMAKE_BINARY_DIR}/mainfast.json)
add_dependencies(idepack downloadrootfs mainfast)
