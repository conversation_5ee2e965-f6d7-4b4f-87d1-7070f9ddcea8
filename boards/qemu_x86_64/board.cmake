set(QEMU_ARGS  -machine type=pc-q35-6.2 -cpu max 
        -smp 2  -nographic  -serial mon:stdio -m 2G 
        -netdev tap,id=tapnet,ifname=tap0,script=no,downscript=no,br=0,vhost=on,queues=2 
        -device virtio-net-pci,netdev=tapnet,mq=on,packed=on,mac=52:54:10:11:12:14)

if(DEFINED CONFIG_ROOTFS_EXT4)
    list(APPEND QEMU_ARGS
        -device virtio-blk-pci,drive=hd0 
        -drive file=sd1.bin,if=none,id=hd0,format=raw )
endif()


if(${CMAKE_HOST_SYSTEM_NAME} STREQUAL "Windows")
    set(BOARD_RUNCMD qemu-system-x86_64 ${QEMU_ARGS} -kernel)
    set(BOARD_DBGCMD qemu-system-x86_64 ${QEMU_ARGS} -S -gdb tcp::${CONFIG_DEBUG_PORT} -kernel)
else()
    set(BOARD_RUNCMD sudo qemu-system-x86_64 ${QEMU_ARGS} -kernel)
    set(BOARD_DBGCMD sudo qemu-system-x86_64 ${QEMU_ARGS} -S -gdb tcp::${CONFIG_DEBUG_PORT} -kernel)
endif()

add_custom_target(mainfast cp -f ${CMAKE_SOURCE_DIR}/boards/${CONFIG_BOARD_PATH}/mainfast.json.template ${CMAKE_BINARY_DIR}/mainfast.c && ${CMAKE_C_COMPILER} -include ${CONFIG_C_HEADER} -include ${CMAKE_SOURCE_DIR}/include/version.h -E ${CMAKE_BINARY_DIR}/mainfast.c -o ${CMAKE_BINARY_DIR}/mainfast.json && sed -i "/^#/d" ${CMAKE_BINARY_DIR}/mainfast.json)
add_custom_target(downloadrootfs COMMAND wget http://*************/repository/download/Intewell-N/rootfs/${CONFIG_ARCH}/rootfs.bin -O ${CMAKE_BINARY_DIR}/rootfs.bin)
add_custom_target(idepack COMMAND zip -j ${CONFIG_BOARD_PATH}.zip ${CMAKE_BINARY_DIR}/rtos.bin ${CMAKE_BINARY_DIR}/rtos.elf ${CMAKE_BINARY_DIR}/rootfs.bin ${CMAKE_BINARY_DIR}/mainfast.json)
add_dependencies(idepack downloadrootfs mainfast)


add_custom_target(iso COMMAND ${BUILD_ENV_PATH}/tools/grub/pack.sh ${CMAKE_BINARY_DIR}/rtos.bin ${CMAKE_BINARY_DIR}/Intewell_RTOS${VERSION_TAG}.iso ${CMAKE_BINARY_DIR}/rootfs.bin COMMENT "Creating ISO file")
add_dependencies(iso downloadrootfs)

add_custom_target(runiso COMMAND qemu-system-x86_64 ${QEMU_ARGS} -cdrom ${CMAKE_BINARY_DIR}/Intewell_RTOS${VERSION_TAG}.iso COMMENT "Running QEMU with ISO file")