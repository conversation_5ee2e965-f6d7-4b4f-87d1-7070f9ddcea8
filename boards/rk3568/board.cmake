# set(BOA<PERSON>_RUNCMD)
# set(BOA<PERSON>_DBGCMD)
set(<PERSON>OARD_MKIMG ${BUILD_ENV_PATH}/tools/ext4_img.sh 128 ${CMAKE_SOURCE_DIR}/boards/${CONFIG_BOARD_PATH}/rk3568-firefly-itx-3568q.dtb ${CMAKE_BINARY_DIR}/System.map ${CMAKE_BINARY_DIR}/.config)

add_custom_target(linktarget COMMAND ln -sf ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.img ${BUILD_ENV_PATH}/tools/rockchip-pack/Image/rtos.img)
add_custom_target(downloadrootfs COMMAND test -f ${BUILD_ENV_PATH}/tools/rockchip-pack/Image/rootfs.img || wget http://*************/repository/download/Intewell-N/rootfs/aarch64/rootfs.bin -O ${BUILD_ENV_PATH}/tools/rockchip-pack/Image/rootfs.img)
add_custom_target(resizeimg COMMAND e2fsck -f ${BUILD_ENV_PATH}/tools/rockchip-pack/Image/rootfs.img -y && resize2fs ${BUILD_ENV_PATH}/tools/rockchip-pack/Image/rootfs.img 2G)
add_custom_target(updateimg COMMAND cd ${BUILD_ENV_PATH}/tools/rockchip-pack && sh build.sh ${CMAKE_BINARY_DIR}/update.img)
add_dependencies(resizeimg downloadrootfs)
add_dependencies(updateimg linktarget resizeimg)
