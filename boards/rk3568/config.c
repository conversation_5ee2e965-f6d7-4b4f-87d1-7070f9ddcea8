/************************************************************************
 *              科东(广州)软件科技有限公司 版权所有
 *   Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 ***********************************************************************/

/*
 * 修改历史：
 * 2023-06-24    彭元志，科东(广州)软件科技有限公司
 *               创建该文件。
 */

/*
 * @file: configs.c
 * @brief:
 *    <li> 系统配置 </li>
 */

/************************头 文 件******************************/
#include <fs/fs.h>
#include <fs/partition.h>
#include <stddef.h>
#include <stdint.h>
#include <ttos_init.h>
#include <unistd.h>

#define KLOG_TAG "board"
#include <klog.h>
/************************宏 定 义******************************/

/************************类型定义******************************/

/************************外部声明******************************/

/************************前向声明******************************/

/************************模块变量******************************/

extern int dtb_data, dtb_end;
/************************全局变量******************************/

/************************函数实现******************************/

void *board_get_dtb(size_t *size)
{
    if (size)
    {
        *size = (uintptr_t)&dtb_end - (uintptr_t)&dtb_data;
    }
    return (void *)&dtb_data;
}

void board_earlycon_uart_config(char **compatible, unsigned long *base)
{
    static char board_compatible[] = "snps,dw-apb-uart";

    *compatible = board_compatible;
    *base = 0xfe660000;
}

static int board_rootfs_init(void)
{
    int ret = 0;

#ifdef CONFIG_FS_LWEXT4
    if (access("/dev/mmcblk", F_OK) == 0)
    {
        parse_block_partition("/dev/mmcblk", NULL, NULL);
    }

    if (access("/dev/_initrd", F_OK) == 0)
    {
        vfs_mount("/dev/_initrd", "/", "ext4", 0, "autoformat");
    }
    else if (access("/dev/userdata", F_OK) == 0)
    {
        ret = vfs_mount("/dev/userdata", "/", "ext4", 0, "autoformat");

        if (ret == 0)
        {
            vfs_mkdir("/boot", 0666);
            vfs_mount("/dev/boot", "/boot", "ext4", 0, "autoformat");
        }
    }
    else if (access("/dev/virtblk0", F_OK) == 0)
    {
        vfs_mount("/dev/virtblk0", "/", "ext4", 0, "autoformat");
    }
#endif /* CONFIG_FS_LWEXT4 */
    ret = vfs_mount(NULL, "/run", "tmpfs", 0, "autoformat");
    if (ret < 0)
    {
        KLOG_E("ERROR: Failed to mount ramfs /run: %d\n", ret);
        return ret;
    }

    return ret;
}
INIT_EXPORT_ROOT_FS(board_rootfs_init, "rootfs init");
