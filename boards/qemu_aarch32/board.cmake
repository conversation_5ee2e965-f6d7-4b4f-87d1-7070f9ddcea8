set(QEMU_ARGS
    -smp cpus=2 -m 1G
    -machine virt,gic-version=3 -nographic
    -global virtio-mmio.force-legacy=false
    -netdev tap,id=tapnet,ifname=tap0,script=no,downscript=no,br=0,vhost=on,queues=2
    -device virtio-net-device,netdev=tapnet,mq=on,packed=on,mac=52:54:10:11:12:13)


if(DEFINED CONFIG_ROOTFS_EXT4)
    list(APPEND QEMU_ARGS
                -drive file=sd1.bin,if=none,format=raw,id=hd 
                -device virtio-blk-device,drive=hd,bus=virtio-mmio-bus.0)
endif()

if(${CMAKE_HOST_SYSTEM_NAME} STREQUAL "Windows")
    set(BOARD_RUNCMD qemu-system-arm ${QEMU_ARGS} -kernel)
    set(BOARD_DBGCMD qemu-system-arm ${QEMU_ARGS} -S -gdb tcp::${CONFIG_DEBUG_PORT} -kernel)
else()
    set(BOARD_RUNCMD sudo qemu-system-arm ${QEMU_ARGS} -kernel)
    set(BOARD_DBGCMD sudo qemu-system-arm ${QEMU_ARGS} -S -gdb tcp::${CONFIG_DEBUG_PORT} -kernel)
endif()

add_custom_target(mainfast cp -f ${CMAKE_SOURCE_DIR}/boards/${CONFIG_BOARD_PATH}/mainfast.json.template ${CMAKE_BINARY_DIR}/mainfast.c && ${CMAKE_C_COMPILER} -include ${CONFIG_C_HEADER} -include ${CMAKE_SOURCE_DIR}/include/version.h -E ${CMAKE_BINARY_DIR}/mainfast.c -o ${CMAKE_BINARY_DIR}/mainfast.json && sed -i "/^#/d" ${CMAKE_BINARY_DIR}/mainfast.json)
add_custom_target(downloadrootfs COMMAND wget http://*************/repository/download/Intewell-N/rootfs/${CONFIG_ARCH}/rootfs.bin -O ${CMAKE_BINARY_DIR}/rootfs.bin)
add_custom_target(idepack COMMAND zip -j ${CONFIG_BOARD_PATH}.zip ${CMAKE_BINARY_DIR}/rtos.bin ${CMAKE_BINARY_DIR}/rtos.elf ${CMAKE_BINARY_DIR}/rootfs.bin ${CMAKE_BINARY_DIR}/mainfast.json)
add_dependencies(idepack downloadrootfs mainfast)