CONFIG_PROJECT_NAME="rtos"

#
# 目标配置
#
# CONFIG_TOOLCHAIN_CLANG is not set
CONFIG_TOOLCHAIN_GCC=y
CONFIG_ARCH_AARCH64=y
CONFIG_ARM_64=y
CONFIG_OS_LP64=y
CONFIG_ARCH_ARM=y
# CONFIG_CHOICE_ARCH_ARMv7 is not set
# CONFIG_CHOICE_ARCH_AARCH32 is not set
CONFIG_CHOICE_ARCH_AARCH64=y
# CONFIG_CHOICE_ARCH_X86_64 is not set
CONFIG_TARGET_CPU_CORTEX_A55=y
# CONFIG_TARGET_CPU_CORTEX_A53 is not set
CONFIG_KERNEL_SPACE_START=0xFFFF000000000000
CONFIG_ARCH="aarch64"
CONFIG_SUB_ARCH=""
CONFIG_SMP=y
CONFIG_MAX_CPUS=8
CONFIG_CPU_USAGE_MONITOR=y
CONFIG_BUILD_OPTIMIZE="2"
CONFIG_BUILD_DEBUG_INFO=y

#
# Hardware
#
CONFIG_ISR_STACK_SIZE=0x2000
CONFIG_HARD_FLOAT=y
# end of Hardware
# end of 目标配置

#
# BSP配置
#
# CONFIG_QEMU_AARCH64 is not set
# CONFIG_S5000C is not set
# CONFIG_QEMU_AARCH32 is not set
# CONFIG_FT_E2000S_AARCH32 is not set
# CONFIG_RK3568_AARCH64 is not set
# CONFIG_QEMU_X86_64 is not set
CONFIG_SEMIDRV_D9_AARCH64=y
# CONFIG_FMSH is not set
CONFIG_BOARD_PATH="semidrv_d9"
# end of BSP配置

#
# Kernel
#

#
# memory
#
CONFIG_TLSF=y
# CONFIG_WORKSPACE is not set
CONFIG_KERNEL_HEAP_SIZE=0x10000000
CONFIG_KERNEL_NC_HEAP_SIZE=0x100000
# CONFIG_MM_KASAN is not set
# CONFIG_MM_UBSAN is not set
# end of memory

#
# process
#
CONFIG_KERNEL_PROCESS_STACKSIZE=0x100000
CONFIG_PROCESS_MAX=65535
# end of process

#
# task
#
CONFIG_PROTECT_STACK=y
# end of task

#
# secure
#
# CONFIG_FIRM_VERIFY is not set
# end of secure
# end of Kernel

#
# 驱动配置
#
CONFIG_CLK=y
# CONFIG_CLK_ROCKCHIP is not set

#
# Network
#
# CONFIG_STMMAC_ETH is not set
CONFIG_SEMIDRIVE_ETH=y
# end of Network

# CONFIG_PHY is not set
CONFIG_PCI=y

#
# Serial
#
CONFIG_UART_TX_BUF_SIZE=0x4000
CONFIG_UART_RX_BUF_SIZE=0x4000
CONFIG_UART_BAUD=115200
CONFIG_UART_PL011=y
# end of Serial

#
# Irqchip Select
#
# CONFIG_GIC_VERSION_V3 is not set
CONFIG_GIC_VERSION_V2=y
# end of Irqchip Select

#
# Timer Select
#
CONFIG_ARM_GEN_TIMER=y
# end of Timer Select

CONFIG_BCH=y
CONFIG_BCH_BUFFER_ALIGNMENT=0
CONFIG_DRIVERS_VIRTIO=y
CONFIG_VIRTIO_MMIO=y
CONFIG_DRIVERS_VIRTIO_MMIO_QUEUE_LEN=256
CONFIG_VIRTIO_MMIO_BLK=y
CONFIG_VIRTIO_PCI=y
CONFIG_VIRTIO_MMIO_NET=y
CONFIG_PSEUDOTERM=y
CONFIG_PSEUDOTERM_SUSV1=y
CONFIG_PSEUDOTERM_RXBUFSIZE=4096
CONFIG_PSEUDOTERM_TXBUFSIZE=4096
CONFIG_MMCSD=y
CONFIG_MMCSD_IOCSUPPORT=y
CONFIG_MMCSD_NSLOTS=1
# CONFIG_MMCSD_PROCFS is not set
# CONFIG_MMCSD_READONLY is not set
CONFIG_MMCSD_MULTIBLOCK_LIMIT=0
CONFIG_MMCSD_MMCSUPPORT=y
CONFIG_MMCSD_HAVE_CARDDETECT=y
CONFIG_MMCSD_HAVE_WRITEPROTECT=y
# CONFIG_MMCSD_SDIO is not set
CONFIG_ENABLE_SDHCI=y
CONFIG_OSPI=y
CONFIG_SEMIDRV_OSPI=y
# CONFIG_SPI is not set
CONFIG_GPIO=y
# CONFIG_E2000_GPIO is not set
# CONFIG_GPIO_DWAPB is not set
CONFIG_SEMIDRV_GPIO=y
# CONFIG_INPUT is not set

#
# Firmware
#
# end of Firmware

CONFIG_CAN=y
CONFIG_CAN_RX_BUF_SIZE=0x400
CONFIG_FLEX_CANFD=y
CONFIG_FLEXCAN_BUS_CLK=80000000
CONFIG_FLEXCAN_TXMB=4
CONFIG_FLEXCAN_RXMB=10
CONFIG_TRANS_RECV_QUEUE_NUM=64
CONFIG_FLEXCAN_SUPPORT_CANFD=y
CONFIG_FLEXCAN_ARBI_BITRATE=1000000
CONFIG_FLEXCAN_ARBI_SAMPLEP=75
CONFIG_FLEXCAN_DATA_BITRATE=2000000
CONFIG_FLEXCAN_DATA_SAMPLEP=85
CONFIG_SEMIDRV_I2C=y
# end of 驱动配置

#
# Components
#

#
# Module
#
# CONFIG_STRICT_MODULE_RWX is not set
CONFIG_MODULES=y
CONFIG_MODULE_UNLOAD=y
# end of Module

#
# Log
#
CONFIG_KLOG_BUFF_SIZE=0x1000000
# CONFIG_LOG_LEVEL_DEBUG is not set
# CONFIG_LOG_LEVEL_INFO is not set
# CONFIG_LOG_LEVEL_WARN is not set
CONFIG_LOG_LEVEL_ERROR=y
# CONFIG_LOG_LEVEL_EMERG is not set
# end of Log

#
# Shell
#
CONFIG_SHELL=y
CONFIG_SHELL_CMD_GROUP=y
# end of Shell

CONFIG_NETWORK=y
CONFIG_TCPIP=y
CONFIG_LWIP_2_2_0=y
CONFIG_LWIP_EXTERNAL_LIBC=y
CONFIG_LWIP_IPV4=y
CONFIG_LWIP_TCP=y
CONFIG_LWIP_UDP=y
CONFIG_LWIP_RAW=y
CONFIG_LWIP_ARP=y
CONFIG_LWIP_IGMP=y
CONFIG_LWIP_DHCP=y
CONFIG_LWIP_AUTOIP=y
CONFIG_LWIP_DNS=y
# CONFIG_LWIP_BROADCAST_LOOPBACK is not set
CONFIG_LWIP_CORE_LOCKING=y
CONFIG_LWIP_CORE_LOCKING_INPUT=y
CONFIG_LWIP_TCPIP_THREAD_PRIO=21
CONFIG_LWIP_TCPIP_THREAD_STACK=32768
CONFIG_LWIP_TCPIP_MBOX_SIZE=32
CONFIG_LWIP_NETCONN_NUM=128
CONFIG_LWIP_NETCONN_ACCEPT_MBOX_SIZE=32
CONFIG_LWIP_NETCONN_TCP_MBOX_SIZE=32
CONFIG_LWIP_NETCONN_UDP_MBOX_SIZE=4096
CONFIG_LWIP_NETCONN_RAW_MBOX_SIZE=4096
CONFIG_LWIP_TCP_MSS=1460
# CONFIG_LWIP_CHECKSUMS is not set
# CONFIG_LWIP_INTERNAL_STATS is not set
CONFIG_MAX_NETIF_IPS=10
CONFIG_NETPKT_RESERVED_LEN=32
CONFIG_ETH_RX_TASK_PRIORITY=0
CONFIG_ETH_RX_TASK_STACK_SIZE=65536
CONFIG_ETH_RX_TASK_AFFINITY=0
CONFIG_PHY_MONITOR_TASK_PRIORITY=253
CONFIG_PHY_MONITOR_CHECK_PERIOD=2
CONFIG_SUPPORT_AF_PACKET_RECV_ARP=y
# CONFIG_SUPPORT_PCAP_TOOL is not set
CONFIG_USER_SHELL_NET_INIT=y
CONFIG_UNIX_SOCKET=y
CONFIG_NET_LOCAL_VFS_PATH="/dev"

#
# FileSystem
#
CONFIG_NFILE_DESCRIPTORS_PER_BLOCK=8
CONFIG_LIBC_HOMEDIR="/"
CONFIG_LIBC_TMPDIR="/tmp"
CONFIG_LIBC_MAX_TMPFILE=32

#
# File system configuration
#
# CONFIG_DISABLE_MOUNTPOINT is not set
CONFIG_FS_LARGEFILE=y
# CONFIG_FS_AUTOMOUNTER is not set
CONFIG_FS_NEPOLL_DESCRIPTORS=8
# CONFIG_DISABLE_PSEUDOFS_OPERATIONS is not set
CONFIG_PSEUDOFS_ATTRIBUTES=y
CONFIG_PSEUDOFS_SOFTLINKS=y
CONFIG_SENDFILE_BUFSIZE=512
CONFIG_EVENT_FD=y
CONFIG_EVENT_FD_VFS_PATH="/dev/event"
CONFIG_EVENT_FD_POLL=y
CONFIG_EVENT_FD_NPOLLWAITERS=4
# CONFIG_TIMER_FD is not set
# CONFIG_FS_NAMED_SEMAPHORES is not set
CONFIG_FS_MQUEUE_VFS_PATH="/dev/mqueue"
CONFIG_FS_MQUEUE_NPOLLWAITERS=4

#
# Partition Table
#
# CONFIG_PTABLE_PARTITION is not set
CONFIG_MBR_PARTITION=y
CONFIG_GPT_PARTITION=y
# CONFIG_TXTABLE_PARTITION is not set
# end of Partition Table

CONFIG_FS_LWEXT4=y
# CONFIG_FS_FAT is not set
CONFIG_NFS=y
# CONFIG_NFS_STATISTICS is not set
# CONFIG_FS_ROMFS is not set
# CONFIG_FS_CROMFS is not set
CONFIG_FS_TMPFS=y
CONFIG_FS_TMPFS_BLOCKSIZE=512
CONFIG_FS_TMPFS_DIRECTORY_ALLOCGUARD=64
CONFIG_FS_TMPFS_DIRECTORY_FREEGUARD=128
CONFIG_FS_TMPFS_FILE_ALLOCGUARD=512
CONFIG_FS_TMPFS_FILE_FREEGUARD=1024
CONFIG_FS_SHMFS=y
CONFIG_FS_SHMFS_BLOCKSIZE=512
CONFIG_FS_SHMFS_DIRECTORY_ALLOCGUARD=64
CONFIG_FS_SHMFS_DIRECTORY_FREEGUARD=128
CONFIG_FS_SHMFS_FILE_ALLOCGUARD=512
CONFIG_FS_SHMFS_FILE_FREEGUARD=1024
# CONFIG_FS_SMARTFS is not set
CONFIG_FS_PROCFS=y
# CONFIG_FS_LITTLEFS is not set
# CONFIG_FS_UNIONFS is not set
# CONFIG_FS_HOSTFS is not set
# end of FileSystem

#
# Trace
#
# CONFIG_TRACING is not set
# end of Trace

#
# Coredump
#
# CONFIG_COREDUMP is not set
# end of Coredump

CONFIG_ALLSYMS=y
# CONFIG_SYMTAB_ORDEREDBYNAME is not set
# CONFIG_SYMTAB_ORDEREDBYVALUE is not set
# CONFIG_SYMTAB_DECORATED is not set
# end of Components
