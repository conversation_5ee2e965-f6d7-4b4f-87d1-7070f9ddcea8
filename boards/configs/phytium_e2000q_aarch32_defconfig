CONFIG_TOOLCHAIN_GCC=y
CONFIG_CHOICE_ARCH_AARCH32=y
CONFIG_BOOTING_FROM_AARCH64=y
CONFIG_BUILD_OPTIMIZE="0"
CONFIG_FT_E2000S_AARCH32=y
CONFIG_ARCH_CHIP_PHYTIUM_E2000Q=y
CONFIG_PHYTIUM_GPIO=y
CONFIG_PHYTIUM_QSPI=y
CONFIG_PHYTIUM_SDIO=y
CONFIG_PHYTIUM_XMAC=y
CONFIG_PHYTIUM_SDIO_CARD=y
CONFIG_PHYTIUM_SDIO_PULLUP=y
CONFIG_PHYTIUM_SDIO_DMA=y
CONFIG_PHYTIUM_SDMMC_50MHZ=y
CONFIG_KERNEL_SPACE_START=0xC0000000
CONFIG_KERNEL_HEAP_SIZE=0x10000000
CONFIG_KERNEL_NC_HEAP_SIZE=0x800000
CONFIG_KERNEL_PROCESS_STACKSIZE=0x100000
CONFIG_BCH=y
CONFIG_PSEUDOTERM=y
CONFIG_PSEUDOTERM_RXBUFSIZE=4096
CONFIG_PSEUDOTERM_TXBUFSIZE=4096
CONFIG_MMCSD=y
CONFIG_MMCSD_MULTIBLOCK_LIMIT=1024
# CONFIG_MMCSD_MMCSUPPORT is not set
CONFIG_MMCSD_SDIO=y
CONFIG_GPIO=y
CONFIG_E2000_GPIO=y
CONFIG_PSEUDOFS_SOFTLINKS=y
CONFIG_FS_RAMMAP=y
CONFIG_MBR_PARTITION=y
CONFIG_GPT_PARTITION=y
CONFIG_FS_LWEXT4=y
CONFIG_FS_TMPFS=y
CONFIG_FS_SHMFS=y
CONFIG_FS_PROCFS=y
# CONFIG_COREDUMP is not set
CONFIG_ALLSYMS=y
