/dts-v1/;

/ {
	#address-cells = <0x01>;
	#size-cells = <0x01>;
	model = "TLFM20S-EVM";
	interrupt-parent = <0x04>;
	compatible = "fmsh,fmsh-psoc";

	chosen {
		bootargs = "console=ttyPS0,115200 earlyprintk loglevel=8 root=/dev/mmcblk0p2 rw";
		stdout-path = "serial0:115200n8";
		linux,initrd-start = <0x1b100000>;
		linux,initrd-end = <0x1f100000>;
	};

	aliases {
		ethernet0 = "/amba@0/ethernet@e0047000";
		ethernet1 = "/amba@0/ethernet@e0049000";
		serial0 = "/amba@0/serial@e0004000";
		serial1 = "/amba@0/serial@e0023000";
		spi0 = "/amba@0/qspi@e0000000";
		spi1 = "/amba@0/qspi@e0020000";
		spi2 = "/amba@0/spi@e0001000";
		spi3 = "/amba@0/spi@e0021000";
		mmc0 = "/amba@0/dwmmc@e0043000";
		mmc1 = "/amba@0/dwmmc@e0044000";
		i2c0 = "/amba@0/i2c@e0002000";
		i2c1 = "/amba@0/i2c@e0022000";
	};

	memory {
		device_type = "memory";
		reg = <0x100000 0x3ff00000>;
	};

	cpus {
		#address-cells = <0x01>;
		#size-cells = <0x00>;

		cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0x00>;
			clocks = <0x01 0x03>;
			operating-points-v2 = <0x02>;
			linux,phandle = <0x05>;
			phandle = <0x05>;
		};

		cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0x01>;
			clocks = <0x01 0x03>;
			linux,phandle = <0x06>;
			phandle = <0x06>;
		};

		cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0x02>;
			clocks = <0x01 0x03>;
			linux,phandle = <0x07>;
			phandle = <0x07>;
		};

		cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0x03>;
			clocks = <0x01 0x03>;
			linux,phandle = <0x08>;
			phandle = <0x08>;
		};
	};

	opp-table {
		compatible = "operating-points-v2";
		linux,phandle = <0x02>;
		phandle = <0x02>;

		opp-1000000000 {
			opp-hz = <0x00 0x3b9aca00>;
			opp-microvolt = <0xf4240>;
		};

		opp-800000000 {
			opp-hz = <0x00 0x2faf0418>;
			opp-microvolt = <0xf4240>;
		};

		opp-666666666 {
			opp-hz = <0x00 0x27bc8410>;
			opp-microvolt = <0xf4240>;
		};
	};

	fpga-full {
		compatible = "fpga-region";
		fpga-mgr = <0x03>;
		#address-cells = <0x01>;
		#size-cells = <0x01>;
		ranges;
		linux,phandle = <0x0f>;
		phandle = <0x0f>;
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupt-parent = <0x04>;
		interrupts = <0x00 0x04 0x04 0x00 0x05 0x04 0x00 0x06 0x04 0x00 0x07 0x04>;
		interrupt-affinity = <0x05 0x06 0x07 0x08>;
		status = "disabled";
	};

	ocmc@0 {
		compatible = "fmsh,fmql-ocmc-1.0";
		status = "disabled";
		linux,phandle = <0x10>;
		phandle = <0x10>;
	};

	amba@0 {
		u-boot,dm-pre-reloc;
		compatible = "simple-bus";
		#address-cells = <0x01>;
		#size-cells = <0x01>;
		interrupt-parent = <0x04>;
		ranges;
		linux,phandle = <0x11>;
		phandle = <0x11>;

		interrupt-controller@f8901000 {
			compatible = "arm,cortex-a7-gic";
			#interrupt-cells = <0x03>;
			#address-cells = <0x01>;
			interrupt-controller;
			reg = <0xf8901000 0x1000 0xf8902000 0x100>;
			linux,phandle = <0x04>;
			phandle = <0x04>;
		};

		slcr@e0026000 {
			u-boot,dm-pre-reloc;
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			compatible = "fmsh,psoc-slcr\0syscon\0simple-mfd";
			reg = <0xe0026000 0x1000>;
			ranges;
			linux,phandle = <0x09>;
			phandle = <0x09>;

			clkc@100 {
				u-boot,dm-pre-reloc;
				compatible = "fmsh,psoc-clkc";
				reg = <0x100 0x100>;
				#clock-cells = <0x01>;
				ps-clk-frequency = <0x1fca055>;
				osc-clk-frequency = <0x7ffd>;
				fclk-enable = <0x00>;
				clock-output-names = "armpll\0ddrpll\0iopll\0cpu\0axi\0ahb\0apb\0axi_cpu\0ddrx1\0ddrx4\0axi_ddr\0apb_ddr\0gtimer\0gmac0_tx\0gmac1_tx\0fclk0\0fclk1\0fclk2\0fclk3\0gmac0_rx\0gmac1_rx\0axi_gmac0\0axi_gmac1\0ahb_gmac0\0ahb_gmac1\0ahb_smc\0ahb_nfc\0nfc\0qspi\0ahb_qspi\0apb_qspi\0sdio0\0sdio1\0ahb_sdio0\0ahb_sdio1\0uart0\0uart1\0apb_uart0\0apb_uart1\0spi0\0spi1\0apb_spi0\0apb_spi1\0apb_can0\0apb_can1\0apb_gpio\0apb_i2c0\0apb_i2c1\0ahb_usb0\0ahb_usb1\0usb0_phy\0usb1_phy\0ahb_dmac\0wdt\0apb_wdt\0ttc0_ref1\0ttc0_ref2\0ttc0_ref3\0ttc1_ref1\0ttc1_ref2\0ttc1_ref3\0apb_ttc0\0apb_ttc1\0ahb_pcap";
				linux,phandle = <0x01>;
				phandle = <0x01>;
			};
		};

		ddr_umc@e0029000 {
			compatible = "fmsh,psoc-ddr-umc\0syscon\0simple-mfd";
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			reg = <0xe0029000 0x17000>;
			linux,phandle = <0x0a>;
			phandle = <0x0a>;
		};

		devcfg@e0040000 {
			compatible = "fmsh,fmql-devcfg-1.0";
			reg = <0xe0040000 0x1000>;
			interrupt-parent = <0x04>;
			interrupts = <0x00 0x08 0x04>;
			clocks = <0x01 0x3f>;
			clock-names = "ref_clk";
			syscon = <0x09>;
			ddrcon = <0x0a>;
			status = "okay";
			linux,phandle = <0x03>;
			phandle = <0x03>;
		};

		timer {
			compatible = "arm,armv7-timer";
			interrupts = <0x01 0x0d 0xf04 0x01 0x0e 0xf04 0x01 0x0b 0xf04 0x01 0x0a 0xf04>;
			arm,cpu-registers-not-fw-configured;
			status = "okay";
			linux,phandle = <0x12>;
			phandle = <0x12>;
		};

		smc@0 {
			compatible = "fmsh,psoc-smc\0simple-bus";
			reg = <0xe0041000 0x1000>;
			clocks = <0x01 0x19>;
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			bank-width = <0x01>;
			ranges;
			linux,phandle = <0x13>;
			phandle = <0x13>;

			sram@e2000000 {
				compatible = "samsung,k6f1616u6a\0mtd-ram";
				reg = <0xe2000000 0x2000000>;
				#address-cells = <0x01>;
				#size-cells = <0x01>;
				bank-width = <0x01>;
				fmsh,smc-type = "sram";
				fmsh,smc-cs = <0x00>;
				status = "disabled";
				linux,phandle = <0x14>;
				phandle = <0x14>;
			};

			sram@e4000000 {
				compatible = "samsung,k6f1616u6a\0mtd-ram";
				reg = <0xe4000000 0x2000000>;
				#address-cells = <0x01>;
				#size-cells = <0x01>;
				bank-width = <0x01>;
				fmsh,smc-type = "sram";
				fmsh,smc-cs = <0x01>;
				status = "disabled";
				linux,phandle = <0x15>;
				phandle = <0x15>;
			};

			flash@e2000000 {
				compatible = "amd,am29lv128ml\0cfi-flash";
				reg = <0xe2000000 0x2000000>;
				bank-width = <0x01>;
				device-width = <0x01>;
				#address-cells = <0x01>;
				#size-cells = <0x01>;
				fmsh,smc-type = "flash";
				fmsh,smc-cs = <0x00>;
				status = "disabled";
				linux,phandle = <0x16>;
				phandle = <0x16>;
			};

			flash@e4000000 {
				compatible = "amd,am29lv128ml\0cfi-flash";
				reg = <0xe4000000 0x2000000>;
				bank-width = <0x01>;
				device-width = <0x01>;
				#address-cells = <0x01>;
				#size-cells = <0x01>;
				fmsh,smc-type = "flash";
				fmsh,smc-cs = <0x01>;
				status = "disabled";
				linux,phandle = <0x17>;
				phandle = <0x17>;
			};
		};

		serial@e0004000 {
			compatible = "snps,dw-apb-uart";
			clocks = <0x01 0x23 0x01 0x25>;
			clock-names = "baudclk\0apb_pclk";
			reg = <0xe0004000 0x1000>;
			interrupts = <0x00 0x17 0x04>;
			reg-shift = <0x02>;
			reg-io-width = <0x04>;
			u-boot,dm-pre-reloc;
			status = "okay";
			linux,phandle = <0x18>;
			phandle = <0x18>;
		};

		serial@e0023000 {
			compatible = "snps,dw-apb-uart";
			clocks = <0x01 0x24 0x01 0x26>;
			clock-names = "baudclk\0apb_pclk";
			reg = <0xe0023000 0x1000>;
			interrupts = <0x00 0x2c 0x04>;
			reg-shift = <0x02>;
			reg-io-width = <0x04>;
			u-boot,dm-pre-reloc;
			status = "disabled";
			linux,phandle = <0x19>;
			phandle = <0x19>;
		};

		ethernet@e0047000 {
			compatible = "fmsh,fmql-gmac\0snps,dwmac-3.70a\0snps,dwmac";
			reg = <0xe0047000 0x2000>;
			reg-names = "stmmaceth";
			interrupts = <0x00 0x13 0x00>;
			interrupt-names = "macirq";
			clocks = <0x01 0x17 0x01 0x15 0x01 0x0d 0x01 0x13>;
			clock-names = "stmmaceth\0pclk\0fmql-gmac-tx\0fmql-gmac-rx";
			phy-mode = "rgmii-id";
			fmsh,gmac-number = <0x00>;
			snps,multicast-filter-bins = <0x100>;
			snps,perfect-filter-entries = <0x80>;
			status = "okay";
			phy-handle = <0x0b>;
			linux,phandle = <0x1a>;
			phandle = <0x1a>;

			mdio@0 {
				compatible = "snps,dwmac-mdio";
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				eth-phy@0 {
					reg = <0x00>;
					phy-tx-delay = <0x0f>;
					phy-rx-delay = <0x00>;
					linux,phandle = <0x0b>;
					phandle = <0x0b>;
				};
			};
		};

		ethernet@e0049000 {
			compatible = "fmsh,fmql-gmac\0snps,dwmac-3.70a\0snps,dwmac";
			reg = <0xe0049000 0x2000>;
			reg-names = "stmmaceth";
			interrupts = <0x00 0x28 0x00>;
			interrupt-names = "macirq";
			clocks = <0x01 0x18 0x01 0x16 0x01 0x0e 0x01 0x14>;
			clock-names = "stmmaceth\0pclk\0fmql-gmac-tx\0fmql-gmac-rx";
			phy-mode = "rgmii";
			fmsh,gmac-number = <0x01>;
			snps,multicast-filter-bins = <0x100>;
			snps,perfect-filter-entries = <0x80>;
			status = "disabled";
			linux,phandle = <0x1b>;
			phandle = <0x1b>;
		};

		gpio@e0003000 {
			compatible = "snps,dw-apb-gpio";
			reg = <0xe0003000 0x100>;
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			clocks = <0x01 0x2d>;
			status = "okay";
			linux,phandle = <0x1c>;
			phandle = <0x1c>;

			gpio-controller@0 {
				compatible = "snps,dw-apb-gpio-port";
				bank-name = "porta";
				gpio-controller;
				#gpio-cells = <0x02>;
				snps,nr-gpios = <0x20>;
				reg = <0x00>;
				interrupt-controller;
				#interrupt-cells = <0x02>;
				interrupts = <0x00 0x11 0x04>;
				linux,phandle = <0x0d>;
				phandle = <0x0d>;
			};
		};

		gpio@e0003100 {
			compatible = "snps,dw-apb-gpio";
			reg = <0xe0003100 0x100>;
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			clocks = <0x01 0x2d>;
			status = "okay";
			linux,phandle = <0x1d>;
			phandle = <0x1d>;

			gpio-controller@0 {
				compatible = "snps,dw-apb-gpio-port";
				bank-name = "portb";
				gpio-controller;
				#gpio-cells = <0x02>;
				snps,nr-gpios = <0x16>;
				reg = <0x00>;
				interrupt-controller;
				#interrupt-cells = <0x02>;
				interrupts = <0x00 0x24 0x04>;
				linux,phandle = <0x1e>;
				phandle = <0x1e>;
			};
		};

		gpio@e0003200 {
			compatible = "snps,dw-apb-gpio";
			reg = <0xe0003200 0x100>;
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			clocks = <0x01 0x2d>;
			status = "okay";
			linux,phandle = <0x1f>;
			phandle = <0x1f>;

			gpio-controller@0 {
				compatible = "snps,dw-apb-gpio-port";
				bank-name = "portc";
				gpio-controller;
				#gpio-cells = <0x02>;
				snps,nr-gpios = <0x20>;
				reg = <0x00>;
				interrupt-controller;
				#interrupt-cells = <0x02>;
				interrupts = <0x00 0x25 0x04>;
				linux,phandle = <0x20>;
				phandle = <0x20>;
			};
		};

		gpio@e0003400 {
			compatible = "snps,dw-apb-gpio";
			reg = <0xe0003400 0x100>;
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			clocks = <0x01 0x2d>;
			status = "okay";
			linux,phandle = <0x21>;
			phandle = <0x21>;

			gpio-controller@0 {
				compatible = "snps,dw-apb-gpio-port";
				bank-name = "portd";
				gpio-controller;
				#gpio-cells = <0x02>;
				snps,nr-gpios = <0x20>;
				reg = <0x00>;
				interrupt-controller;
				#interrupt-cells = <0x02>;
				interrupts = <0x00 0x26 0x04>;
				linux,phandle = <0x22>;
				phandle = <0x22>;
			};
		};

		qspi@e0000000 {
			compatible = "fmsh,qspi-nor\0cadence,qspi";
			clocks = <0x01 0x1c 0x01 0x1d 0x01 0x1e>;
			clock-names = "clk_ref\0hclk\0pclk";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			reg = <0xe0000000 0x1000 0xe8000000 0x1000000>;
			interrupts = <0x00 0x0e 0x04>;
			cdns,fifo-depth = <0x100>;
			cdns,fifo-width = <0x04>;
			cdns,trigger-address = <0xe8000000>;
			status = "okay";
			linux,phandle = <0x23>;
			phandle = <0x23>;

			flash@0 {
				compatible = "spi-flash\0spansion,s25fl256s1\0jedec,spi-nor";
				reg = <0x00>;
				spi-max-frequency = <0x2faf080>;
				spi-tx-bus-width = <0x01>;
				spi-rx-bus-width = <0x04>;
				m25p,fast-read;
				page-size = <0x100>;
				block-size = <0x10>;
				cdns,read-delay = <0x02>;
				cdns,tshsl-ns = <0x00>;
				cdns,tsd2d-ns = <0x00>;
				cdns,tchsh-ns = <0x00>;
				cdns,tslch-ns = <0x00>;
				#address-cells = <0x01>;
				#size-cells = <0x01>;
				linux,phandle = <0x24>;
				phandle = <0x24>;

				<EMAIL> {
					label = "spi.boot";
					reg = <0x00 0x100000>;
				};

				<EMAIL> {
					label = "spi.env";
					reg = <0x100000 0x20000>;
				};

				<EMAIL>-data {
					label = "spi.user-data";
					reg = <0x120000 0x00>;
				};
			};
		};

		qspi@e0020000 {
			compatible = "fmsh,qspi-nor\0cadence,qspi";
			clocks = <0x01 0x1c 0x01 0x1d 0x01 0x1e>;
			clock-names = "clk_ref\0hclk\0pclk";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			reg = <0xe0020000 0x1000 0xe9000000 0x1000000>;
			interrupts = <0x00 0x0f 0x04>;
			cdns,fifo-depth = <0x100>;
			cdns,fifo-width = <0x04>;
			cdns,trigger-address = <0xe9000000>;
			status = "disabled";
			linux,phandle = <0x25>;
			phandle = <0x25>;
		};

		spi@e0001000 {
			compatible = "fmsh,dw-apb-ssi\0snps,dw-apb-ssi";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			reg = <0xe0001000 0x1000>;
			interrupts = <0x00 0x16 0x04>;
			num-cs = <0x03>;
			clocks = <0x01 0x27 0x01 0x29>;
			clock-names = "clk_ref\0pclk";
			reg-io-width = <0x04>;
			spi-max-frequency = <0xf4240>;
			status = "disabled";
			linux,phandle = <0x26>;
			phandle = <0x26>;
		};

		spi@e0021000 {
			compatible = "fmsh,dw-apb-ssi\0snps,dw-apb-ssi";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			reg = <0xe0021000 0x1000>;
			interrupts = <0x00 0x2b 0x04>;
			num-cs = <0x03>;
			clocks = <0x01 0x28 0x01 0x2a>;
			clock-names = "clk_ref\0pclk";
			reg-io-width = <0x04>;
			spi-max-frequency = <0xf4240>;
			status = "disabled";
			linux,phandle = <0x27>;
			phandle = <0x27>;
		};

		dma@e004b000 {
			compatible = "snps,dma-spear1340";
			reg = <0xe004b000 0x1000>;
			interrupts = <0x00 0x0d 0x04>;
			dma-channels = <0x08>;
			dma-requests = <0x10>;
			dma-masters = <0x01>;
			#dma-cells = <0x03>;
			chan_allocation_order = <0x01>;
			chan_priority = <0x01>;
			block_size = <0xfff>;
			data-width = <0x04>;
			clocks = <0x01 0x34>;
			clock-names = "hclk";
			status = "okay";
			linux,phandle = <0x28>;
			phandle = <0x28>;
		};

		usbphy@0 {
			compatible = "usb-nop-xceiv";
			#phy-cells = <0x00>;
			clocks = <0x01 0x32>;
			clock-names = "main_clk";
			status = "okay";
			linux,phandle = <0x0c>;
			phandle = <0x0c>;
		};

		usbphy@1 {
			compatible = "usb-nop-xceiv";
			#phy-cells = <0x00>;
			clocks = <0x01 0x33>;
			clock-names = "main_clk";
			status = "disabled";
			linux,phandle = <0x0e>;
			phandle = <0x0e>;
		};

		usb@e0045000 {
			compatible = "fmsh,psoc-dwc2-usb\0snps,dwc2";
			reg = <0xe0045000 0x1000>;
			interrupts = <0x00 0x12 0x04>;
			clocks = <0x01 0x30>;
			clock-names = "otg";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			phys = <0x0c>;
			phy-names = "usb2-phy";
			dr_mode = "host";
			phy-width = <0x08>;
			fmql,host_dma = <0x01>;
			status = "okay";
			disable-over-current;
			hnp-srp-disable;
			phy-reset-gpio = <0x0d 0x08 0x00>;
			phy-reset-duration = <0x64>;
			phy-reset-active-low;
			linux,phandle = <0x29>;
			phandle = <0x29>;
		};

		usb@e0046000 {
			compatible = "fmsh,psoc-dwc2-usb\0snps,dwc2";
			reg = <0xe0046000 0x1000>;
			interrupts = <0x00 0x27 0x04>;
			clocks = <0x01 0x31>;
			clock-names = "otg";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			phys = <0x0e>;
			phy-names = "usb2-phy";
			dr_mode = "otg";
			phy-width = <0x08>;
			fmql,host_dma = <0x00>;
			status = "disabled";
			linux,phandle = <0x2a>;
			phandle = <0x2a>;
		};

		dwmmc@e0043000 {
			compatible = "fmsh,psoc-dw-mshc";
			reg = <0xe0043000 0x1000>;
			interrupts = <0x00 0x14 0x04>;
			clocks = <0x01 0x21 0x01 0x1f>;
			clock-names = "biu\0ciu";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			data-addr = <0x100>;
			fifo-depth = <0x20>;
			bus-width = <0x04>;
			status = "okay";
			cap-sd-highspeed;
			cap-mmc-highspeed;
			max-frequency = <0x1312d00>;
			broken-cd;
			card-detect-delay = <0xc8>;
			linux,phandle = <0x2b>;
			phandle = <0x2b>;
		};

		dwmmc@e0044000 {
			compatible = "fmsh,psoc-dw-mshc";
			reg = <0xe0044000 0x1000>;
			interrupts = <0x00 0x29 0x04>;
			clocks = <0x01 0x22 0x01 0x20>;
			clock-names = "biu\0ciu";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			data-addr = <0x100>;
			fifo-depth = <0x20>;
			bus-width = <0x04>;
			status = "okay";
			cap-sd-highspeed;
			cap-mmc-highspeed;
			broken-cd;
			max-frequency = <0x2faf080>;
			linux,phandle = <0x2c>;
			phandle = <0x2c>;
		};

		nfc@e0042000 {
			compatible = "fmsh,psoc-nfc";
			reg = <0xe0042000 0x1000>;
			clocks = <0x01 0x1a 0x01 0x1b>;
			clock-names = "pclk\0nfc_ref";
			#address-cells = <0x01>;
			#size-cells = <0x01>;
			interrupts = <0x00 0x10 0x04>;
			nand-bus-width = <0x08>;
			nand-ecc-mode = "hw";
			nand-ecc-strength = <0x08>;
			nand-ecc-step-size = <0x200>;
			nand-use-mode = "dma";
			status = "disabled";
			linux,phandle = <0x2d>;
			phandle = <0x2d>;
		};

		i2c@e0002000 {
			compatible = "snps,designware-i2c";
			reg = <0xe0002000 0x1000>;
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			clocks = <0x01 0x2e>;
			interrupts = <0x00 0x15 0x04>;
			i2c-max-frequency = <0xf4240>;
			status = "disabled";
			linux,phandle = <0x2e>;
			phandle = <0x2e>;
		};

		i2c@e0022000 {
			compatible = "snps,designware-i2c";
			reg = <0xe0022000 0x1000>;
			clocks = <0x01 0x2f>;
			interrupts = <0x00 0x2a 0x04>;
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			i2c-max-frequency = <0xf4240>;
			status = "okay";
			clock-frequency = <0x186a0>;
			i2c-sda-hold-time-ns = <0x12c>;
			i2c-sda-falling-time-ns = <0x12c>;
			i2c-scl-falling-time-ns = <0x12c>;
			linux,phandle = <0x2f>;
			phandle = <0x2f>;

			rtc@68 {
				compatible = "dallas,ds1307";
				reg = <0x68>;
				linux,phandle = <0x30>;
				phandle = <0x30>;
			};
		};

		timer@e0007000 {
			compatible = "snps,dw-apb-timer";
			interrupts = <0x00 0x0a 0x04>;
			reg = <0xe0007000 0x1000>;
			clocks = <0x01 0x37 0x01 0x3d>;
			clock-names = "timer\0pclk";
			linux,phandle = <0x31>;
			phandle = <0x31>;
		};

		timer@e0024000 {
			compatible = "snps,dw-apb-timer";
			interrupts = <0x00 0x21 0x04>;
			reg = <0xe0024000 0x1000>;
			clocks = <0x01 0x3a 0x01 0x3e>;
			clock-names = "timer\0pclk";
			linux,phandle = <0x32>;
			phandle = <0x32>;
		};

		watchdog@e0025000 {
			compatible = "fmql,dw-wdt";
			reg = <0xe0025000 0x1000>;
			interrupts = <0x00 0x09 0x04>;
			clocks = <0x01 0x35 0x01 0x36>;
			clock-names = "wdt\0pclk";
			status = "okay";
			linux,phandle = <0x33>;
			phandle = <0x33>;
		};

		can@e0005000 {
			compatible = "fmql,sja1000";
			reg = <0xe0005000 0x1000>;
			clocks = <0x01 0x2b>;
			clock-names = "pclk";
			interrupts = <0x00 0x18 0x04>;
			reg-io-width = <0x04>;
			nxp,tx-output-mode = <0x01>;
			nxp,tx-output-config = <0x02>;
			nxp,no-comparator-bypass;
			status = "okay";
			linux,phandle = <0x34>;
			phandle = <0x34>;
		};

		can@e0006000 {
			compatible = "fmql,sja1000";
			reg = <0xe0006000 0x1000>;
			clocks = <0x01 0x2c>;
			clock-names = "pclk";
			interrupts = <0x00 0x2d 0x04>;
			reg-io-width = <0x04>;
			nxp,tx-output-mode = <0x01>;
			nxp,tx-output-config = <0x02>;
			nxp,no-comparator-bypass;
			status = "okay";
			linux,phandle = <0x35>;
			phandle = <0x35>;
		};
	};

	reserved-memory {
		#address-cells = <0x01>;
		#size-cells = <0x01>;
		ranges;

		microblaze@18000000 {
			no-map;
			reg = <0x18000000 0x1000000>;
			linux,phandle = <0x36>;
			phandle = <0x36>;
		};

		rproc@19000000 {
			no-map;
			reg = <0x19000000 0x1000000>;
			linux,phandle = <0x37>;
			phandle = <0x37>;
		};

		reserved@1A000000 {
			no-map;
			reg = <0x1a000000 0x5000000>;
			linux,phandle = <0x38>;
			phandle = <0x38>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led0 {
			label = "heartbeat";
			gpios = <0x0d 0x0e 0x00>;
			linux,default-trigger = "heartbeat";
		};

		led1 {
			label = "disc";
			gpios = <0x0d 0x0f 0x00>;
			linux,default-trigger = "mmc1";
		};

		led2 {
			label = "user-led0";
			gpios = <0x0d 0x07 0x00>;
			default-state = "on";
		};
	};

	gpio-keys {
		status = "okay";
		compatible = "gpio-keys";
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		autorepeat;

		user_key@0 {
			label = "user-key0";
			gpios = <0x0d 0x09 0x01>;
			linux,code = <0x94>;
			debounce-interval = <0x00>;
		};
	};

	__symbols__ {
		cpu0 = "/cpus/cpu@0";
		cpu1 = "/cpus/cpu@1";
		cpu2 = "/cpus/cpu@2";
		cpu3 = "/cpus/cpu@3";
		cpu0_opp_table = "/opp-table";
		fpga_full = "/fpga-full";
		ocmc = "/ocmc@0";
		amba = "/amba@0";
		intc = "/amba@0/interrupt-controller@f8901000";
		slcr = "/amba@0/slcr@e0026000";
		clkc = "/amba@0/slcr@e0026000/clkc@100";
		ddr_umc = "/amba@0/ddr_umc@e0029000";
		devcfg = "/amba@0/devcfg@e0040000";
		timer = "/amba@0/timer";
		smc = "/amba@0/smc@0";
		sram0 = "/amba@0/smc@0/sram@e2000000";
		sram1 = "/amba@0/smc@0/sram@e4000000";
		nor0 = "/amba@0/smc@0/flash@e2000000";
		nor1 = "/amba@0/smc@0/flash@e4000000";
		uart0 = "/amba@0/serial@e0004000";
		uart1 = "/amba@0/serial@e0023000";
		gmac0 = "/amba@0/ethernet@e0047000";
		phy0 = "/amba@0/ethernet@e0047000/mdio@0/eth-phy@0";
		gmac1 = "/amba@0/ethernet@e0049000";
		gpio0 = "/amba@0/gpio@e0003000";
		porta = "/amba@0/gpio@e0003000/gpio-controller@0";
		gpio1 = "/amba@0/gpio@e0003100";
		portb = "/amba@0/gpio@e0003100/gpio-controller@0";
		gpio2 = "/amba@0/gpio@e0003200";
		portc = "/amba@0/gpio@e0003200/gpio-controller@0";
		gpio3 = "/amba@0/gpio@e0003400";
		portd = "/amba@0/gpio@e0003400/gpio-controller@0";
		qspi0 = "/amba@0/qspi@e0000000";
		flash = "/amba@0/qspi@e0000000/flash@0";
		qspi1 = "/amba@0/qspi@e0020000";
		spi0 = "/amba@0/spi@e0001000";
		spi1 = "/amba@0/spi@e0021000";
		dmahost = "/amba@0/dma@e004b000";
		usbphy0 = "/amba@0/usbphy@0";
		usbphy1 = "/amba@0/usbphy@1";
		usb0 = "/amba@0/usb@e0045000";
		usb1 = "/amba@0/usb@e0046000";
		mmc0 = "/amba@0/dwmmc@e0043000";
		mmc1 = "/amba@0/dwmmc@e0044000";
		nand = "/amba@0/nfc@e0042000";
		i2c0 = "/amba@0/i2c@e0002000";
		i2c1 = "/amba@0/i2c@e0022000";
		ds1307 = "/amba@0/i2c@e0022000/rtc@68";
		timer1 = "/amba@0/timer@e0007000";
		timer2 = "/amba@0/timer@e0024000";
		watchdog = "/amba@0/watchdog@e0025000";
		can0 = "/amba@0/can@e0005000";
		can1 = "/amba@0/can@e0006000";
		microblaze_reserved = "/reserved-memory/microblaze@18000000";
		rproc_0_reserved = "/reserved-memory/rproc@19000000";
		reserved = "/reserved-memory/reserved@1A000000";
	};
};
