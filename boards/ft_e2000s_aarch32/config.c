/************************************************************************
 *              科东(广州)软件科技有限公司 版权所有
 *   Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 ***********************************************************************/

/*
 * 修改历史：
 * 2023-06-24    彭元志，科东(广州)软件科技有限公司
 *               创建该文件。
 */

/*
 * @file: configs.c
 * @brief:
 *    <li> 系统配置 </li>
 */

/************************头 文 件******************************/
#include "fio_mux.h"
#include <fs/fs.h>
#include <stddef.h>
#include <stdint.h>
#include <ttos_init.h>

#undef KLOG_TAG
#define KLOG_TAG "board"
#include <klog.h>
/************************宏 定 义******************************/

/************************类型定义******************************/

/************************外部声明******************************/

/************************前向声明******************************/

/************************模块变量******************************/

extern int dtb_data, dtb_end;
/************************全局变量******************************/

/************************函数实现******************************/

void *board_get_dtb(size_t *size)
{
    if (size)
    {
        *size = (uintptr_t)&dtb_end - (uintptr_t)&dtb_data;
    }
    return (void *)&dtb_data;
}

static int board_rootfs_init(void)
{
    int ret = 0;
#ifdef CONFIG_MMCSD
    /* 挂载sd卡 或者emmc 设备分区，mmcblk0p0设备为系统镜像分区， mmcblk0p1是ext4文件系统分区 */
    ret = vfs_mount("/dev/mmcblk0p1", "/", "ext4", 0, NULL);
    if (ret < 0)
    {
        KLOG_E("ERROR: Failed to mount lwext4 at %s: %d", "/", ret);
        return ret;
    }
#else
    ret = vfs_mount("/dev/ramdisk", "/", "ext4", 0, "autoformat");
    if (ret < 0)
    {
        KLOG_E("ERROR: Failed to mount lwext4 at %s: %d", "/", ret);
        return ret;
    }
#endif /* CONFIG_FS_LWEXT4 */
    /* 挂载tmpfs内存文件系统 */
    ret = vfs_mount(NULL, "/run", "tmpfs", 0, "autoformat");
    if (ret < 0)
    {
        KLOG_E("ERROR: Failed to mount tmpfs /run: %d", ret);
        return ret;
    }

    return 0;
}
INIT_EXPORT_ROOT_FS(board_rootfs_init, "rootfs init");

static int board_init(void)
{
    FIOMuxInit();
}

INIT_EXPORT_ARCH(board_init, "board init");
