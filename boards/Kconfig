menu "BSP配置"
    choice
        prompt "目标板"
        default QEMU_AARCH32
        config QEMU_AARCH64
            bool "qemu aarch64"
        config S5000C
            bool "S5000C"
        config QEMU_AARCH32
            bool "qemu aarch32"
        config FT_E2000S_AARCH32
            bool "ft e2000s aarch32"
        config RK3568_AARCH64
            bool "rk3568 aarch64"
        config QEMU_X86_64
            bool "qemu_x86_64"
        config SEMIDRV_D9_AARCH64
            bool "SemiDrv D9340 aarch64"
        config FMSH
            bool "fmsh"
    endchoice
    if QEMU_AARCH32
        source "boards/qemu_aarch32/Kconfig"
    endif
    if FT_E2000S_AARCH32
        source "boards/ft_e2000s_aarch32/Kconfig"
    endif
    if QEMU_AARCH64
        source "boards/qemu_aarch64/Kconfig"
    endif
    if S5000C
        source "boards/S5000C/Kconfig"
    endif
    if RK3568_AARCH64
        source "boards/rk3568/Kconfig"
    endif
    if QEMU_X86_64
        source "boards/qemu_x86_64/Kconfig"
    endif
    if SEMIDRV_D9_AARCH64
        source "boards/semidrv_d9/Kconfig"
    endif
    if FMSH
        source "boards/fmsh/Kconfig"
    endif
    config BOARD_PATH
        string
        default "qemu_aarch64" if QEMU_AARCH64
        default "qemu_aarch32" if QEMU_AARCH32
        default "ft_e2000s_aarch32" if FT_E2000S_AARCH32
        default "S5000C" if S5000C
        default "rk3568" if RK3568_AARCH64
        default "semidrv_d9" if SEMIDRV_D9_AARCH64
        default "qemu_x86_64" if QEMU_X86_64
        default "fmsh" if FMSH
endmenu
