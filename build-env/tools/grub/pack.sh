#!/bin/bash

# Script usage function
usage() {
    echo "Usage: $0 <rtos.bin path> <output ISO filename> <rootfs.bin path>"
    echo "Example: $0 ./rtos.bin intewell.iso ./rootfs/x86_64/rootfs.bin"
    exit 1
}

# Check number of arguments
if [ $# -ne 3 ]; then
    echo "Error: Three arguments required"
    usage
fi

RTOS_BIN="$1"
OUTPUT_ISO="$2"
ROOTFS_BIN="$3"

# Check if rtos.bin file exists
if [ ! -f "$RTOS_BIN" ]; then
    echo "Error: RTOS binary file '$RTOS_BIN' does not exist"
    exit 1
fi

# Check if rootfs.bin file exists
if [ ! -f "$ROOTFS_BIN" ]; then
    echo "Error: Rootfs binary file '$ROOTFS_BIN' does not exist"
    exit 1
fi

echo "Using RTOS binary file: $RTOS_BIN"
echo "Using Rootfs binary file: $ROOTFS_BIN"
echo "Output ISO file: $OUTPUT_ISO"

# Clean and create temporary directory
rm -fr /tmp/iso
mkdir -p /tmp/iso/boot/grub

# Copy RTOS binary file
cp "$RTOS_BIN" /tmp/iso/boot/kernel.bin

# Copy Rootfs binary file
cp "$ROOTFS_BIN" /tmp/iso/boot/rootfs.bin

# Create GRUB configuration file
cat <<'EOF' | tee /tmp/iso/boot/grub/grub.cfg > /dev/null
set timeout=5
set default=0

menuentry "Intewell RTOS" {
    multiboot /boot/kernel.bin
    module /boot/rootfs.bin rootfs
    boot
}
EOF

# Create ISO file
grub-mkrescue -o "$OUTPUT_ISO" /tmp/iso

# Check if ISO creation was successful
if [ $? -eq 0 ]; then
    echo "ISO file created successfully: $OUTPUT_ISO"
else
    echo "Error: ISO file creation failed"
    exit 1
fi

# Clean up temporary files
rm -fr /tmp/iso