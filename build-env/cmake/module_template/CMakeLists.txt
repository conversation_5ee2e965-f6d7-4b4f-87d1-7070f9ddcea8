cmake_minimum_required(VERSION 3.13)
project(example_module C)

# Set SDK directory path - modify this to point to your SDK location
set(SDK_DIR "/path/to/sdk")

# Include the SDK's module build system
include(${SDK_DIR}/cmake/module.cmake)

# The module.cmake will automatically:
# 1. Load kernel configuration from loadDOTCONFIG.cmake
# 2. Set up correct compiler and flags
# 3. Include all necessary header paths
# 4. Add kernel configuration header

# Add your kernel module
add_kernel_module(example_module
    src/example_module.c
)
