cmake_minimum_required(VERSION 3.13)

# Include required cmake modules
include(${SDK_DIR}/cmake/tools.cmake)

# Set DOTCONFIG path to use the SDK's .config file
set(DOTCONFIG ${SDK_DIR}/.config)

# Load kernel configuration
include(${SDK_DIR}/cmake/loadDOTCONFIG.cmake)

# Set compiler and flags to match kernel compilation
set(CMAKE_C_COMPILER "@MODULE_CMAKE_C_COMPILER@")
set(CMAKE_ASM_COMPILER "@MODULE_CMAKE_C_COMPILER@")
set(CMAKE_LD "@MODULE_CMAKE_LD@")

# Set compilation flags
set(CMAKE_C_FLAGS "@CMAKE_C_FLAGS@ -fno-builtin -nostdinc -fno-pic -fno-pie")
set(CMAKE_ASM_FLAGS "@CMAKE_ASM_FLAGS@")

# Include paths
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${SDK_DIR}/include/kernel
    ${SDK_DIR}/include/libk
    ${SDK_DIR}/include/lwip
    ${SDK_DIR}/include/linux-comp
)

# Add kernel config header to compilation
add_definitions(-include ${SDK_DIR}/include/kconfig/include/generated/autoconfig.h)

if(CONFIG_ARCH_ARMv7 OR CONFIG_ARCH_AARCH32)
include_directories(
    ${SDK_DIR}/include/kernel/arch/arm
    ${SDK_DIR}/include/kernel/arch/arm_common
)
endif(CONFIG_ARCH_ARMv7 OR CONFIG_ARCH_AARCH32)

if(CONFIG_ARCH_AARCH64)
include_directories(
        ${SDK_DIR}/include/kernel/arch/aarch64
        ${SDK_DIR}/include/kernel/arch/arm_common
)
endif(CONFIG_ARCH_AARCH64)

# Function to create kernel module target
function(add_kernel_module MODULE_NAME)
    add_library(${MODULE_NAME}_obj OBJECT ${ARGN})

    add_custom_command(
        OUTPUT ${MODULE_NAME}.ko
        COMMAND ${CMAKE_LD} -r -T ${SDK_DIR}/linkscript/module.lds $<TARGET_OBJECTS:${MODULE_NAME}_obj> -o ${MODULE_NAME}.ko
        COMMAND_EXPAND_LISTS
        DEPENDS ${MODULE_NAME}_obj
        COMMENT "Linking kernel module"
    )
    add_custom_target(${MODULE_NAME} ALL DEPENDS ${MODULE_NAME}.ko)
    target_compile_definitions(${MODULE_NAME}_obj PRIVATE
        MODULE
        __KERNEL__
        KBUILD_MODNAME=\"${MODULE_NAME}\"
    )
endfunction()

# Example usage:
# add_kernel_module(my_module
#     src/my_module.c
#     src/other_source.c
# )
