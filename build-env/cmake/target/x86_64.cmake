set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -march=x86-64 -m64 -mcmodel=large  -mno-red-zone")

if(CONFIG_HARD_FLOAT)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -mhard-float" CACHE STRING "target" FORCE)
else()
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -msoft-float" CACHE STRING "target" FORCE)
endif()


set(CC_TARGET x86_64-none-unknown-elf)

if(CONFIG_TOOLCHAIN_CLANG)
    set(CMAKE_C_COMPILER clang CACHE STRING "target" FORCE)
    set(CMAKE_ASM_COMPILER clang CACHE STRING "target" FORCE)
    set(CMAKE_CXX_COMPILER clang++ CACHE STRING "target" FORCE)
    SET(CMAKE_OBJCOPY llvm-objcopy CACHE STRING "target" FORCE)
    SET(CMAKE_AR llvm-ar CACHE STRING "target" FORCE)
    SET(CMAKE_AS llvm-as CACHE STRING "target" FORCE)
    SET(CMAKE_LD  ld.lld CACHE STRING "target" FORCE)
    SET(CMAKE_NM llvm-nm CACHE STRING "target" FORCE)
    SET(CMAKE_STRIP  llvm-strip CACHE STRING "target" FORCE)
    SET(CMAKE_RANLIB llvm-ranlib CACHE STRING "target" FORCE)
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -target ${CC_TARGET}")

    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fuse-ld=lld")
else()
    set(TOOLCHAIN_PREFIX x86_64-linux-musl-)
    set(CMAKE_C_COMPILER ${TOOLCHAIN_PREFIX}gcc CACHE STRING "target" FORCE)
    set(CMAKE_ASM_COMPILER ${TOOLCHAIN_PREFIX}gcc CACHE STRING "target" FORCE)
    set(CMAKE_OBJDUMP  ${TOOLCHAIN_PREFIX}objdump CACHE STRING "target" FORCE)
    SET(CMAKE_CXX_COMPILER ${TOOLCHAIN_PREFIX}g++ CACHE STRING "target" FORCE)
    SET(CMAKE_OBJCOPY ${TOOLCHAIN_PREFIX}objcopy CACHE STRING "target" FORCE)
    SET(CMAKE_AR ${TOOLCHAIN_PREFIX}ar CACHE STRING "target" FORCE)
    SET(CMAKE_AS ${TOOLCHAIN_PREFIX}as CACHE STRING "target" FORCE)
    SET(CMAKE_LD  ${TOOLCHAIN_PREFIX}ld CACHE STRING "target" FORCE)
    SET(CMAKE_NM ${TOOLCHAIN_PREFIX}nm CACHE STRING "target" FORCE)
    SET(CMAKE_RANLIB ${TOOLCHAIN_PREFIX}ranlib CACHE STRING "target" FORCE)
    SET(CMAKE_STRIP  ${TOOLCHAIN_PREFIX}strip CACHE STRING "target" FORCE)

endif()
