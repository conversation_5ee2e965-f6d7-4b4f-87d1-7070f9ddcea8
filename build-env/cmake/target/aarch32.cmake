add_definitions(-DCPU_BIT=32)

add_compile_options(-march=armv8-a)
if(CONFIG_HARD_FLOAT)
    add_compile_options(-mfloat-abi=hard)
    add_compile_options(-mfpu=neon-fp-armv8)
else()
    add_compile_options(-mfloat-abi=soft)
    add_compile_options(-mfpu=none)
endif()

add_compile_options(-mno-unaligned-access)

set(CC_TARGET arm-none-unknown-eabi)

if(CONFIG_TOOLCHAIN_CLANG)
    set(CMAKE_C_COMPILER clang CACHE STRING "target" FORCE)
    set(CMAKE_ASM_COMPILER clang CACHE STRING "target" FORCE)
    set(CMAKE_CXX_COMPILER clang++ CACHE STRING "target" FORCE)
    SET(CMAKE_OBJCOPY llvm-objcopy CACHE STRING "target" FORCE)
    SET(CMAKE_AR llvm-ar CACHE STRING "target" FORCE)
    SET(CMAKE_AS llvm-as CACHE STRING "target" FORCE)
    SET(CMAKE_LD  ld.lld CACHE STRING "target" FORCE)
    SET(CMAKE_NM llvm-nm CACHE STRING "target" FORCE)
    SET(CMAKE_STRIP  llvm-strip CACHE STRING "target" FORCE)
    SET(CMAKE_RANLIB llvm-ranlib CACHE STRING "target" FORCE)
    add_compile_options(-target ${CC_TARGET})
    add_link_options(-target ${CC_TARGET})
    add_compile_options(-Wno-comment)
    add_link_options(-fuse-ld=lld)
else()
    set(TOOLCHAIN_PREFIX arm-linux-musleabihf-)
    set(CMAKE_C_COMPILER ${TOOLCHAIN_PREFIX}gcc CACHE STRING "target" FORCE)
    set(CMAKE_ASM_COMPILER ${TOOLCHAIN_PREFIX}gcc CACHE STRING "target" FORCE)
    set(CMAKE_OBJDUMP  ${TOOLCHAIN_PREFIX}objdump CACHE STRING "target" FORCE)
    SET(CMAKE_CXX_COMPILER ${TOOLCHAIN_PREFIX}g++ CACHE STRING "target" FORCE)
    SET(CMAKE_OBJCOPY ${TOOLCHAIN_PREFIX}objcopy CACHE STRING "target" FORCE)
    SET(CMAKE_AR ${TOOLCHAIN_PREFIX}ar CACHE STRING "target" FORCE)
    SET(CMAKE_AS ${TOOLCHAIN_PREFIX}as CACHE STRING "target" FORCE)
    SET(CMAKE_LD  ${TOOLCHAIN_PREFIX}ld CACHE STRING "target" FORCE)
    SET(CMAKE_NM ${TOOLCHAIN_PREFIX}nm CACHE STRING "target" FORCE)
    SET(CMAKE_RANLIB ${TOOLCHAIN_PREFIX}ranlib CACHE STRING "target" FORCE)
    SET(CMAKE_STRIP  ${TOOLCHAIN_PREFIX}strip CACHE STRING "target" FORCE)
    add_link_options(-no-pie)

endif()

# 添加静态链接选项
add_link_options(-static -Wl,-z,notext)

add_compile_options(-fno-pic -fno-pie)
