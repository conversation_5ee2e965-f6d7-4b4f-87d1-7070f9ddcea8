add_definitions(-DCPU_BIT=64)

if(CONFIG_TARGET_CPU_CORTEX_A55)
    add_compile_options(-march=armv8.2-a)
elseif(CONFIG_TARGET_CPU_CORTEX_A53)
    add_compile_options(-march=armv8-a)
endif()

if(NOT CONFIG_HARD_FLOAT)
    add_compile_options(-mgeneral-regs-only)
endif(NOT CONFIG_HARD_FLOAT)

set(CC_TARGET aarch64-none-unknown-elf)

if(CONFIG_TOOLCHAIN_CLANG)
    set(CMAKE_C_COMPILER clang CACHE STRING "target" FORCE)
    set(CMAKE_ASM_COMPILER clang CACHE STRING "target" FORCE)
    set(CMAKE_CXX_COMPILER clang++ CACHE STRING "target" FORCE)
    SET(CMAKE_OBJCOPY llvm-objcopy CACHE STRING "target" FORCE)
    SET(CMAKE_AR llvm-ar CACHE STRING "target" FORCE)
    SET(CMAKE_AS llvm-as CACHE STRING "target" FORCE)
    SET(CMAKE_NM llvm-nm CACHE STRING "target" FORCE)
    SET(CMAKE_STRIP  llvm-strip CACHE STRING "target" FORCE)
    SET(CMAKE_RANLIB llvm-ranlib CACHE STRING "target" FORCE)
    SET(CMAKE_LD  clang CACHE STRING "target" FORCE)
    # SET(CMAKE_LINKER  ${TOOLCHAIN_PREFIX}ld CACHE STRING "target" FORCE)
    add_compile_options(-target ${CC_TARGET})
    add_link_options(-target ${CC_TARGET})
    add_compile_options(-Wno-comment)
    add_link_options(-fuse-ld=lld)
else()
    set(TOOLCHAIN_PREFIX aarch64-linux-musl-)
    set(CMAKE_C_COMPILER ${TOOLCHAIN_PREFIX}gcc CACHE STRING "target" FORCE)
    set(CMAKE_ASM_COMPILER ${TOOLCHAIN_PREFIX}gcc CACHE STRING "target" FORCE)
    set(CMAKE_OBJDUMP  ${TOOLCHAIN_PREFIX}objdump CACHE STRING "target" FORCE)
    SET(CMAKE_CXX_COMPILER ${TOOLCHAIN_PREFIX}g++ CACHE STRING "target" FORCE)
    SET(CMAKE_OBJCOPY ${TOOLCHAIN_PREFIX}objcopy CACHE STRING "target" FORCE)
    SET(CMAKE_AR ${TOOLCHAIN_PREFIX}ar CACHE STRING "target" FORCE)
    SET(CMAKE_AS ${TOOLCHAIN_PREFIX}as CACHE STRING "target" FORCE)
    SET(CMAKE_LD  ${TOOLCHAIN_PREFIX}ld CACHE STRING "target" FORCE)
    SET(CMAKE_NM ${TOOLCHAIN_PREFIX}nm CACHE STRING "target" FORCE)
    SET(CMAKE_RANLIB ${TOOLCHAIN_PREFIX}ranlib CACHE STRING "target" FORCE)
    SET(CMAKE_STRIP  ${TOOLCHAIN_PREFIX}strip CACHE STRING "target" FORCE)

    add_link_options(-no-pie)
endif()

add_compile_options(-fno-pic -fno-pie)