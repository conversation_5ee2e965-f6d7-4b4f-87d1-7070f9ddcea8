set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_C_COMPILER_WORKS TRUE)
set(CMAKE_CXX_COMPILER_WORKS TRUE)
set(CMAKE_CROSSCOMPILING TRUE)

set(TARGET_TOOLCHAIN_DIR ${BUILD_ENV_PATH}/cmake/target)

if(CONFIG_TARGET_CPU_CORTEX_R5)
    include(${TARGET_TOOLCHAIN_DIR}/cortex-r5.cmake)
elseif(CONFIG_ARCH_AARCH64)
    include(${TARGET_TOOLCHAIN_DIR}/aarch64.cmake)
elseif(CONFIG_ARCH_ARMv7 OR CONFIG_ARCH_AARCH32)
    include(${TARGET_TOOLCHAIN_DIR}/aarch32.cmake)
elseif(CONFIG_ARCH_X86_64)
    include(${TARGET_TOOLCHAIN_DIR}/x86_64.cmake)
else()
    include(${TARGET_TOOLCHAIN_DIR}/default.cmake)
endif()

add_compile_options(-include ${CONFIG_C_HEADER})
add_compile_options(-O${CONFIG_BUILD_OPTIMIZE})
add_compile_options(-fno-omit-frame-pointer)
add_compile_options(-fno-optimize-sibling-calls)
add_compile_options(-ffunction-sections)
add_compile_options(-fdata-sections)
add_compile_options(-fno-strict-aliasing)
add_compile_options(-fno-common)
add_compile_options(-fno-builtin)
add_compile_options(-nostdinc)
add_compile_options(-Werror)
add_compile_options(-Wno-initializer-overrides)

if(CONFIG_BUILD_DEBUG_INFO)
add_compile_options(-g)
endif(CONFIG_BUILD_DEBUG_INFO)

if(CONFIG_MM_KASAN_ALL)
    add_compile_options(-fsanitize=kernel-address)
endif(CONFIG_MM_KASAN_ALL)

if(CONFIG_MM_KASAN_GLOBAL)
    add_compile_options("SHELL:--param asan-globals=1")
endif(CONFIG_MM_KASAN_GLOBAL)

if(CONFIG_MM_KASAN_DISABLE_READS_CHECK)
    add_compile_options("SHELL:--param asan-instrument-reads=0")
endif(CONFIG_MM_KASAN_DISABLE_READS_CHECK)

if(CONFIG_MM_KASAN_DISABLE_WRITES_CHECK)
    add_compile_options("SHELL:--param asan-instrument-writes=0")
endif(CONFIG_MM_KASAN_DISABLE_WRITES_CHECK)

if(CONFIG_MM_UBSAN_ALL)
    add_compile_options(${CONFIG_MM_UBSAN_OPTION})
endif(CONFIG_MM_UBSAN_ALL)
if(CONFIG_MM_UBSAN_TRAP_ON_ERROR)
    add_compile_options(-fsanitize-undefined-trap-on-error)
endif(CONFIG_MM_UBSAN_TRAP_ON_ERROR)

add_link_options(-nostdlib)


if(CONFIG_HARD_FLOAT)
add_definitions(-D_HARD_FLOAT_)
endif()

add_definitions(-D_LITTLE_ENDIAN_)

